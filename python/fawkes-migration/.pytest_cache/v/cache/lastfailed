{"model/test_globals.py::TestCache1::test_source_cluster": true, "model/service_components/test_workers_crons.py::test_rollback": true, "model/service_components/test_workers_crons.py::test_rollback_dry_run": true, "model/service_components/test_workers_crons.py::test_rollback_workload_name": true, "model/service_components/test_workers_crons.py::test_rollback_idempotent": true, "fawkes_traffic_switcher/test_lib.py::test_infer_strategy[ferry_none]": true}