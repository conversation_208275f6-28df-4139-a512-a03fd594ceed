{"model/test_globals.py::TestCache1::test_source_cluster": true, "model/service_components/test_workers_crons.py::test_rollback": true, "model/service_components/test_workers_crons.py::test_rollback_dry_run": true, "model/service_components/test_workers_crons.py::test_rollback_workload_name": true, "model/service_components/test_workers_crons.py::test_rollback_idempotent": true, "fawkes_traffic_switcher/test_lib.py::test_infer_strategy_private": true, "fawkes_traffic_switcher/test_lib.py::test_infer_strategy_public": true, "fawkes_traffic_switcher/test_lib.py::test_infer_strategy_ferry_boat": true, "fawkes_traffic_switcher/test_lib.py::test_infer_strategy_ferry_none": true, "fawkes_traffic_switcher/test_lib.py::test_infer_trident_r53_set_id_none": true, "fawkes_traffic_switcher/test_lib.py::test_infer_trident_r53_set_id": true, "fawkes_traffic_switcher/test_lib.py::test_infer_trident_r53_set_id_multiple": true, "fawkes_traffic_switcher/test_lib.py::test_infer_trident_cf_origin_none": true, "fawkes_traffic_switcher/test_lib.py::test_infer_trident_cf_origin_proxied": true, "fawkes_traffic_switcher/test_lib.py::test_infer_trident_cf_origin_not_proxied": true, "fawkes_traffic_switcher/test_lib.py::test_infer_fawkes_cf_origin_none": true, "fawkes_traffic_switcher/test_lib.py::test_infer_fawkes_cf_origin": true}