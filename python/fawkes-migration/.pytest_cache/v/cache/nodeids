["fawkes_traffic_switcher/test_lib.py::test_infer_fawkes_cf_origin", "fawkes_traffic_switcher/test_lib.py::test_infer_fawkes_cf_origin_none", "fawkes_traffic_switcher/test_lib.py::test_infer_strategy[ferry_boat]", "fawkes_traffic_switcher/test_lib.py::test_infer_strategy[ferry_none]", "fawkes_traffic_switcher/test_lib.py::test_infer_strategy[private]", "fawkes_traffic_switcher/test_lib.py::test_infer_strategy[public]", "fawkes_traffic_switcher/test_lib.py::test_infer_strategy_ferry_boat", "fawkes_traffic_switcher/test_lib.py::test_infer_strategy_ferry_none", "fawkes_traffic_switcher/test_lib.py::test_infer_strategy_private", "fawkes_traffic_switcher/test_lib.py::test_infer_strategy_public", "fawkes_traffic_switcher/test_lib.py::test_infer_trident_cf_origin_none", "fawkes_traffic_switcher/test_lib.py::test_infer_trident_cf_origin_not_proxied", "fawkes_traffic_switcher/test_lib.py::test_infer_trident_cf_origin_proxied", "fawkes_traffic_switcher/test_lib.py::test_infer_trident_r53_set_id", "fawkes_traffic_switcher/test_lib.py::test_infer_trident_r53_set_id_multiple", "fawkes_traffic_switcher/test_lib.py::test_infer_trident_r53_set_id_none", "migrate/service_role/test_lib.py::test_check_modes_consistency[modes0-False]", "migrate/service_role/test_lib.py::test_check_modes_consistency[modes1-True]", "migrate/service_role/test_lib.py::test_check_modes_consistency[modes2-True]", "migrate/service_role/test_lib.py::test_check_modes_consistency[modes3-False]", "migrate/service_role/test_lib.py::test_check_modes_consistency[modes4-False]", "migrate/service_role/test_lib.py::test_contains_sa[cashpay-live-True]", "migrate/service_role/test_lib.py::test_contains_sa[payout-dev-False]", "migrate/service_role/test_lib.py::test_extract_chamber_modes", "migrate/service_role/test_lib.py::test_extract_chamber_modes_not_found", "migrate/service_role/test_lib.py::test_extract_chamber_services", "migrate/service_role/test_lib.py::test_extract_chamber_services_not_found", "migrate/service_role/test_lib.py::test_extract_consumed_sqs", "migrate/service_role/test_lib.py::test_extract_consumed_sqs_not_found", "migrate/service_role/test_lib.py::test_extract_kms_keys", "migrate/service_role/test_lib.py::test_extract_kms_keys_not_found", "migrate/service_role/test_lib.py::test_extract_published_sns", "migrate/service_role/test_lib.py::test_extract_published_sns_not_found", "migrate/service_role/test_lib.py::test_extract_published_sqs", "migrate/service_role/test_lib.py::test_extract_published_sqs_not_found", "migrate/service_role/test_lib.py::test_find_chamber_policy", "migrate/service_role/test_lib.py::test_find_chamber_policy_not_found", "migrate/service_role/test_lib.py::test_find_irsa_custom_policies", "migrate/service_role/test_lib.py::test_find_irsa_custom_policies_not_found", "migrate/service_role/test_lib.py::test_find_irsa_sns_sqs_policy", "migrate/service_role/test_lib.py::test_find_irsa_sns_sqs_policy_none", "migrate/service_role/test_lib.py::test_get_environment[im-sg-prod-aws-0-production]", "migrate/service_role/test_lib.py::test_get_environment[not-xendit-]", "migrate/service_role/test_lib.py::test_get_environment[trident-im-production-0-production]", "migrate/service_role/test_lib.py::test_get_environment[trident-staging-0-staging]", "migrate/service_role/test_lib.py::test_get_environment[xnd-sg-stg-aws-0-staging]", "migrate/service_role/test_lib.py::test_get_org[iluma-iluma]", "migrate/service_role/test_lib.py::test_get_org[iluma-staging-iluma]", "migrate/service_role/test_lib.py::test_get_org[instamoney-instamoney]", "migrate/service_role/test_lib.py::test_get_org[instamoney-staging-instamoney]", "migrate/service_role/test_lib.py::test_get_org[not-xendit-]", "migrate/service_role/test_lib.py::test_get_org[xendit-staging-xendit]", "migrate/service_role/test_lib.py::test_get_org[xendit-xendit]", "migrate/service_role/test_lib.py::test_get_role_index[my-role-0]", "migrate/service_role/test_lib.py::test_get_role_index[not-found--1]", "migrate/service_role/test_lib.py::test_match_name[cashpay-service-False]", "migrate/service_role/test_lib.py::test_match_name[payment-statement-service-development-True]", "migrate/service_role/test_lib.py::test_match_name_all", "migrate/service_role/test_lib.py::test_match_namespace[cashpay-live-True]", "migrate/service_role/test_lib.py::test_match_namespace[payout-dev-False]", "migrate/service_role/test_lib.py::test_match_namespace_all", "migrate/service_role/test_lib.py::test_render_tf_import_im", "migrate/service_role/test_lib.py::test_render_tf_import_im_stg", "migrate/service_role/test_lib.py::test_render_tf_import_xnd", "migrate/service_role/test_lib.py::test_render_tf_import_xnd_custom", "migrate/service_role/test_lib.py::test_render_tf_import_xnd_stg", "migrate/workers_and_crons/test_main.py::test_rollback", "migrate/workers_and_crons/test_main.py::test_rollback_dry_run", "migrate/workers_and_crons/test_main.py::test_rollback_idempotent", "migrate/workers_and_crons/test_main.py::test_rollback_warn", "migrate/workers_and_crons/test_main.py::test_rollback_workload_name", "model/service_components/test_r53_fawkes_destination.py::test_r53_fawkes_destination_migrate", "model/service_components/test_r53_fawkes_destination_lib.py::test_upsert_fawkes_destination", "model/service_components/test_service_role.py::test_filter_irsa", "model/service_components/test_service_role.py::test_migrate", "model/service_components/test_service_role.py::test_process_service_account", "model/service_components/test_service_role_lib.py::test_check_modes_consistency[modes0-False]", "model/service_components/test_service_role_lib.py::test_check_modes_consistency[modes1-True]", "model/service_components/test_service_role_lib.py::test_check_modes_consistency[modes2-True]", "model/service_components/test_service_role_lib.py::test_check_modes_consistency[modes3-False]", "model/service_components/test_service_role_lib.py::test_check_modes_consistency[modes4-False]", "model/service_components/test_service_role_lib.py::test_contains_sa[cashpay-live-True]", "model/service_components/test_service_role_lib.py::test_contains_sa[payout-dev-False]", "model/service_components/test_service_role_lib.py::test_extract_chamber_modes", "model/service_components/test_service_role_lib.py::test_extract_chamber_modes_not_found", "model/service_components/test_service_role_lib.py::test_extract_chamber_services", "model/service_components/test_service_role_lib.py::test_extract_chamber_services_not_found", "model/service_components/test_service_role_lib.py::test_extract_consumed_sqs", "model/service_components/test_service_role_lib.py::test_extract_consumed_sqs_not_found", "model/service_components/test_service_role_lib.py::test_extract_kms_keys", "model/service_components/test_service_role_lib.py::test_extract_kms_keys_not_found", "model/service_components/test_service_role_lib.py::test_extract_published_sns", "model/service_components/test_service_role_lib.py::test_extract_published_sns_not_found", "model/service_components/test_service_role_lib.py::test_extract_published_sqs", "model/service_components/test_service_role_lib.py::test_extract_published_sqs_not_found", "model/service_components/test_service_role_lib.py::test_find_chamber_policy", "model/service_components/test_service_role_lib.py::test_find_chamber_policy_not_found", "model/service_components/test_service_role_lib.py::test_find_irsa_custom_policies", "model/service_components/test_service_role_lib.py::test_find_irsa_custom_policies_not_found", "model/service_components/test_service_role_lib.py::test_find_irsa_sns_sqs_policy", "model/service_components/test_service_role_lib.py::test_find_irsa_sns_sqs_policy_none", "model/service_components/test_service_role_lib.py::test_get_environment[im-sg-prod-aws-0-production]", "model/service_components/test_service_role_lib.py::test_get_environment[not-xendit-]", "model/service_components/test_service_role_lib.py::test_get_environment[trident-im-production-0-production]", "model/service_components/test_service_role_lib.py::test_get_environment[trident-staging-0-staging]", "model/service_components/test_service_role_lib.py::test_get_environment[xnd-sg-stg-aws-0-staging]", "model/service_components/test_service_role_lib.py::test_get_org[iluma-iluma]", "model/service_components/test_service_role_lib.py::test_get_org[iluma-staging-iluma]", "model/service_components/test_service_role_lib.py::test_get_org[instamoney-instamoney]", "model/service_components/test_service_role_lib.py::test_get_org[instamoney-staging-instamoney]", "model/service_components/test_service_role_lib.py::test_get_org[not-xendit-]", "model/service_components/test_service_role_lib.py::test_get_org[xendit-staging-xendit]", "model/service_components/test_service_role_lib.py::test_get_org[xendit-xendit]", "model/service_components/test_service_role_lib.py::test_get_role_index[my-role-0]", "model/service_components/test_service_role_lib.py::test_get_role_index[not-found--1]", "model/service_components/test_service_role_lib.py::test_match_name[cashpay-service-False]", "model/service_components/test_service_role_lib.py::test_match_name[payment-statement-service-development-True]", "model/service_components/test_service_role_lib.py::test_match_name_all", "model/service_components/test_service_role_lib.py::test_match_namespace[cashpay-live-True]", "model/service_components/test_service_role_lib.py::test_match_namespace[payout-dev-False]", "model/service_components/test_service_role_lib.py::test_match_namespace_all", "model/service_components/test_service_role_lib.py::test_render_tf_import_im", "model/service_components/test_service_role_lib.py::test_render_tf_import_im_stg", "model/service_components/test_service_role_lib.py::test_render_tf_import_xnd", "model/service_components/test_service_role_lib.py::test_render_tf_import_xnd_custom", "model/service_components/test_service_role_lib.py::test_render_tf_import_xnd_stg", "model/service_components/test_workers_crons.py::test_rollback", "model/service_components/test_workers_crons.py::test_rollback[False-None-replicas0-suspends0-<lambda>]", "model/service_components/test_workers_crons.py::test_rollback[False-None-replicas3-suspends3-<lambda>]", "model/service_components/test_workers_crons.py::test_rollback[False-app1-worker-replicas2-suspends2-<lambda>]", "model/service_components/test_workers_crons.py::test_rollback[True-None-replicas1-suspends1-<lambda>]", "model/service_components/test_workers_crons.py::test_rollback_dry_run", "model/service_components/test_workers_crons.py::test_rollback_idempotent", "model/service_components/test_workers_crons.py::test_rollback_warn", "model/service_components/test_workers_crons.py::test_rollback_workload_name", "model/test_cluster.py::test_cluster", "model/test_cluster.py::test_source_cluster", "model/test_globals.py::TestCache1::test_dest_cluster", "model/test_globals.py::TestCache1::test_source_cluster", "model/test_globals.py::TestCache::test_source_cluster", "model/test_globals.py::test_dest_cluster", "model/test_globals.py::test_source_cluster"]