# Fawkes Migration Tool

A comprehensive CLI tool for migrating AWS EKS infrastructure components between clusters, specifically designed for transitioning from legacy infrastructure to the Fawkes platform.

## Overview

The Fawkes Migration Tool provides automated migration capabilities for:

- **Service Roles (IRSA)**: Migrates IAM Roles for Service Accounts (IRSA) management to team-centric infrastructure
- **Workers and Crons**: Migrates Kubernetes deployments and cronjobs between EKS clusters
- **Traffic Switching**: Automates traffic switching of web services from Trident to Fawkes
- **Route53 Management**: Converts Route53 records and manages DNS routing during migration

## Architecture

```
fawkes-migration/
├── main.py                          # Main CLI entry point
├── migrate/
│   ├── service_role/               # IRSA migration module
│   │   ├── main.py                # Service role migration CLI
│   │   ├── lib.py                 # Core migration functions
│   │   ├── templates/             # Terraform import templates
│   │   └── test_lib.py            # Unit tests
│   └── workers_and_crons/         # Workload migration module
│       ├── main.py                # Workers/crons migration CLI
│       ├── cluster.py             # Cluster management
│       ├── workload.py            # Workload abstractions
│       ├── strategy.py            # Migration strategies
│       └── test_main.py           # Unit tests
├── fawkes_traffic_switcher/        # Traffic switching module
│   ├── main.py                    # Traffic switching CLI
│   ├── lib.py                     # Traffic switching logic
│   ├── model.py                   # Data models
│   └── target/                    # Configuration files
├── r53_weighted_converter/         # Route53 converter module
│   ├── main.py                    # Route53 conversion CLI
│   ├── lib.py                     # Route53 conversion logic
│   └── convert_in_teamcentric.py  # Team-centric variant
├── r53_fawkes_destination/         # Route53 destination module
│   ├── main.py                    # Route53 destination CLI
│   └── lib.py                     # Route53 destination logic
├── pyproject.toml                  # Project configuration
└── docs/                           # Documentation
```

## Features

### Service Role Migration
- Discovers all IRSA-enabled service accounts in source cluster
- Extracts IAM role policies and configurations
- Generates team-centric infrastructure configurations
- Creates Terraform import blocks for existing resources
- Supports filtering by namespace, service name, and mode
- Handles Chamber services, SNS/SQS policies, and custom policies

### Workers and Crons Migration
- Interactive and non-interactive migration modes
- Backup and rollback capabilities
- Multiple migration strategies (single/multiple replicas)
- Real-time validation and error handling
- Supports selective workload migration
- ArgoCD application awareness

### Traffic Switching
- Automated traffic switching from Trident to Fawkes clusters
- Multiple switching strategies (Traefik Private/Public, Ferry Boat)
- Automatic rollback on errors or interruption
- Health check validation before and after switching
- Support for CloudFront and Route53 configurations

### Route53 Management
- Converts Route53 CNAME records from simple to weighted routing
- Manages Route53 destinations for Fawkes migration
- Team-centric configuration support
- Rollback capabilities for DNS changes

## Prerequisites

- Python 3.13+
- [uv](https://docs.astral.sh/uv/getting-started/installation/) package manager
- AWS CLI configured with appropriate profiles
- kubectl configured for source and destination clusters
- Appropriate IAM permissions for cluster access

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd python/fawkes-migration
```

2. Install dependencies:
```bash
uv sync
```

3. Verify installation:
```bash
uv run python main.py --help
```

## Quick Start

### Service Role Migration

```bash
# Migrate IRSA configurations from source to destination cluster
uv run python main.py \
  --source-cluster trident-production-0 \
  --dest-cluster xnd-sg-prod-aws-0 \
  --region ap-southeast-1 \
  --aws-profile xendit-production \
  --mode live \
  --namespace payment-live \
  --dry-run true \
  migrate service-role
```

### Workers and Crons Migration

```bash
# Interactive migration mode
uv run python main.py \
  --source-cluster trident-production-0 \
  --dest-cluster xnd-sg-prod-aws-0 \
  --region ap-southeast-1 \
  --aws-profile xendit-production \
  --namespace payment-live \
  migrate workers-and-crons \
  --application payment-api \
  repl
```

### Traffic Switching

```bash
# Switch traffic using configuration file
uv run python main.py \
  --dry-run true \
  migrate fawkes-traffic-switcher \
  --input-file target/phase-1.yaml
```

### Route53 Conversion

```bash
# Convert Route53 records to weighted routing
uv run python main.py \
  --source-cluster trident-staging-0 \
  --dest-cluster xnd-sg-stg-aws-0 \
  --region ap-southeast-1 \
  --aws-profile xendit-staging \
  --mode all \
  --dry-run false \
  migrate r53-weighted-converter
```

## Configuration Options

### Global Options

| Option | Description | Required | Default |
|--------|-------------|----------|---------|
| `--source-cluster` | Source EKS cluster name | Yes | - |
| `--dest-cluster` | Destination EKS cluster name | Yes | - |
| `--region` | AWS region | Yes | - |
| `--aws-profile` | AWS profile name | Yes | - |
| `--mode` | Processing mode (dev/live/all) | No | dev |
| `--namespace` | Target namespaces | No | all |
| `--dry-run` | Enable dry-run mode | No | true |
| `--debug` | Enable debug logging | No | false |

### Service Role Options

| Option | Description | Default |
|--------|-------------|---------|
| `--start-index` | Starting index for processing | 0 |
| `--end-index` | Ending index for processing | unlimited |
| `--force-import` | Generate Terraform imports for existing entries | false |
| `--name` | Target service account names | all |
| `--exclude` | Exclude service account names | none |
| `--add-dest-cluster` | Add destination cluster to service role | true |
| `--write-irsa` | Write IRSA data to YAML file | true |

### Workers and Crons Options

| Option | Description | Default |
|--------|-------------|---------|
| `--application` | ArgoCD application name | Required |
| `--workload` | Specific workload to migrate | all |
| `--workers` | Enable worker migration | true |
| `--cronjobs` | Enable cronjob migration | true |

### Traffic Switcher Options

| Option | Description | Default |
|--------|-------------|---------|
| `--input-file` | Input file with traffic switch targets | input.yaml |
| `--validate-terminal-progression` | Validate initial and end weights | true |
| `--allow-reuse-lb` | Allow reusing existing CloudFront LB | false |

## Usage Examples

### Complete Service Role Migration

```bash
# 1. Dry run to preview changes
uv run python main.py \
  --source-cluster trident-production-0 \
  --dest-cluster xnd-sg-prod-aws-0 \
  --region ap-southeast-1 \
  --aws-profile xendit-production \
  --mode live \
  --dry-run true \
  migrate service-role

# 2. Execute migration
uv run python main.py \
  --source-cluster trident-production-0 \
  --dest-cluster xnd-sg-prod-aws-0 \
  --region ap-southeast-1 \
  --aws-profile xendit-production \
  --mode live \
  --dry-run false \
  migrate service-role
```

### Interactive Workers Migration

```bash
# Start interactive session
uv run python main.py \
  --source-cluster trident-production-0 \
  --dest-cluster xnd-sg-prod-aws-0 \
  --region ap-southeast-1 \
  --aws-profile xendit-production \
  --namespace payment-live \
  migrate workers-and-crons \
  --application payment-api \
  repl

# In the interactive prompt:
> list                    # Show current workload status
> backup                  # Create backup
> migrate --dry-run       # Preview migration
> migrate                 # Execute migration
> rollback                # Rollback if needed
```

### Traffic Switching Workflow

```bash
# 1. Prepare input configuration file
cat > traffic-switch.yaml << EOF
traffic_switch:
  api.example.com:
    strategy: TRAEFIK_PUBLIC
    source_set_id: Z123456789
    target_set_id: Z987654321
    progression:
      - weight: 0
      - weight: 10
      - weight: 50
      - weight: 100
EOF

# 2. Dry run traffic switch
uv run python main.py \
  --dry-run true \
  migrate fawkes-traffic-switcher \
  --input-file traffic-switch.yaml

# 3. Execute traffic switch
uv run python main.py \
  --dry-run false \
  migrate fawkes-traffic-switcher \
  --input-file traffic-switch.yaml

# 4. Check service health
uv run python main.py \
  migrate fawkes-traffic-switcher \
  check --input-file traffic-switch.yaml

# 5. Rollback if needed
uv run python main.py \
  --dry-run false \
  migrate fawkes-traffic-switcher \
  rollback --input-file traffic-switch.yaml
```

### Route53 Management

```bash
# Convert simple CNAME to weighted routing
uv run python main.py \
  --source-cluster trident-production-0 \
  --dest-cluster xnd-sg-prod-aws-0 \
  --region ap-southeast-1 \
  --aws-profile xendit-production \
  --mode live \
  --dry-run false \
  migrate r53-weighted-converter

# Team-centric variant
uv run python main.py \
  migrate r53-weighted-converter \
  convert-in-teamcentric \
  --yaml-file input-r53.yaml \
  --aws-profile xendit-production \
  --dry-run false

# Manage Route53 destinations
uv run python main.py \
  --source-cluster trident-production-0 \
  --dest-cluster xnd-sg-prod-aws-0 \
  --region ap-southeast-1 \
  --aws-profile xendit-production \
  migrate r53-fawkes-destination
```

## Available Commands

### Main Commands

| Command | Description |
|---------|-------------|
| `migrate service-role` | Migrate IRSA service roles |
| `migrate workers-and-crons` | Migrate Kubernetes workloads |
| `migrate fawkes-traffic-switcher` | Switch traffic between clusters |
| `migrate r53-weighted-converter` | Convert Route53 records |
| `migrate r53-fawkes-destination` | Manage Route53 destinations |

### Traffic Switcher Subcommands

| Subcommand | Description |
|------------|-------------|
| `check` | Validate service health |
| `rollback` | Rollback traffic switch |
| `generate-config` | Generate configuration from hostnames |

### Workers and Crons Subcommands

| Subcommand | Description |
|------------|-------------|
| `repl` | Start interactive session |
| `list` | List workloads |
| `backup` | Create backup |
| `migrate` | Execute migration |
| `rollback` | Rollback migration |
| `restore-from-backup` | Restore from backup |

## Testing

Run the test suite:

```bash
# Run all tests
uv run pytest -vv

# Run specific module tests
uv run pytest migrate/service_role/test_lib.py -vv
uv run pytest migrate/workers_and_crons/test_main.py -vv
uv run pytest fawkes_traffic_switcher/test_lib.py -vv

# Run with coverage
uv run pytest --cov=migrate --cov=fawkes_traffic_switcher --cov-report=html
```

## Docker Support

Several modules support Docker execution:

### Traffic Switcher

```bash
# Build and run in container
cd fawkes_traffic_switcher
make shell
uv sync --frozen
uv run main.py --input-file target/phase-1.yaml
```

### Route53 Converter

```bash
# Build and run in container
cd r53_weighted_converter
make shell
uv sync --frozen
uv run main.py --aws-profile xendit-staging --region ap-southeast-1
```

## Configuration Files

### Traffic Switcher Configuration

Example traffic switch configuration:

```yaml
traffic_switch:
  api.example.com:
    strategy: TRAEFIK_PUBLIC
    source_set_id: Z123456789ABCDEF
    target_set_id: Z987654321FEDCBA
    progression:
      - weight: 0
      - weight: 25
      - weight: 50
      - weight: 75
      - weight: 100

  internal-api.example.com:
    strategy: TRAEFIK_PRIVATE
    source_set_id: Z111111111111111
    target_set_id: Z222222222222222
    progression:
      - weight: 0
      - weight: 100

  cdn.example.com:
    strategy: FERRY_BOAT
    source_cf_origin: trident-origin
    target_cf_origin: fawkes-origin
    progression:
      - weight: 0
      - weight: 10
      - weight: 30
      - weight: 70
      - weight: 100
```

### Service Role Input

Service role migration uses team-centric input files:

```yaml
aws:
  xendit:
    global:
      service_role:
        production:
          - role_name: payment-service-live
            service_name: payment-service
            mode: live
            clusters:
              - trident-production-0
              - xnd-sg-prod-aws-0
            service_accounts:
              - name: payment-service-live
                namespace: payment-live
            chamber_services:
              - additional-service
            enable_sns_sqs: true
```

## Troubleshooting

### Common Issues

1. **Missing AWS Credentials**
   ```bash
   aws configure --profile <profile-name>
   aws sts get-caller-identity --profile <profile-name>
   ```

2. **Kubectl Access Issues**
   ```bash
   aws eks update-kubeconfig --name <cluster> --region <region> --profile <profile>
   kubectl get nodes
   ```

3. **Permission Errors**
   - Ensure IAM permissions for EKS, Route53, CloudFront
   - Verify kubectl RBAC permissions

4. **Migration Failures**
   - Check logs with `--debug` flag
   - Use `--dry-run` to preview changes
   - Verify cluster connectivity

### Debug Mode

Enable detailed logging:

```bash
uv run python main.py --debug true <other-options>
```

### Rollback Procedures

Each migration type supports rollback:

```bash
# Service roles: Manual Terraform revert
git revert <commit>
terraform apply

# Workers/crons: Automatic rollback
> rollback  # In interactive mode

# Traffic switching: Automatic rollback
uv run python main.py migrate fawkes-traffic-switcher rollback --input-file <file>

# Route53: Manual rollback script
uv run rollback.py --source r53-<cluster>.yaml
```

## Documentation

- [API Documentation](docs/API.md) - Detailed function and class documentation
- [Migration Guide](docs/MIGRATION_GUIDE.md) - Step-by-step migration procedures
- [Developer Guide](docs/DEVELOPER_GUIDE.md) - Development setup and contributing
- [Troubleshooting](docs/TROUBLESHOOTING.md) - Common issues and solutions

## Support

For issues and questions:
1. Check the [Troubleshooting Guide](docs/TROUBLESHOOTING.md)
2. Review existing issues in the repository
3. Create a new issue with detailed information

## License

[Add license information here]
