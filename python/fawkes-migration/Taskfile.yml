version: '3'

vars:
  SOURCE_PORT: 18080
  DEST_PORT: 18081
  KWOK_VERSION: v0.7.0
  K8S_VERSION: v1.31.6

tasks:
  start_source_cluster:
    cmds:
      - docker run --detach -p {{.SOURCE_PORT}}:8080 --name kwok-1 registry.k8s.io/kwok/cluster:{{.KWOK_VERSION}}-k8s.{{.K8S_VERSION}}
      - sleep 5
      - kubectl -s localhost:{{.SOURCE_PORT}} apply -k test_data/crds
      - sleep 3
      - kubectl -s localhost:{{.SOURCE_PORT}} apply -k test_data/source_cluster
  stop_source_cluster:
    cmds:
      - docker container rm -f kwok-1

  start_dest_cluster:
    cmds:
      - docker run --detach -p {{.DEST_PORT}}:8080 --name kwok-2 registry.k8s.io/kwok/cluster:{{.KWOK_VERSION}}-k8s.{{.K8S_VERSION}}
  stop_dest_cluster:
    cmds:
      - docker container rm -f kwok-2

  start_clusters:
    deps:
      - start_source_cluster
      - start_dest_cluster

  stop_clusters:
    deps:
      - stop_source_cluster
      - stop_dest_cluster

  pytest:
    cmds:
      - uv run pytest

  test:
    cmds:
      - task: start_clusters
      - task: pytest
      - task: stop_clusters
