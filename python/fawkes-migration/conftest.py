from types import SimpleNamespace
import logging
from unittest.mock import Mock

import pytest
import kubernetes

import team_centric
from model import globals

@pytest.fixture(scope='session')
def constants():
    return SimpleNamespace(
        SOURCE_PORT=18080,
        DEST_PORT=18081,
    )

@pytest.fixture(autouse=True, scope='session')
def set_inputs():
    globals.register_inputs(
        debug=False,
        service=['ewallet-service-v2-development'],
        namespace=['digipay-dev'],
        source_cluster='src-stg',
        dest_cluster='dst-stg',
        region='ap-southeast-1',
        aws_profile='xendit-pytest',
        dry_run=True
    )

class _TestCache(globals.Cache):
    def __init__(self, source_port, dest_port):
        self.source_port = source_port
        self.dest_port = dest_port

    def source_client_factory(self):
        return lambda : kubernetes.client.ApiClient(
            configuration=kubernetes.client.Configuration(host=f'http://localhost:{self.source_port}')
        )

    def dest_client_factory(self):
        return lambda : kubernetes.client.ApiClient(
            configuration=kubernetes.client.Configuration(host=f'http://localhost:{self.dest_port}')
        )

@pytest.fixture(autouse=True, scope='session')
def patch_cache(constants):
    setattr(globals, 'cache', _TestCache(constants.SOURCE_PORT, constants.DEST_PORT))

@pytest.fixture(autouse=True)
def patch_team_centric_dir(monkeypatch):
    monkeypatch.setattr(team_centric, "TEAM_CENTRIC_DIR", "./test_data/team-centric")

    monkeypatch.setattr(team_centric, "write_input", Mock())
    monkeypatch.setattr(team_centric, "write_file", Mock())

@pytest.fixture(autouse=True)
def mock_aws_session(monkeypatch):
    session = Mock()
    clients = {}

    def mock_client(svc, *args, **kwargs):
        return clients.setdefault(svc, Mock())

    session.client = mock_client
    monkeypatch.setattr(globals.cache, "aws_session", session)
    return session
