{"test_lib.py::test_infer_strategy_private": true, "test_lib.py::test_infer_strategy_public": true, "test_lib.py::test_infer_strategy_ferry_boat": true, "test_lib.py::test_infer_strategy_ferry_none": true, "test_lib.py::test_infer_trident_r53_set_id_none": true, "test_lib.py::test_infer_trident_r53_set_id": true, "test_lib.py::test_infer_trident_r53_set_id_multiple": true, "test_lib.py::test_infer_trident_cf_origin_none": true, "test_lib.py::test_infer_trident_cf_origin_proxied": true, "test_lib.py::test_infer_trident_cf_origin_not_proxied": true, "test_lib.py::test_infer_fawkes_cf_origin_none": true, "test_lib.py::test_infer_fawkes_cf_origin": true, "test_lib.py": true}