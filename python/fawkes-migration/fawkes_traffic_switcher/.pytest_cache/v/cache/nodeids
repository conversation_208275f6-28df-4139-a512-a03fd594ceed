["test_lib.py::test_infer_fawkes_cf_origin", "test_lib.py::test_infer_fawkes_cf_origin_none", "test_lib.py::test_infer_strategy_ferry_boat", "test_lib.py::test_infer_strategy_ferry_none", "test_lib.py::test_infer_strategy_private", "test_lib.py::test_infer_strategy_public", "test_lib.py::test_infer_trident_cf_origin_none", "test_lib.py::test_infer_trident_cf_origin_not_proxied", "test_lib.py::test_infer_trident_cf_origin_proxied", "test_lib.py::test_infer_trident_r53_set_id", "test_lib.py::test_infer_trident_r53_set_id_multiple", "test_lib.py::test_infer_trident_r53_set_id_none"]