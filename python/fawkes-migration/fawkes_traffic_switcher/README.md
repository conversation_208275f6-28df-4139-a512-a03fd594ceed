# Fawkes Traffic Switcher

A tool to help automating traffic switch of web services on Trident to Fawkes.

## Prerequisite

- [uv](https://docs.astral.sh/uv/)

If running in container:

- Docker
- make

## Run

To run the migration:

1. Prepare input file. See examples of input files on the `target` directory.
1. Run script on dry mode
   
   ```shell
   uv run main.py --input-file <path-to-input-file>
   ```

   Review dry run result to make sure there is no error.

1. Run script on actual mode
   ```shell
   uv main.py --input-file <path-to-input-file> --dry-run false
   ```


To run the migration on a docker container:

1. Export AWS credentials to `~/.aws/credentials` for the following profile
    - iluma
    - iluma-staging
    - instamoney
    - instamoney-staging
    - xendit
    - xendit-staging
    - gmf
1. Get shell to run the script, `make shell`
1. Install python dependencies using `uv sync --frozen`
1. Run the conversion, `uv run main.py`

## Rollback

The script will automatically rollback changes during traffic switch when it detects an error or being interrupted.
We can rollback changes after the script finish running using as follow:

```shell
uv run main.py rollback --input-file <path-to-input-file>
```

## Run Test

Use the following command to run test

```shell
uv run pytest -vv -s
```