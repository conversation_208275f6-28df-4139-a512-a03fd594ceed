import boto3
import fawkes
import infra_cf
import infra_lib
import infra_r53
import ipaddress
import logging
import time
import ruamel.yaml

from botocore.config import Config
from cloudflare import Cloudflare
from cloudflare.types.load_balancers import <PERSON>Param, LoadBalancer, Pool
from .model import TrafficSwitchTarget, TrafficSwitchProgression, TrafficSwitchStrategy
from pydantic import ValidationError
from typing import Optional


def set_record_weight(target: TrafficSwitchTarget, index: int):
    """Set Route53 record weight inside given target

    Args:
        target: Traffic switch target
        index: Index of progression from which weight will be sourced.
            -1 means setting source weight to 100 and target weight to 0.
    """
    records = infra_r53.get_record_set(target.name)
    source_record = None
    target_record = None

    if index < -1 or index >= target.num_iteration:
        raise ValueError(
            f"Given index {index} is out of range [-1..{target.num_iteration}]"
        )

    for record in records:
        if record.record_set["Type"] == target.type:
            if record.record_set["SetIdentifier"] == target.source_set_id:
                source_record = record
            elif record.record_set["SetIdentifier"] == target.target_set_id:
                target_record = record

    if source_record and target_record:
        target_weight = 0 if index == -1 else target.progression[index].weight
        source_weight = 100 - target_weight

        source_record_set = source_record.record_set
        source_record_set["Weight"] = source_weight

        target_record_set = target_record.record_set
        target_record_set["Weight"] = target_weight

        config = Config(retries={"max_attempts": 10, "mode": "adaptive"})
        session = boto3.Session(profile_name=target.source_aws_profile)
        r53 = session.client("route53", config=config)

        logging.info(
            f"Setting weight name={target.name} source_weight={source_record_set["Weight"]} target_weight={target_record_set["Weight"]}"
        )
        r53.change_resource_record_sets(
            HostedZoneId=target.source_zone_id,
            ChangeBatch={
                "Comment": "Fawkes traffic switcher script",
                "Changes": [
                    {
                        "Action": "UPSERT",
                        "ResourceRecordSet": source_record_set,
                    },
                    {
                        "Action": "UPSERT",
                        "ResourceRecordSet": target_record_set,
                    },
                ],
            },
        )
    else:
        logging.error(f"Can not find source or target record for {target.name}")


def parse_target(
    input_file: str, rollback: bool = False, validate_terminal_progression: bool = True
) -> list[TrafficSwitchTarget]:
    """Crate traffic switch target from given file

    Args:
        input_file: Input file name
        rollback: Flag to indicate whether target is for rollback operation
        validate_terminal_progression: Flag to indicate whether to validate initial and end progress weight

    Return:
        list[TrafficSwitchTarget]: List of traffic switch target.
            Empty when encounter error during reading file content or parsing.
    """
    with open(input_file) as f:
        yaml = ruamel.yaml.YAML()
        yaml.indent(mapping=2, sequence=4, offset=2)
        yaml.preserve_quotes = True
        input = yaml.load(f)

    targets = []
    try:
        for key, value in input["traffic_switch"].items():
            progression = []
            for p in value["progression"]:
                duration = p.get("duration", 0)

                logging.debug(
                    f"Creating traffic switch progression name={key} weight={p["weight"]} duration={duration}"
                )
                progression.append(
                    TrafficSwitchProgression(weight=p["weight"], duration=duration)
                )

            logging.info(f"Creating and validating traffic switch target name={key}")

            strategy = None

            match value.get("strategy"):
                case "TRAEFIK_PRIVATE":
                    strategy = TrafficSwitchStrategy.TRAEFIK_PRIVATE
                case "TRAEFIK_PUBLIC":
                    strategy = TrafficSwitchStrategy.TRAEFIK_PUBLIC
                case "FERRY_BOAT":
                    strategy = TrafficSwitchStrategy.FERRY_BOAT
                case _:
                    addrs = infra_lib.get_ip_list(key)

                    if infra_lib.is_private_ip(addrs):
                        strategy = TrafficSwitchStrategy.TRAEFIK_PRIVATE

            assert (
                strategy is not None
            ), f"Can not find properly traffic switch strategy name={key}"

            if strategy == TrafficSwitchStrategy.FERRY_BOAT:
                cf_zone = infra_cf.get_cf_zone(key)
                cf_zone_id = infra_cf.get_cf_zone_id(cf_zone)
                cf_record = infra_cf.get_cf_record(key)

                assert (
                    len(cf_record) > 0
                ), f"Can not find Cloudflare DNS record name={key}"

                cf_token = infra_cf.get_cf_token(
                    path="/iss/cloudflare/xnd-cf-prod-env-api-token"
                )

                target = TrafficSwitchTarget(
                    name=key,
                    strategy=strategy,
                    type=cf_record[0].type,
                    source_cf_origin=value["source_cf_origin"],
                    target_cf_origin=value["target_cf_origin"],
                    rollback=rollback,
                    progression=progression,
                    cf_token=cf_token,
                    cf_zone=cf_zone,
                    cf_zone_id=cf_zone_id,
                    cf_record_id=cf_record[0].id,
                    cf_record_content=cf_record[0].content,
                    cf_record_proxied=cf_record[0].proxied,
                    validate_terminal_progression=validate_terminal_progression,
                )
            else:
                target = TrafficSwitchTarget(
                    name=key,
                    validate_terminal_progression=validate_terminal_progression,
                    strategy=strategy,
                    type=value.get("type", "CNAME"),
                    source_set_id=value["source_set_id"],
                    target_set_id=value["target_set_id"],
                    rollback=rollback,
                    progression=progression,
                )

            targets.append(target)
    except (ValidationError, ValueError) as e:
        raise e

    return targets


def monitor_traefik_private(target: TrafficSwitchTarget) -> bool:
    """Monitor traefik private traffic switch to catch error when it happen

    Args:
        target: Traffic switch target

    Return:
        bool: True when there is no error detected during traffic switch. False otherwise
    """
    result = True
    error_limit = 1

    # Monitor traffic switch progress by ensuring DNS resolution is working
    error_counter = 0
    for i in range(0, 10):
        ip_addresses = infra_lib.get_ip_list(target.name)
        if len(ip_addresses) > 0:
            any_error = False
            for ip in ip_addresses:
                try:
                    addr = ipaddress.ip_address(ip)
                    if not addr.is_private:
                        logging.error(
                            f"Get non private ip addresses while monitoring traefik private traffic switch name={target.name} ip_address={ip}"
                        )
                        any_error = True
                except ValueError:
                    any_error = True
                    logging.error(
                        f"Get invalid ip address while monitoring traefik private traffic switch name={target.name} ip_address={ip}"
                    )
                    pass

            if any_error:
                error_counter += 1
        else:
            error_counter += 1
            logging.error(
                f"DNS resolution return empty ip addresses name={target.name}"
            )

        if error_counter > error_limit:
            return False

    return result


def switch_traefik_private(target: TrafficSwitchTarget, dry_run: bool = True):
    """Orchestrate traffic switch progression for traefik private

    Args:
        target: Traffic switch target
        dry_run: Flag to indicate whether to run in dry run mode

    """
    interval = 5

    for i, p in enumerate(target.progression):
        logging.info(
            f"Traffic switch dry_run={dry_run} name={target.name} strategy={target.strategy} iteration={i+1} {p.to_string()}"
        )

        if not dry_run:
            set_record_weight(target, i)

            # No need to sleep on the last iteration
            if i < target.num_iteration - 1:
                counter = 0
                while counter <= p.duration:
                    result = monitor_traefik_private(target)
                    if not result:
                        logging.error(
                            f"Rolling back due to failed monitor {target.to_compact_string()}"
                        )
                        rollback_traffic_switch(target, dry_run)
                        return

                    time.sleep(interval)
                    counter += interval


def monitor_traefik_public(target: TrafficSwitchTarget) -> bool:
    """Monitor traefik public traffic switch to catch error when it happen

    Args:
        target: Traffic switch target

    Return:
        bool: True when there is no error detected during traffic switch. False otherwise
    """
    result = True
    error_limit = 1

    # Monitor traffic switch progress by ensuring DNS resolution is working
    error_counter = 0
    for i in range(0, 10):
        ip_addresses = infra_lib.get_ip_list(target.name)
        if len(ip_addresses) > 0:
            any_error = False
            for ip in ip_addresses:
                try:
                    addr = ipaddress.ip_address(ip)
                    if not addr.is_global:
                        logging.error(
                            f"Get non global ip addresses while monitoring traefik public traffic switch name={target.name} ip_address={ip}"
                        )
                        any_error = True
                except ValueError:
                    any_error = True
                    logging.error(
                        f"Get invalid ip address while monitoring traefik public traffic switch name={target.name} ip_address={ip}"
                    )
                    pass

            if any_error:
                error_counter += 1
        else:
            error_counter += 1
            logging.error(
                f"DNS resolution return empty ip addresses name={target.name}"
            )

        if error_counter > error_limit:
            return False

    return result


def switch_traefik_public(target: TrafficSwitchTarget, dry_run: bool = True):
    """Orchestrate traffic switch progression for traefik public

    Args:
        target: Traffic switch target
        dry_run: Flag to indicate whether to run in dry run mode

    """
    interval = 5

    for i, p in enumerate(target.progression):
        logging.info(
            f"Traffic switch dry_run={dry_run} name={target.name} strategy={target.strategy} iteration={i+1} {p.to_string()}"
        )

        if not dry_run:
            set_record_weight(target, i)

            # No need to sleep on the last iteration
            if i < target.num_iteration - 1:
                counter = 0
                while counter <= p.duration:
                    result = monitor_traefik_public(target)
                    if not result:
                        logging.error(
                            f"Rolling back due to failed monitor {target.to_compact_string()}"
                        )
                        rollback_traffic_switch(target, dry_run)
                        return

                    time.sleep(interval)
                    counter += interval


def rollback_traffic_switch(target: TrafficSwitchTarget, dry_run: bool = True):
    """Rollback traffic switch by setting weight 100 on source and 0 on target

    Args:
        target: Service that is going to be rolled back
        dry_run: Flag to indicate whether to run in dry run mode
    """
    if not dry_run:
        match target.strategy:
            case TrafficSwitchStrategy.TRAEFIK_PRIVATE:
                set_record_weight(target, -1)
            case TrafficSwitchStrategy.TRAEFIK_PUBLIC:
                set_record_weight(target, -1)
            case TrafficSwitchStrategy.FERRY_BOAT:
                finalize_cf_record(target=target, rollback=True)
                delete_cf_lb(target)


def monitor_ferry_boat(target: TrafficSwitchTarget) -> bool:
    """Monitor traffic switch progress that uses ferry boat strategy

    Args:
        target: Traffic switch target that is monitored

    Return:
        bool: True if target is healthy. False otherwise
    """
    result = True

    lb = infra_cf.get_cf_lb(
        name=target.name,
        cf_token=target.cf_token,
    )

    result = infra_cf.is_cf_pool_healthy(
        account_id=target.cf_account_id,
        pool_id=lb.default_pools[0],
        cf_token=target.cf_token,
    )

    return result


def switch_ferry_boat(
    target: TrafficSwitchTarget,
    dry_run: bool = True,
    allow_reuse_lb: bool = False,
):
    """Orchestrate traffic switch progression using ferry boat strategy

    Args:
        target: Traffic switch target
        dry_run: Flag to indicate whether to run in dry run mode
        allow_reuse_lb: Flag to control whether to allow using existing CF load balancer
    """

    source_origin = {
        "name": "source_origin",
        "address": target.source_cf_origin,
        "weight": 1.0,
        "enabled": True,
    }

    target_origin = {
        "name": "target_origin",
        "address": target.target_cf_origin,
        "weight": 0.0,
        "enabled": True,
    }

    if not dry_run:
        lb = infra_cf.get_cf_lb(
            name=target.name,
            cf_token=target.cf_token,
        )

        if lb is not None:
            if not allow_reuse_lb:
                raise ValueError(
                    f"Load balancer is already exist on CF name={target.name} allow_reuse_lb={allow_reuse_lb}"
                )
        else:
            lb = create_cf_lb(
                account_id=target.cf_account_id,
                zone_id=target.cf_zone_id,
                name=target.name,
                origins=[source_origin, target_origin],
                cf_token=target.cf_token,
            )

            # Wait for load balancer to be healthy
            wait_limit = 600
            counter = 0
            healthy = False

            while counter < wait_limit:
                healthy = infra_cf.is_cf_pool_healthy(
                    account_id=target.cf_account_id,
                    pool_id=lb.default_pools[0],
                    cf_token=target.cf_token,
                )

                if healthy:
                    break

                time.sleep(15)

            if not healthy:
                raise Exception(
                    f"Pool is not healthy after waiting for {wait_limit} seconds name={target.name}"
                )

    interval = 10
    finish_traffic_switch = False

    for i, p in enumerate(target.progression):
        logging.info(
            f"Traffic switch dry_run={dry_run} name={target.name} strategy={target.strategy} iteration={i+1} {p.to_string()}"
        )
        if not dry_run:
            set_cf_pool_weight(target, i)

            if p.weight == 100:
                finish_traffic_switch = True

            # No need to sleep on the last iteration
            if i < target.num_iteration - 1:
                counter = 0
                while counter <= p.duration:
                    result = monitor_ferry_boat(target)
                    if not result:
                        logging.error(
                            f"Rolling back due to failed monitor {target.to_compact_string()}"
                        )
                        rollback_traffic_switch(target, dry_run)
                        return

                    time.sleep(interval)
                    counter += interval

    if not dry_run and finish_traffic_switch:
        # Traffic switch finished
        # Update CF DNS record and delete load balancer
        finalize_cf_record(target)
        delete_cf_lb(target)


def create_cf_lb(
    account_id: str, zone_id: str, name: str, origins: list[dict], cf_token: str
) -> LoadBalancer:
    """Create load balancer and its dependencies

    Args:
        account_id: Cloudflare account id
        zone_id: Cloudflare zone id
        name: Load balancer name
        origins: List of Cloudflare load balancer origin
        cf_token: Cloudflare token that will be used to create load balancer

    Returns:
        LoadBalancer: Newly created load balancer
    """
    result = None
    pool_created = False
    monitor_created = False

    cf = Cloudflare(
        api_token=cf_token,
        max_retries=5,
    )

    origin_params = []

    for origin in origins:
        origin_param: OriginParam = {
            "name": f"{name}-{origin["name"]}",
            "address": origin["address"],
            "enabled": True,
            "weight": origin["weight"],
        }
        origin_params.append(origin_param)

    if len(origin_params) > 0:
        try:
            # Create monitor for pool
            monitor = cf.load_balancers.monitors.create(
                account_id=account_id,
                description=name,
                expected_codes="404",
                method="GET",
                path="/",
                port=443,
                type="https",
                interval=15,
                timeout=5,
                retries=2,
                consecutive_down=2,
                consecutive_up=2,
                allow_insecure=True,
            )

            monitor_created = True

            # Create pool for load balancer
            pool = cf.load_balancers.pools.create(
                account_id=account_id,
                name=name,
                enabled=True,
                origins=origin_params,
                monitor=monitor.id,
            )

            # Edit health check region and limit it to SEAS
            # Health check region can't be specified during pool creation
            cf.load_balancers.pools.edit(
                account_id=account_id,
                pool_id=pool.id,
                check_regions=["SEAS"],
            )

            pool_created = True

            # Create load balancer
            result = cf.load_balancers.create(
                name=name,
                default_pools=[pool.id],
                fallback_pool=pool.id,
                proxied=True,
                zone_id=zone_id,
            )
        except Exception as e:
            print(
                f"monitor_create={monitor_created} pool_created={pool_created} exception={e}"
            )
            # Clean up pool when Error happen during creation of load balancer
            if pool_created:
                cf.load_balancers.pools.delete(
                    account_id=account_id,
                    pool_id=pool.id,
                )

            # Clean up monitor when Error happen during creation of load balancer
            if monitor_created:
                cf.load_balancers.monitors.delete(
                    account_id=account_id,
                    monitor_id=monitor.id,
                )

            raise Exception(f"Encounter error when creating lb name={name}: {e}")

    return result


def delete_cf_lb(target: TrafficSwitchTarget):
    """Delete given Cloudflare load balancer and all its dependencies

    Args:
        account_id: Cloudflare account id
        zone_id: Cloudflare zone id
        name: Cloudflare load balancer name
        cf_token: Cloudflare API token
    """
    name = target.name
    cf_token = target.cf_token
    zone_id = target.cf_zone_id
    account_id = target.cf_account_id

    lb = infra_cf.get_cf_lb(name, cf_token)

    if lb is not None:
        cf = Cloudflare(
            api_token=cf_token,
            max_retries=5,
        )

        monitors = []

        # Delete load balancer
        try:
            cf.load_balancers.delete(
                zone_id=zone_id,
                load_balancer_id=lb.id,
            )
        except Exception as e:
            raise Exception(
                f"Encounter error when deleting load balancer name={name}: {e}"
            )

        # Delete pool associated with the load balancer
        try:
            for pool in lb.default_pools:
                cf_pool = cf.load_balancers.pools.get(
                    account_id=account_id, pool_id=pool
                )

                if cf_pool.monitor is not None:
                    monitors.append(cf_pool.monitor)

                cf.load_balancers.pools.delete(
                    account_id=account_id,
                    pool_id=pool,
                )
        except Exception as e:
            raise Exception(
                f"Encounter error when deleting lb_name={name} pool={pool}: {e}"
            )

        # Delete monitor associated with the pool
        try:
            for monitor in monitors:
                cf.load_balancers.monitors.delete(
                    account_id=account_id,
                    monitor_id=monitor,
                )
        except Exception as e:
            raise Exception(
                f"Encounter error when deleting lb_name={name} monitor={monitor}: {e}"
            )


def set_cf_pool_weight(target: TrafficSwitchTarget, index: int):
    """Adjust Cloudflare pool weight

    Args:
        target: Traffic switch target
        index: Index of progression from which weight will be sourced.
            -1 means setting source weight to 100 and target weight to 0.
    """
    assert (
        index >= 0 and index < target.num_iteration
    ), f"Index must be within [0..{target.num_iteration}]"

    name = target.name

    cf = Cloudflare(
        api_token=target.cf_token,
        max_retries=5,
    )

    lb = infra_cf.get_cf_lb(name=name, cf_token=target.cf_token)
    assert lb is not None, f"Fail to get CF load balancer name={name}"

    pool_id = lb.default_pools[0]

    pool: Pool = cf.load_balancers.pools.get(
        pool_id=pool_id, account_id=target.cf_account_id
    )
    assert pool is not None, f"Fail to get CF load balancer pool name={name}"

    target_weight = (
        0.0 if index == -1 else round(target.progression[index].weight / 100, 2)
    )

    source_weight = round(1.0 - target_weight, 2)

    source_origin_param: OriginParam = {
        "name": pool.origins[0].name,
        "address": pool.origins[0].address,
        "enabled": pool.origins[0].enabled,
        "weight": source_weight,
    }

    target_origin_param: OriginParam = {
        "name": pool.origins[1].name,
        "address": pool.origins[1].address,
        "enabled": pool.origins[1].enabled,
        "weight": target_weight,
    }

    try:
        cf.load_balancers.pools.edit(
            account_id=target.cf_account_id,
            pool_id=pool_id,
            origins=[source_origin_param, target_origin_param],
        )
    except Exception as e:
        raise Exception(f"Encounter error while adjusting pool weight name={name}: {e}")


def finalize_cf_record(target: TrafficSwitchTarget, rollback: bool = False):
    """Edit CF DNS record to target CF origin

    Args:
        target: Traffic switch target
        rollback: Flag to indicate whether to rollback changes
    """
    cf = Cloudflare(
        api_token=target.cf_token,
        max_retries=5,
    )

    content = target.target_cf_origin
    proxied = True

    if rollback:
        content = target.cf_record_content
        proxied = target.cf_record_proxied

    cf.dns.records.edit(
        dns_record_id=target.cf_record_id,
        zone_id=target.cf_zone_id,
        name=target.name,
        type=target.type,
        content=content,
        proxied=proxied,
    )


def infer_strategy(host: str) -> Optional[str]:
    """Infer matching traffic switch strategy for given host

    Args:
        host: Domain name

    Returns:
        Optional[str]: Traffic switch strategy for the host
    """
    result = None
    ips = infra_lib.get_ip_list(host)

    if infra_lib.is_private_ip(ips):
        result = "TRAEFIK_PRIVATE"
    elif infra_lib.is_global_ip(ips):
        if infra_lib.is_aws_ip(ips):
            result = "TRAEFIK_PUBLIC"
        elif infra_cf.is_cloudflare_ip(ips):
            result = "FERRY_BOAT"

    return result


def infer_trident_r53_set_id(host: str) -> Optional[str]:
    """Infer Route53 set id that represents trident destination

       Route53 record that represent destination to trident has weight of 100
       or the highest weight among record with the same name

    Args:
        host: Domain name

    Returns:
        Optional[str]: Route53 set id
    """
    set_id = None

    record_sets = infra_r53.get_record_set(host)

    if len(record_sets) > 0:
        weighted_record_sets = [
            rs
            for rs in record_sets
            if "Weight" in rs.record_set and rs.record_set["SetIdentifier"] != "fawkes"
        ]
        highest_weight = sorted(
            weighted_record_sets, key=lambda rs: rs.record_set["Weight"]
        )[-1]
        set_id = highest_weight.record_set["SetIdentifier"]

    return set_id


def infer_fawkes_r53_set_id(host: str) -> Optional[str]:
    """Infer Route53 set id that represents fawkes destination

       Route53 record that represent destination to fawkes is always set to fawkes

    Args:
        host: Domain name

    Returns:
        Optional[str]: Route53 set id
    """
    set_id = "fawkes"

    return set_id


def infer_trident_cf_origin(host: str) -> Optional[str]:
    """Infer Cloudflare origin that represents trident destination

    Args:
        host: Domain name

    Returns:
        Optional[str]: Cloudflare load balancer origin
    """
    origin = None

    records = infra_cf.get_cf_record(host)

    if len(records) > 0:
        proxied = records[0].proxied
        content = records[0].content

        if proxied:
            if fawkes.is_routed_to_trident_public(content):
                origin = content
        else:
            lb = infra_cf.get_cf_lb(content)

            if lb:
                for pool_id in lb.default_pools:
                    pool = infra_cf.get_cf_pool(pool_id)

                    for item in pool.origins:
                        if item.weight > 0.0:
                            if fawkes.is_routed_to_trident_public(item.address):
                                origin = item.address
                                break

                    if origin is not None:
                        break

    return origin


def infer_fawkes_cf_origin(host: str) -> Optional[str]:
    """Infer Cloudflare origin that represents fawkes destination

    Args:
        host: Domain name

    Returns:
        Optional[str]: Cloudflare load balancer origin
    """
    origin = None

    trident_cf_origin = infer_trident_cf_origin(host)

    if trident_cf_origin is not None:
        trident_cluster = fawkes.get_cluster(trident_cf_origin)
        fawkes_cluster = fawkes.get_fawkes_cluster(trident_cluster)
        origin = fawkes.get_traefik_domain(cluster=fawkes_cluster)

    return origin
