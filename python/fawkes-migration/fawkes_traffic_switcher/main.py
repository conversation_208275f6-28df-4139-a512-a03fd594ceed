import sys
import logging
import signal

import click
import ruamel.yaml

import infra_lib
from . import lib
from .model import TrafficSwitchStrategy

# Global variable to hold current target for traffic switching
CURRENT_TARGET = None
# Global flag to indicate whether the script is running in dry run mode
DRY_RUN_MODE = True


def sigterm_handler(_signo, _stack_frame):
    if CURRENT_TARGET is not None and not DRY_RUN_MODE:
        logging.error(
            f"Rolling back due to signal {_signo} {CURRENT_TARGET.to_compact_string()}"
        )
        lib.rollback_traffic_switch(CURRENT_TARGET, DRY_RUN_MODE)

    sys.exit(1)


@click.group(invoke_without_command=True)
@click.pass_context
@click.option(
    "--input-file",
    default="input.yaml",
    show_default=True,
    help="Input file that contains target for traffic switching",
)
@click.option(
    "--validate-terminal-progression",
    default=True,
    show_default=True,
    help="Flag to control whether validation of initial and end weight of traffic switch progression",
)
@click.option(
    "--allow-reuse-lb",
    default=False,
    show_default=True,
    help="Flag to control whether to allow reusing existing CF load balancer",
)
def main(
    ctx,
    input_file: str,
    validate_terminal_progression: bool,
    allow_reuse_lb: bool,
):
    dry_run = ctx.parent.params['dry_run']

    if ctx.invoked_subcommand is None:
        global DRY_RUN_MODE
        DRY_RUN_MODE = dry_run

        logging.info(
            f"Running in dry_run={dry_run} validate_terminal_progression={validate_terminal_progression} allow_reuse_lb={allow_reuse_lb}"
        )

        signal.signal(signal.SIGTERM, sigterm_handler)

        try:
            targets = lib.parse_target(
                input_file=input_file,
                validate_terminal_progression=validate_terminal_progression,
            )
        except Exception as e:
            print(f"{e}")
            sys.exit(1)

        try:
            for target in targets:
                logging.info(f"Starting traffic switch for {target.to_string()}")
                global CURRENT_TARGET
                CURRENT_TARGET = target
                match target.strategy:
                    case TrafficSwitchStrategy.TRAEFIK_PRIVATE:
                        lib.switch_traefik_private(target, dry_run)
                    case TrafficSwitchStrategy.TRAEFIK_PUBLIC:
                        lib.switch_traefik_public(target, dry_run)
                    case TrafficSwitchStrategy.FERRY_BOAT:
                        lib.switch_ferry_boat(
                            target=target,
                            dry_run=dry_run,
                            allow_reuse_lb=allow_reuse_lb,
                        )
                    case _:
                        logging.warning(
                            f"Skipping traffic switch due to unknown strategy {target.to_string()}"
                        )
        except ValueError as e:
            logging.error(
                f"Stopping the script due to exception {e} {target.to_compact_string()}"
            )
            sys.exit(1)
        except Exception as e:
            logging.error(
                f"Rolling back due to exception {e} {target.to_compact_string()}"
            )
            lib.rollback_traffic_switch(target, dry_run)
            sys.exit(1)
        except KeyboardInterrupt:
            logging.error(
                f"Rolling back due to keyboard interrupt {target.to_compact_string()}"
            )
            lib.rollback_traffic_switch(target, dry_run)
            sys.exit(1)


@main.command()
@click.option(
    "--input-file",
    default="input.yaml",
    show_default=True,
    help="Input file that contains target that will be rolled back",
)
@click.pass_context
def rollback(ctx, input_file: str):
    dry_run = ctx.parent.params['dry_run']
    logging.info(f"rollback input_file={input_file} dry_run={dry_run}")
    targets = lib.parse_target(input_file=input_file, rollback=True)
    for target in targets:
        logging.info(f"Rolling back traffic switch for {target.to_compact_string()}")
        lib.rollback_traffic_switch(target, dry_run)


@main.command()
@click.option(
    "--input-file",
    default="input.yaml",
    show_default=True,
    help="Input file that contains target that will be checked",
)
def check(input_file: str):
    success = True
    targets = lib.parse_target(input_file=input_file)

    for target in targets:
        if target.strategy == TrafficSwitchStrategy.FERRY_BOAT:
            logging.info(f"Skip checking host due to unsupported name={target.name}")
        else:
            source_http_code = infra_lib.get_http_code(target=target.name)

            if source_http_code != "000":
                fawkes_target = str(target.target_ip_addresses[0])
                fawkes_http_code = infra_lib.get_http_code(
                    target=target.name, via_host=fawkes_target
                )

                if source_http_code == fawkes_http_code:
                    logging.info(
                        f"Check success name={target.name} path=/healthcheck/readiness http_code={source_http_code}"
                    )
                else:
                    logging.info(
                        f"Check failed name={target.name} path=/healthcheck/readiness http_code={source_http_code} fawkes_destination={fawkes_target} fawkes_http_code={fawkes_http_code}"
                    )
                    success = False
            else:
                logging.info(
                    f"Check failed name={target.name} path=/healthcheck/readiness http_code={source_http_code}"
                )
                success = False

    if not success:
        sys.exit(1)


@main.command()
@click.argument("hosts_file", type=click.File(), default="-")
def generate_config(hosts_file: click.File):
    """
    Generate a configuration snippet based on a list of hostnames in HOSTS_FILE.

    If HOSTS_FILE is not given, read from standard input.
    """
    targets = [host for line in hosts_file for host in line.split()]

    traffic_switch = {"traffic_switch": {}}
    for target in targets:
        result = traffic_switch["traffic_switch"]
        strategy = lib.infer_strategy(target)

        if strategy is not None:
            key = target
            result[key] = {"strategy": strategy}

            if strategy in ["TRAEFIK_PRIVATE", "TRAEFIK_PUBLIC"]:
                result[key]["source_set_id"] = lib.infer_trident_r53_set_id(target)
                result[key]["target_set_id"] = lib.infer_fawkes_r53_set_id(target)
            elif strategy == "FERRY_BOAT":
                result[key]["source_cf_origin"] = lib.infer_trident_cf_origin(target)
                result[key]["target_cf_origin"] = lib.infer_fawkes_cf_origin(target)

    yaml = ruamel.yaml.YAML(typ=["rt", "string"])
    yaml.indent(mapping=2, sequence=4, offset=2)
    yaml.width = 10240
    yaml.dump(traffic_switch, sys.stdout)


if __name__ == "__main__":
    main()
