import infra_lib
import infra_r53
import ipaddress


from typing import List, Optional
from typing_extensions import Self
from pydantic import BaseModel, Field, field_validator, computed_field, model_validator
from enum import Enum


class TrafficSwitchStrategy(Enum):
    TRAEFIK_PRIVATE = 1
    TRAEFIK_PUBLIC = 2
    FERRY_BOAT = 3


class TrafficSwitchProgression(BaseModel):
    weight: int = Field(gt=0, le=100)
    duration: int = Field(ge=0)

    def to_string(self) -> str:
        return f"weight={self.weight} duration={self.duration}"


class TrafficSwitchTarget(BaseModel):
    name: str
    strategy: TrafficSwitchStrategy
    type: Optional[str] = Field(pattern=r"^(A|CNAME)$")
    source_set_id: Optional[str] = "mass_conversion"
    target_set_id: Optional[str] = "fawkes"
    rollback: bool = False
    progression: List[TrafficSwitchProgression]
    source_cf_origin: Optional[str] = ""
    target_cf_origin: Optional[str] = ""
    cf_account_id: Optional[str] = "0b4765f36e2c522694fffab17f855174"
    cf_zone: Optional[str] = ""
    cf_zone_id: Optional[str] = ""
    cf_token: Optional[str] = ""
    cf_record_id: Optional[str] = ""
    cf_record_content: Optional[str] = ""
    cf_record_proxied: Optional[bool] = False
    validate_terminal_progression: bool = True

    @computed_field
    @property
    def num_iteration(self) -> str:
        return len(self.progression)

    @computed_field
    @property
    def total_duration(self) -> str:
        duration = 0

        for p in self.progression:
            duration += p.duration

        return duration

    @computed_field
    @property
    def source_aws_profile(self) -> str:
        result = ""

        if (
            self.strategy == TrafficSwitchStrategy.TRAEFIK_PRIVATE
            or self.strategy == TrafficSwitchStrategy.TRAEFIK_PUBLIC
        ):
            record = self.get_record(self.source_set_id)

            if not record:
                raise ValueError(
                    f"Can not compute source_aws_profile for name={self.name} type={self.type} set_id={self.source_set_id}"
                )

            result = record.aws_profile

        return result

    @computed_field
    @property
    def source_ip_addresses(self) -> List[ipaddress.IPv4Address]:
        addrs = infra_lib.get_ip_list(self.name)
        ip_addresses = [ipaddress.ip_address(addr) for addr in addrs]

        if not all(
            [ip.is_private == ip_addresses[0].is_private for ip in ip_addresses]
        ):
            raise ValueError(
                f"Inconsistent source ip addresses {addrs}. All source ip addresses must be either private or global"
            )

        return ip_addresses

    @computed_field
    @property
    def source_zone_id(self) -> str:
        result = ""

        if (
            self.strategy == TrafficSwitchStrategy.TRAEFIK_PRIVATE
            or self.strategy == TrafficSwitchStrategy.TRAEFIK_PUBLIC
        ):
            record = self.get_record(self.source_set_id)

            if not record:
                raise ValueError(
                    f"Can not compute source_zone_id for name={self.name} type={self.type} set_id={self.source_set_id}"
                )

            result = record.zone_id

        return result

    @computed_field
    @property
    def target_aws_profile(self) -> str:
        result = ""

        if (
            self.strategy == TrafficSwitchStrategy.TRAEFIK_PRIVATE
            or self.strategy == TrafficSwitchStrategy.TRAEFIK_PUBLIC
        ):
            record = self.get_record(self.target_set_id)

            if not record:
                raise ValueError(
                    f"Can not compute target_aws_profile for name={self.name} type={self.type} set_id={self.target_set_id}"
                )

            result = record.aws_profile

        return result

    @computed_field
    @property
    def target_ip_addresses(self) -> List[ipaddress.IPv4Address]:
        if (
            self.strategy == TrafficSwitchStrategy.TRAEFIK_PRIVATE
            or self.strategy == TrafficSwitchStrategy.TRAEFIK_PUBLIC
        ):
            target_ip_addresses = []
            target_record = self.get_record(self.target_set_id)

            if self.type == "CNAME":
                values = []

                if target_record.record_set.get("AliasTarget") is not None:
                    values.append(target_record.record_set["AliasTarget"]["DNSName"])
                else:
                    for r in target_record.record_set["ResourceRecords"]:
                        values.append(r["Value"])

                for v in values:
                    ip_addresses = infra_lib.get_ip_list(v)

                    if len(ip_addresses) == 0:
                        raise ValueError(
                            f"Target {v} is not resolvable to any ip addressess"
                        )

                    for ip in ip_addresses:
                        target_ip_addresses.append(ipaddress.ip_address(ip))
            elif self.type == "A":
                values = []

                if target_record.record_set.get("AliasTarget") is not None:
                    ip_addresses = infra_lib.get_ip_list(
                        target_record.record_set["AliasTarget"]["DNSName"]
                    )

                    for ip in ip_addresses:
                        target_ip_addresses.append(ipaddress.ip_address(ip))
                else:
                    for r in target_record.record_set["ResourceRecords"]:
                        target_ip_addresses.append(ipaddress.ip_address(r["Value"]))

            ip_addresses = [ipaddress.ip_address(addr) for addr in target_ip_addresses]

            if not all(
                [ip.is_private == ip_addresses[0].is_private for ip in ip_addresses]
            ):
                raise ValueError(
                    f"Inconsistent target ip addresses {target_ip_addresses}. All target ip addresses must be either private or global"
                )

            return ip_addresses
        else:
            return []

    @computed_field
    @property
    def target_zone_id(self) -> str:
        result = ""

        if (
            self.strategy == TrafficSwitchStrategy.TRAEFIK_PRIVATE
            or self.strategy == TrafficSwitchStrategy.TRAEFIK_PUBLIC
        ):
            record = self.get_record(self.target_set_id)

            if not record:
                raise ValueError(
                    f"Can not compute target_zone_id for name={self.name} type={self.type} set_id={self.target_set_id}"
                )

            result = record.zone_id

        return result

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str):
        addrs = infra_lib.get_ip_list(v)

        if len(addrs) == 0:
            raise ValueError(f"Source {v} is not resolvable to any ip addressess")

        return v

    @field_validator("progression")
    @classmethod
    def validate_progression(cls, v: List[TrafficSwitchProgression]):
        valid = True
        invalid_index = []
        length = len(v)

        # Subsequent progression weight must be bigger than any of previous progression weight
        for i in range(1, length):
            for j in range(0, i):
                if v[j].weight >= v[i].weight:
                    valid = False
                    invalid_index.append(i)
                    break

        if not valid:
            raise ValueError(
                f"Invalid progression weight since they are less than previous progression on the following index {invalid_index}"
            )

        return v

    @model_validator(mode="after")
    def validate_source_target(self) -> Self:
        if self.source_aws_profile != self.target_aws_profile:
            raise ValueError(
                f"Both source_aws_profile and target_aws_profile must be the same. Got {self.source_aws_profile} and {self.target_aws_profile} for source and target aws profile respectively"
            )

        if self.source_zone_id != self.target_zone_id:
            raise ValueError(
                f"Both source_zone_id and target_zone_id must be the same. Got {self.source_aws_profile} and {self.target_aws_profile} for source and target zone id respectively"
            )

        if self.validate_terminal_progression:
            progression_length = len(self.progression)
            last_weight = self.progression[progression_length - 1].weight

            # The last progression weight must be 100
            if last_weight != 100:
                raise ValueError(
                    f"The last progression weight must be 100. Get {last_weight}"
                )

        if (
            self.strategy == TrafficSwitchStrategy.TRAEFIK_PRIVATE
            or self.strategy == TrafficSwitchStrategy.TRAEFIK_PUBLIC
        ):
            source_record = self.get_record(self.source_set_id)
            source_weight = source_record.record_set["Weight"]
            if (
                source_weight != 100
                and self.validate_terminal_progression
                and not self.rollback
            ):
                raise ValueError(
                    f"Initial weight of source record must be 100 for traffic switching. Got {source_weight}"
                )

            target_record = self.get_record(self.target_set_id)
            target_weight = target_record.record_set["Weight"]
            if (
                target_weight != 0
                and self.validate_terminal_progression
                and not self.rollback
            ):
                raise ValueError(
                    f"Initial weight of target record must be 0 for traffic switching. Got {target_weight}"
                )

            source_private = self.source_ip_addresses[0].is_private
            target_private = self.target_ip_addresses[0].is_private
            if source_private != target_private:
                raise ValueError(
                    f"Both source and target ip address must be private source_private={source_private} target_private={target_private}"
                )

            source_global = self.source_ip_addresses[0].is_global
            target_global = self.target_ip_addresses[0].is_global
            if source_global != target_global:
                raise ValueError(
                    f"Both source and target ip address must be global source_global={source_global} target_global={target_global}"
                )

        if self.strategy == TrafficSwitchStrategy.FERRY_BOAT and not self.rollback:
            if len(self.cf_token) <= 0:
                raise ValueError("cf_token must be set and can not be left empty")

            if len(self.cf_zone) <= 0:
                raise ValueError("cf_zone must be set and can not be left empty")

            if len(self.cf_zone_id) <= 0:
                raise ValueError("cf_zone_id must be set and can not be left empty")

            addrs = infra_lib.get_ip_list(self.source_cf_origin)

            if len(addrs) == 0:
                raise ValueError(
                    "Field source_cf_origin is not resolvable to any ip addressess"
                )

            if not infra_lib.is_aws_ip(addrs):
                raise ValueError(
                    "Field source_cf_origin is expected to resolve to AWS IP addresses"
                )

            addrs = infra_lib.get_ip_list(self.target_cf_origin)

            if len(addrs) == 0:
                raise ValueError(
                    "Field target_cf_origin is not resolvable to any ip addressess"
                )

            if not infra_lib.is_aws_ip(addrs):
                raise ValueError(
                    "Field target_cf_origin is expected to resolve to AWS IP addresses"
                )

        return self

    def to_string(self) -> str:
        if self.strategy == TrafficSwitchStrategy.FERRY_BOAT:
            return (
                f"name={self.name} type={self.type} strategy={self.strategy} "
                f"num_iteration={self.num_iteration} total_duration={self.total_duration} rollback={self.rollback}"
            )
        else:
            return (
                f"name={self.name} type={self.type} strategy={self.strategy} "
                f"source_set_id={self.source_set_id} source_aws_profile={self.source_aws_profile} source_zone_id={self.source_zone_id} "
                f"target_set_id={self.target_set_id} target_aws_profile={self.target_aws_profile} target_zone_id={self.target_zone_id} "
                f"num_iteration={self.num_iteration} total_duration={self.total_duration} rollback={self.rollback}"
            )

    def to_compact_string(self) -> str:
        if self.strategy == TrafficSwitchStrategy.FERRY_BOAT:
            return f"name={self.name}"
        else:
            return f"name={self.name} type={self.type} source_set_id={self.source_set_id} target_set_id={self.target_set_id}"

    def get_record(self, set_id: str) -> tuple | None:
        records = infra_r53.get_record_set(self.name)

        for record in records:
            record_set = record.record_set
            if (
                record_set["Type"] == self.type
                and record_set["SetIdentifier"] == set_id
            ):
                return record

        return None
