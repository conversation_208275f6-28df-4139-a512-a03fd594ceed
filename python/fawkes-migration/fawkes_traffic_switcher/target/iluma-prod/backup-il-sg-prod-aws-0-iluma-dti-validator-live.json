{"apiVersion": "v1", "kind": "List", "items": [{"metadata": {"annotations": {"deployment.kubernetes.io/revision": "1", "helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"Deployment\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-dti-validator-live-dti-validator-worker-worker\",\"argocd-il-sg-prod-aws-0.il.tidnex.com\":\"iluma-dti-validator-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-dti-validator-live-dti-validator-worker-worker\",\"namespace\":\"iluma-live\"},\"spec\":{\"replicas\":1,\"revisionHistoryLimit\":10,\"selector\":{\"matchLabels\":{\"app\":\"iluma-dti-validator-live-dti-validator-worker-worker\",\"environment\":\"production\",\"mode\":\"live\"}},\"strategy\":{\"rollingUpdate\":{\"maxSurge\":2,\"maxUnavailable\":0},\"type\":\"RollingUpdate\"},\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-dti-validator-live-dti-validator-worker-worker\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-dti-validator-live-dti-validator-worker-worker\"},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}},\"podAntiAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-dti-validator-live-dti-validator-worker-worker\"]}]},\"topologyKey\":\"kubernetes.io/hostname\"},\"weight\":50},{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-dti-validator-live-dti-validator-worker-worker\"]}]},\"topologyKey\":\"failure-domain.beta.kubernetes.io/zone\"},\"weight\":50}]}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-dti-validator\",\"--\",\"npm\",\"run\",\"start-consumer\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-dti-validator-wrk\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-dti-validator-wrk\"},{\"name\":\"DD_TRACE_ENABLED\",\"value\":\"true\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-dti-validator-wrk\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-dti-validator-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-dti-validator:2dd0d7d-24.05.15-09.01-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-dti-validator-live-dti-validator-worker-worker\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-dti-validator-live\",\"terminationGracePeriodSeconds\":62,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2024-07-31T03:26:38+00:00", "generation": 1, "labels": {"app": "iluma-dti-validator-live-dti-validator-worker-worker", "argocd-il-sg-prod-aws-0.il.tidnex.com": "iluma-dti-validator-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {".": {}, "f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {".": {}, "f:app": {}, "f:argocd-il-sg-prod-aws-0.il.tidnex.com": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:progressDeadlineSeconds": {}, "f:replicas": {}, "f:revisionHistoryLimit": {}, "f:selector": {}, "f:strategy": {"f:rollingUpdate": {".": {}, "f:maxSurge": {}, "f:maxUnavailable": {}}, "f:type": {}}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:helm.sh/chart": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}, "f:name": {}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:preferredDuringSchedulingIgnoredDuringExecution": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}, "f:podAntiAffinity": {".": {}, "f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-dti-validator-live-dti-validator-worker-worker\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_TRACE_ENABLED\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:image": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:terminationGracePeriodSeconds": {}, "f:tolerations": {}}}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-07-31T03:26:38+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:deployment.kubernetes.io/revision": {}}}, "f:status": {"f:conditions": {".": {}, "k:{\"type\":\"Available\"}": {".": {}, "f:lastTransitionTime": {}, "f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}, "f:status": {}, "f:type": {}}, "k:{\"type\":\"Progressing\"}": {".": {}, "f:lastTransitionTime": {}, "f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}, "f:status": {}, "f:type": {}}}, "f:observedGeneration": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-07-31T03:26:38+00:00"}], "name": "iluma-dti-validator-live-dti-validator-worker-worker", "namespace": "iluma-live", "resourceVersion": "*********", "uid": "acec981a-fcc0-47f5-ac55-e8a9e4fdadf2"}, "spec": {"progressDeadlineSeconds": 600, "replicas": 0, "revisionHistoryLimit": 10, "selector": {"matchLabels": {"app": "iluma-dti-validator-live-dti-validator-worker-worker", "environment": "production", "mode": "live"}}, "strategy": {"rollingUpdate": {"maxSurge": 2, "maxUnavailable": 0}, "type": "RollingUpdate"}, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-dti-validator-live-dti-validator-worker-worker", "environment": "production", "mode": "live"}, "name": "iluma-dti-validator-live-dti-validator-worker-worker"}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}, "podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-dti-validator-live-dti-validator-worker-worker"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 50}, {"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-dti-validator-live-dti-validator-worker-worker"]}]}, "topologyKey": "failure-domain.beta.kubernetes.io/zone"}, "weight": 50}]}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-dti-validator", "--", "npm", "run", "start-consumer"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-dti-validator-wrk"}, {"name": "DD_SERVICE", "value": "iluma-dti-validator-wrk"}, {"name": "DD_TRACE_ENABLED", "value": "true"}, {"name": "SERVICE_NAME", "value": "iluma-dti-validator-wrk"}], "envFrom": [{"configMapRef": {"name": "iluma-dti-validator-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-dti-validator:2dd0d7d-24.05.15-09.01-prod-live", "imagePullPolicy": "Always", "name": "iluma-dti-validator-live-dti-validator-worker-worker", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "Always", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-dti-validator-live", "serviceAccountName": "iluma-dti-validator-live", "terminationGracePeriodSeconds": 62, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}, "status": {"conditions": [{"lastTransitionTime": "2024-07-31T03:26:38+00:00", "lastUpdateTime": "2024-07-31T03:26:38+00:00", "message": "Deployment has minimum availability.", "reason": "MinimumReplicasAvailable", "status": "True", "type": "Available"}, {"lastTransitionTime": "2024-07-31T03:26:38+00:00", "lastUpdateTime": "2024-07-31T03:26:38+00:00", "message": "ReplicaSet \"iluma-dti-validator-live-dti-validator-worker-worker-6bc7759cdd\" has successfully progressed.", "reason": "NewReplicaSetAvailable", "status": "True", "type": "Progressing"}], "observedGeneration": 1}, "apiVersion": "apps/v1", "kind": "Deployment"}]}