{"apiVersion": "v1", "kind": "List", "items": [{"metadata": {"annotations": {"deployment.kubernetes.io/revision": "2", "helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"Deployment\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-identity-card-processor-live-wrk-worker\",\"argocd-il-sg-prod-aws-0.il.tidnex.com\":\"iluma-identity-card-processor-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-identity-card-processor-live-wrk-worker\",\"namespace\":\"iluma-live\"},\"spec\":{\"replicas\":1,\"revisionHistoryLimit\":10,\"selector\":{\"matchLabels\":{\"app\":\"iluma-identity-card-processor-live-wrk-worker\",\"environment\":\"production\",\"mode\":\"live\"}},\"strategy\":{\"rollingUpdate\":{\"maxSurge\":2,\"maxUnavailable\":0},\"type\":\"RollingUpdate\"},\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-identity-card-processor-live-wrk-worker\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-identity-card-processor-live-wrk-worker\"},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}},\"podAntiAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-identity-card-processor-live-wrk-worker\"]}]},\"topologyKey\":\"kubernetes.io/hostname\"},\"weight\":50},{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-identity-card-processor-live-wrk-worker\"]}]},\"topologyKey\":\"failure-domain.beta.kubernetes.io/zone\"},\"weight\":50}]}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-identity-card-processor\",\"--\",\"npm\",\"run\",\"start-consumer\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-identity-card-processor-wrk\"},{\"name\":\"DD_AGENT_PORT\",\"value\":\"8126\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-identity-card-processor-wrk\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-identity-card-processor-wrk\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-identity-card-processor-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-identity-card-processor:707b94b-24.07.15-03.57-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-identity-card-processor-live-wrk-worker\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-identity-card-processor-live\",\"terminationGracePeriodSeconds\":62,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2024-07-31T03:25:09+00:00", "generation": 2, "labels": {"app": "iluma-identity-card-processor-live-wrk-worker", "argocd-il-sg-prod-aws-0.il.tidnex.com": "iluma-identity-card-processor-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {".": {}, "f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {".": {}, "f:app": {}, "f:argocd-il-sg-prod-aws-0.il.tidnex.com": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:progressDeadlineSeconds": {}, "f:replicas": {}, "f:revisionHistoryLimit": {}, "f:selector": {}, "f:strategy": {"f:rollingUpdate": {".": {}, "f:maxSurge": {}, "f:maxUnavailable": {}}, "f:type": {}}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:helm.sh/chart": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}, "f:name": {}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:preferredDuringSchedulingIgnoredDuringExecution": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}, "f:podAntiAffinity": {".": {}, "f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-identity-card-processor-live-wrk-worker\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_AGENT_PORT\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:image": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:terminationGracePeriodSeconds": {}, "f:tolerations": {}}}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-08-12T02:21:24+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:deployment.kubernetes.io/revision": {}}}, "f:status": {"f:conditions": {".": {}, "k:{\"type\":\"Available\"}": {".": {}, "f:lastTransitionTime": {}, "f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}, "f:status": {}, "f:type": {}}, "k:{\"type\":\"Progressing\"}": {".": {}, "f:lastTransitionTime": {}, "f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}, "f:status": {}, "f:type": {}}}, "f:observedGeneration": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-08-12T02:21:24+00:00"}], "name": "iluma-identity-card-processor-live-wrk-worker", "namespace": "iluma-live", "resourceVersion": "*********", "uid": "c645b122-484c-46f5-b6c1-d391db9e9f7f"}, "spec": {"progressDeadlineSeconds": 600, "replicas": 0, "revisionHistoryLimit": 10, "selector": {"matchLabels": {"app": "iluma-identity-card-processor-live-wrk-worker", "environment": "production", "mode": "live"}}, "strategy": {"rollingUpdate": {"maxSurge": 2, "maxUnavailable": 0}, "type": "RollingUpdate"}, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-identity-card-processor-live-wrk-worker", "environment": "production", "mode": "live"}, "name": "iluma-identity-card-processor-live-wrk-worker"}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}, "podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-identity-card-processor-live-wrk-worker"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 50}, {"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-identity-card-processor-live-wrk-worker"]}]}, "topologyKey": "failure-domain.beta.kubernetes.io/zone"}, "weight": 50}]}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-identity-card-processor", "--", "npm", "run", "start-consumer"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-identity-card-processor-wrk"}, {"name": "DD_AGENT_PORT", "value": "8126"}, {"name": "DD_SERVICE", "value": "iluma-identity-card-processor-wrk"}, {"name": "SERVICE_NAME", "value": "iluma-identity-card-processor-wrk"}], "envFrom": [{"configMapRef": {"name": "iluma-identity-card-processor-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-identity-card-processor:707b94b-24.07.15-03.57-prod-live", "imagePullPolicy": "Always", "name": "iluma-identity-card-processor-live-wrk-worker", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "Always", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-identity-card-processor-live", "serviceAccountName": "iluma-identity-card-processor-live", "terminationGracePeriodSeconds": 62, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}, "status": {"conditions": [{"lastTransitionTime": "2024-07-31T03:25:09+00:00", "lastUpdateTime": "2024-07-31T03:25:09+00:00", "message": "Deployment has minimum availability.", "reason": "MinimumReplicasAvailable", "status": "True", "type": "Available"}, {"lastTransitionTime": "2024-07-31T03:25:09+00:00", "lastUpdateTime": "2024-08-12T02:21:24+00:00", "message": "ReplicaSet \"iluma-identity-card-processor-live-wrk-worker-597f759fc5\" has successfully progressed.", "reason": "NewReplicaSetAvailable", "status": "True", "type": "Progressing"}], "observedGeneration": 2}, "apiVersion": "apps/v1", "kind": "Deployment"}, {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"batch/v1\",\"kind\":\"CronJob\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-identity-card-processor-live-pndg-pssprt-cron\",\"argocd-il-sg-prod-aws-0.il.tidnex.com\":\"iluma-identity-card-processor-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-identity-card-processor-live-pndg-pssprt-cron\",\"namespace\":\"iluma-live\"},\"spec\":{\"concurrencyPolicy\":\"Forbid\",\"failedJobsHistoryLimit\":1,\"jobTemplate\":{\"spec\":{\"activeDeadlineSeconds\":300,\"backoffLimit\":6,\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-identity-card-processor-live-pndg-pssprt-cron\",\"environment\":\"production\",\"mode\":\"live\"}},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-identity-card-processor\",\"--\",\"node\",\"dist/crons/monitorPendingPassportOCR.js\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-identity-card-processor-cron\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-identity-card-processor-cron\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-identity-card-processor-cron\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-identity-card-processor-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-identity-card-processor:707b94b-24.07.15-03.57-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-identity-card-processor-live-pndg-pssprt-cron\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"restartPolicy\":\"OnFailure\",\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-identity-card-processor-live\",\"terminationGracePeriodSeconds\":120,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}},\"schedule\":\"*/5 * * * *\",\"startingDeadlineSeconds\":300,\"successfulJobsHistoryLimit\":3,\"suspend\":false}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2024-07-31T03:25:09+00:00", "generation": 2, "labels": {"app": "iluma-identity-card-processor-live-pndg-pssprt-cron", "argocd-il-sg-prod-aws-0.il.tidnex.com": "iluma-identity-card-processor-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {".": {}, "f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {".": {}, "f:app": {}, "f:argocd-il-sg-prod-aws-0.il.tidnex.com": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:concurrencyPolicy": {}, "f:failedJobsHistoryLimit": {}, "f:jobTemplate": {"f:spec": {"f:activeDeadlineSeconds": {}, "f:backoffLimit": {}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:helm.sh/chart": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:preferredDuringSchedulingIgnoredDuringExecution": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-identity-card-processor-live-pndg-pssprt-cron\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:image": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:terminationGracePeriodSeconds": {}, "f:tolerations": {}}}}}, "f:schedule": {}, "f:startingDeadlineSeconds": {}, "f:successfulJobsHistoryLimit": {}, "f:suspend": {}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-08-12T02:21:24+00:00"}], "name": "iluma-identity-card-processor-live-pndg-pssprt-cron", "namespace": "iluma-live", "resourceVersion": "*********", "uid": "b154d664-58a0-44fa-83f3-f05103b5f00d"}, "spec": {"concurrencyPolicy": "Forbid", "failedJobsHistoryLimit": 1, "jobTemplate": {"metadata": {}, "spec": {"activeDeadlineSeconds": 300, "backoffLimit": 6, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-identity-card-processor-live-pndg-pssprt-cron", "environment": "production", "mode": "live"}}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-identity-card-processor", "--", "node", "dist/crons/monitorPendingPassportOCR.js"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-identity-card-processor-cron"}, {"name": "DD_SERVICE", "value": "iluma-identity-card-processor-cron"}, {"name": "SERVICE_NAME", "value": "iluma-identity-card-processor-cron"}], "envFrom": [{"configMapRef": {"name": "iluma-identity-card-processor-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-identity-card-processor:707b94b-24.07.15-03.57-prod-live", "imagePullPolicy": "Always", "name": "iluma-identity-card-processor-live-pndg-pssprt-cron", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "OnFailure", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-identity-card-processor-live", "serviceAccountName": "iluma-identity-card-processor-live", "terminationGracePeriodSeconds": 120, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}}, "schedule": "*/5 * * * *", "startingDeadlineSeconds": 300, "successfulJobsHistoryLimit": 3, "suspend": true}, "status": {}, "apiVersion": "batch/v1", "kind": "<PERSON><PERSON><PERSON><PERSON>"}]}