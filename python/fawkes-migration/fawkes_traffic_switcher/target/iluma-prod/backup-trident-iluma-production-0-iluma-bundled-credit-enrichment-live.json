{"apiVersion": "v1", "kind": "List", "items": [{"metadata": {"annotations": {"deployment.kubernetes.io/revision": "13", "helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"Deployment\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-bundled-credit-enrichment-live-batch-worker\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-bundled-credit-enrichment-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-bundled-credit-enrichment-live-batch-worker\",\"namespace\":\"iluma-live\"},\"spec\":{\"replicas\":1,\"revisionHistoryLimit\":10,\"selector\":{\"matchLabels\":{\"app\":\"iluma-bundled-credit-enrichment-live-batch-worker\",\"environment\":\"production\",\"mode\":\"live\"}},\"strategy\":{\"rollingUpdate\":{\"maxSurge\":2,\"maxUnavailable\":0},\"type\":\"RollingUpdate\"},\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-bundled-credit-enrichment-live-batch-worker\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-bundled-credit-enrichment-live-batch-worker\"},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}},\"podAntiAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-bundled-credit-enrichment-live-batch-worker\"]}]},\"topologyKey\":\"kubernetes.io/hostname\"},\"weight\":50},{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-bundled-credit-enrichment-live-batch-worker\"]}]},\"topologyKey\":\"failure-domain.beta.kubernetes.io/zone\"},\"weight\":50}]}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-bundled-credit-enrichment\",\"--\",\"npm\",\"run\",\"start-consumer\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-bundled-credit-enrichment-batch-wrk\"},{\"name\":\"BATCH\",\"value\":\"true\"},{\"name\":\"DD_AGENT_PORT\",\"value\":\"8126\"},{\"name\":\"DD_ENV\",\"value\":\"production-development\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-bundled-credit-enrichment-batch-wrk\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-bundled-credit-enrichment-batch-wrk\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-bundled-credit-enrichment-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-bundled-credit-enrichment:b9ae16c-24.05.15-08.18-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-bundled-credit-enrichment-live-batch-worker\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-bundled-credit-enrichment-live\",\"terminationGracePeriodSeconds\":62,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2022-02-22T09:18:36+00:00", "generation": 15, "labels": {"app": "iluma-bundled-credit-enrichment-live-batch-worker", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-bundled-credit-enrichment-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:progressDeadlineSeconds": {}, "f:replicas": {}, "f:selector": {}, "f:strategy": {"f:rollingUpdate": {".": {}, "f:maxSurge": {}, "f:maxUnavailable": {}}, "f:type": {}}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}, "f:name": {}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}, "f:podAntiAffinity": {".": {}, "f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-bundled-credit-enrichment-live-batch-worker\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"BATCH\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_AGENT_PORT\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_ENV\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "manager": "kubectl", "operation": "Update", "time": "2022-02-22T09:18:36+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:spec": {"f:template": {"f:spec": {"f:containers": {"k:{\"name\":\"iluma-bundled-credit-enrichment-live-batch-worker\"}": {"f:env": {"k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}}}}}}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2022-07-28T09:35:38+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:conditions": {".": {}, "k:{\"type\":\"Available\"}": {".": {}, "f:type": {}}, "k:{\"type\":\"Progressing\"}": {".": {}, "f:lastTransitionTime": {}, "f:status": {}, "f:type": {}}}}}, "manager": "kube-controller-manager", "operation": "Update", "time": "2023-01-20T08:07:25+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:revisionHistoryLimit": {}, "f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-bundled-credit-enrichment-live-batch-worker\"}": {"f:image": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-23T03:20:46+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:deployment.kubernetes.io/revision": {}}}, "f:status": {"f:availableReplicas": {}, "f:conditions": {"k:{\"type\":\"Available\"}": {"f:lastTransitionTime": {}, "f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}, "f:status": {}}, "k:{\"type\":\"Progressing\"}": {"f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}}}, "f:observedGeneration": {}, "f:readyReplicas": {}, "f:replicas": {}, "f:updatedReplicas": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-07-19T03:31:00+00:00"}], "name": "iluma-bundled-credit-enrichment-live-batch-worker", "namespace": "iluma-live", "resourceVersion": "817381419", "uid": "170c90e0-3634-49a7-958a-9f779f4404f7"}, "spec": {"progressDeadlineSeconds": 600, "replicas": 1, "revisionHistoryLimit": 10, "selector": {"matchLabels": {"app": "iluma-bundled-credit-enrichment-live-batch-worker", "environment": "production", "mode": "live"}}, "strategy": {"rollingUpdate": {"maxSurge": 2, "maxUnavailable": 0}, "type": "RollingUpdate"}, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-bundled-credit-enrichment-live-batch-worker", "environment": "production", "mode": "live"}, "name": "iluma-bundled-credit-enrichment-live-batch-worker"}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}, "podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-bundled-credit-enrichment-live-batch-worker"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 50}, {"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-bundled-credit-enrichment-live-batch-worker"]}]}, "topologyKey": "failure-domain.beta.kubernetes.io/zone"}, "weight": 50}]}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-bundled-credit-enrichment", "--", "npm", "run", "start-consumer"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-bundled-credit-enrichment-batch-wrk"}, {"name": "BATCH", "value": "true"}, {"name": "DD_AGENT_PORT", "value": "8126"}, {"name": "DD_ENV", "value": "production-development"}, {"name": "DD_SERVICE", "value": "iluma-bundled-credit-enrichment-batch-wrk"}, {"name": "SERVICE_NAME", "value": "iluma-bundled-credit-enrichment-batch-wrk"}], "envFrom": [{"configMapRef": {"name": "iluma-bundled-credit-enrichment-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-bundled-credit-enrichment:b9ae16c-24.05.15-08.18-prod-live", "imagePullPolicy": "Always", "name": "iluma-bundled-credit-enrichment-live-batch-worker", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "Always", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-bundled-credit-enrichment-live", "serviceAccountName": "iluma-bundled-credit-enrichment-live", "terminationGracePeriodSeconds": 62, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}, "status": {"availableReplicas": 1, "conditions": [{"lastTransitionTime": "2022-02-22T09:18:36+00:00", "lastUpdateTime": "2024-05-23T03:21:05+00:00", "message": "ReplicaSet \"iluma-bundled-credit-enrichment-live-batch-worker-6688fc68b8\" has successfully progressed.", "reason": "NewReplicaSetAvailable", "status": "True", "type": "Progressing"}, {"lastTransitionTime": "2024-07-19T03:31:00+00:00", "lastUpdateTime": "2024-07-19T03:31:00+00:00", "message": "Deployment has minimum availability.", "reason": "MinimumReplicasAvailable", "status": "True", "type": "Available"}], "observedGeneration": 15, "readyReplicas": 1, "replicas": 1, "updatedReplicas": 1}, "apiVersion": "apps/v1", "kind": "Deployment"}, {"metadata": {"annotations": {"deployment.kubernetes.io/revision": "33", "helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"Deployment\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-bundled-credit-enrichment-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\",\"namespace\":\"iluma-live\"},\"spec\":{\"replicas\":1,\"revisionHistoryLimit\":10,\"selector\":{\"matchLabels\":{\"app\":\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\",\"environment\":\"production\",\"mode\":\"live\"}},\"strategy\":{\"rollingUpdate\":{\"maxSurge\":2,\"maxUnavailable\":0},\"type\":\"RollingUpdate\"},\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\"},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}},\"podAntiAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\"]}]},\"topologyKey\":\"kubernetes.io/hostname\"},\"weight\":50},{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\"]}]},\"topologyKey\":\"failure-domain.beta.kubernetes.io/zone\"},\"weight\":50}]}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-bundled-credit-enrichment\",\"--\",\"npm\",\"run\",\"start-consumer\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-bundled-credit-enrichment-wrk\"},{\"name\":\"DD_AGENT_PORT\",\"value\":\"8126\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-bundled-credit-enrichment-wrk\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-bundled-credit-enrichment-wrk\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-bundled-credit-enrichment-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-bundled-credit-enrichment:b9ae16c-24.05.15-08.18-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-bundled-credit-enrichment-live\",\"terminationGracePeriodSeconds\":62,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2021-11-03T03:41:11+00:00", "generation": 37, "labels": {"app": "iluma-bundled-credit-enrichment-live-bdl-crdt-worker", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-bundled-credit-enrichment-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:progressDeadlineSeconds": {}, "f:selector": {}, "f:strategy": {"f:rollingUpdate": {".": {}, "f:maxSurge": {}, "f:maxUnavailable": {}}, "f:type": {}}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}, "f:name": {}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}, "f:podAntiAffinity": {".": {}, "f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_AGENT_PORT\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "manager": "kubectl", "operation": "Update", "time": "2021-11-03T03:41:11+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:spec": {"f:template": {"f:spec": {"f:containers": {"k:{\"name\":\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\"}": {"f:env": {"k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}}}}}}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2022-07-28T09:35:37+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:conditions": {".": {}, "k:{\"type\":\"Available\"}": {".": {}, "f:type": {}}, "k:{\"type\":\"Progressing\"}": {".": {}, "f:lastTransitionTime": {}, "f:status": {}, "f:type": {}}}}}, "manager": "kube-controller-manager", "operation": "Update", "time": "2023-01-20T08:07:22+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:replicas": {}, "f:revisionHistoryLimit": {}, "f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-bundled-credit-enrichment-live-bdl-crdt-worker\"}": {"f:image": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-23T03:20:46+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:deployment.kubernetes.io/revision": {}}}, "f:status": {"f:availableReplicas": {}, "f:conditions": {"k:{\"type\":\"Available\"}": {"f:lastTransitionTime": {}, "f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}, "f:status": {}}, "k:{\"type\":\"Progressing\"}": {"f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}}}, "f:observedGeneration": {}, "f:readyReplicas": {}, "f:replicas": {}, "f:updatedReplicas": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-07-19T03:31:16+00:00"}], "name": "iluma-bundled-credit-enrichment-live-bdl-crdt-worker", "namespace": "iluma-live", "resourceVersion": "817381730", "uid": "d1659cb2-1b8d-42bb-82a4-6133df48b046"}, "spec": {"progressDeadlineSeconds": 600, "replicas": 1, "revisionHistoryLimit": 10, "selector": {"matchLabels": {"app": "iluma-bundled-credit-enrichment-live-bdl-crdt-worker", "environment": "production", "mode": "live"}}, "strategy": {"rollingUpdate": {"maxSurge": 2, "maxUnavailable": 0}, "type": "RollingUpdate"}, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-bundled-credit-enrichment-live-bdl-crdt-worker", "environment": "production", "mode": "live"}, "name": "iluma-bundled-credit-enrichment-live-bdl-crdt-worker"}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}, "podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-bundled-credit-enrichment-live-bdl-crdt-worker"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 50}, {"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-bundled-credit-enrichment-live-bdl-crdt-worker"]}]}, "topologyKey": "failure-domain.beta.kubernetes.io/zone"}, "weight": 50}]}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-bundled-credit-enrichment", "--", "npm", "run", "start-consumer"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-bundled-credit-enrichment-wrk"}, {"name": "DD_AGENT_PORT", "value": "8126"}, {"name": "DD_SERVICE", "value": "iluma-bundled-credit-enrichment-wrk"}, {"name": "SERVICE_NAME", "value": "iluma-bundled-credit-enrichment-wrk"}], "envFrom": [{"configMapRef": {"name": "iluma-bundled-credit-enrichment-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-bundled-credit-enrichment:b9ae16c-24.05.15-08.18-prod-live", "imagePullPolicy": "Always", "name": "iluma-bundled-credit-enrichment-live-bdl-crdt-worker", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "Always", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-bundled-credit-enrichment-live", "serviceAccountName": "iluma-bundled-credit-enrichment-live", "terminationGracePeriodSeconds": 62, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}, "status": {"availableReplicas": 1, "conditions": [{"lastTransitionTime": "2021-11-03T03:41:11+00:00", "lastUpdateTime": "2024-05-23T03:21:05+00:00", "message": "ReplicaSet \"iluma-bundled-credit-enrichment-live-bdl-crdt-worker-7655558b6\" has successfully progressed.", "reason": "NewReplicaSetAvailable", "status": "True", "type": "Progressing"}, {"lastTransitionTime": "2024-07-19T03:31:16+00:00", "lastUpdateTime": "2024-07-19T03:31:16+00:00", "message": "Deployment has minimum availability.", "reason": "MinimumReplicasAvailable", "status": "True", "type": "Available"}], "observedGeneration": 37, "readyReplicas": 1, "replicas": 1, "updatedReplicas": 1}, "apiVersion": "apps/v1", "kind": "Deployment"}]}