{"apiVersion": "v1", "kind": "List", "items": [{"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"batch/v1\",\"kind\":\"CronJob\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-business-service-live-create-xendit-bid-cron\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-business-service-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-business-service-live-create-xendit-bid-cron\",\"namespace\":\"iluma-live\"},\"spec\":{\"concurrencyPolicy\":\"Forbid\",\"failedJobsHistoryLimit\":1,\"jobTemplate\":{\"spec\":{\"activeDeadlineSeconds\":300,\"backoffLimit\":6,\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-business-service-live-create-xendit-bid-cron\",\"environment\":\"production\",\"mode\":\"live\"}},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-business-service\",\"--\",\"node\",\"dist/scripts/create_xendit_bid.js\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-business-service-cron\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-business-service-cron\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-business-service-cron\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-business-service-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-business-service:f9663af-24.05.14-05.45-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-business-service-live-create-xendit-bid-cron\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"restartPolicy\":\"OnFailure\",\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-business-service-live\",\"terminationGracePeriodSeconds\":120,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}},\"schedule\":\"0 1 * * *\",\"startingDeadlineSeconds\":300,\"successfulJobsHistoryLimit\":3,\"suspend\":false}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2023-09-12T01:31:33+00:00", "generation": 9, "labels": {"app": "iluma-business-service-live-create-xendit-bid-cron", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-business-service-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:concurrencyPolicy": {}, "f:failedJobsHistoryLimit": {}, "f:jobTemplate": {"f:spec": {"f:activeDeadlineSeconds": {}, "f:backoffLimit": {}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-business-service-live-create-xendit-bid-cron\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "f:schedule": {}, "f:startingDeadlineSeconds": {}, "f:suspend": {}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2024-05-03T09:02:43+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:jobTemplate": {"f:spec": {"f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-business-service-live-create-xendit-bid-cron\"}": {"f:image": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "f:successfulJobsHistoryLimit": {}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-21T08:34:32+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:lastScheduleTime": {}, "f:lastSuccessfulTime": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-08-13T01:00:16+00:00"}], "name": "iluma-business-service-live-create-xendit-bid-cron", "namespace": "iluma-live", "resourceVersion": "840787043", "uid": "4da74ee8-c2bf-4e7a-b872-0386b9d7f72d"}, "spec": {"concurrencyPolicy": "Forbid", "failedJobsHistoryLimit": 1, "jobTemplate": {"metadata": {}, "spec": {"activeDeadlineSeconds": 300, "backoffLimit": 6, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-business-service-live-create-xendit-bid-cron", "environment": "production", "mode": "live"}}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-business-service", "--", "node", "dist/scripts/create_xendit_bid.js"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-business-service-cron"}, {"name": "DD_SERVICE", "value": "iluma-business-service-cron"}, {"name": "SERVICE_NAME", "value": "iluma-business-service-cron"}], "envFrom": [{"configMapRef": {"name": "iluma-business-service-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-business-service:f9663af-24.05.14-05.45-prod-live", "imagePullPolicy": "Always", "name": "iluma-business-service-live-create-xendit-bid-cron", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "OnFailure", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-business-service-live", "serviceAccountName": "iluma-business-service-live", "terminationGracePeriodSeconds": 120, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}}, "schedule": "0 1 * * *", "startingDeadlineSeconds": 300, "successfulJobsHistoryLimit": 3, "suspend": false}, "status": {"lastScheduleTime": "2024-08-13T01:00:00+00:00", "lastSuccessfulTime": "2024-08-13T01:00:16+00:00"}, "apiVersion": "batch/v1", "kind": "<PERSON><PERSON><PERSON><PERSON>"}]}