{"apiVersion": "v1", "kind": "List", "items": [{"metadata": {"annotations": {"deployment.kubernetes.io/revision": "43", "helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"Deployment\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-callback-service-live-consumer-worker\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-callback-service-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-callback-service-live-consumer-worker\",\"namespace\":\"iluma-live\"},\"spec\":{\"replicas\":3,\"revisionHistoryLimit\":10,\"selector\":{\"matchLabels\":{\"app\":\"iluma-callback-service-live-consumer-worker\",\"environment\":\"production\",\"mode\":\"live\"}},\"strategy\":{\"rollingUpdate\":{\"maxSurge\":2,\"maxUnavailable\":0},\"type\":\"RollingUpdate\"},\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-callback-service-live-consumer-worker\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-callback-service-live-consumer-worker\"},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}},\"podAntiAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-callback-service-live-consumer-worker\"]}]},\"topologyKey\":\"kubernetes.io/hostname\"},\"weight\":50},{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-callback-service-live-consumer-worker\"]}]},\"topologyKey\":\"failure-domain.beta.kubernetes.io/zone\"},\"weight\":50}]}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-callback-service\",\"--\",\"npm\",\"run\",\"start-consumer\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-callback-service-wrk\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-callback-service-wrk\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-callback-service-wrk\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-callback-service-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-callback-service:f5db1c9-24.05.14-09.37-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-callback-service-live-consumer-worker\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-callback-service-live\",\"terminationGracePeriodSeconds\":62,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2022-02-07T02:39:24+00:00", "generation": 45, "labels": {"app": "iluma-callback-service-live-consumer-worker", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-callback-service-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:spec": {"f:replicas": {}}}, "manager": "kubectl", "operation": "Update", "subresource": "scale"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:progressDeadlineSeconds": {}, "f:selector": {}, "f:strategy": {"f:rollingUpdate": {".": {}, "f:maxSurge": {}, "f:maxUnavailable": {}}, "f:type": {}}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}, "f:name": {}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}, "f:podAntiAffinity": {".": {}, "f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-callback-service-live-consumer-worker\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "manager": "kubectl", "operation": "Update", "time": "2022-02-07T02:39:24+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:conditions": {".": {}, "k:{\"type\":\"Available\"}": {".": {}, "f:type": {}}, "k:{\"type\":\"Progressing\"}": {".": {}, "f:lastTransitionTime": {}, "f:status": {}, "f:type": {}}}}}, "manager": "kube-controller-manager", "operation": "Update", "time": "2023-01-17T06:29:36+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:spec": {"f:template": {"f:spec": {"f:containers": {"k:{\"name\":\"iluma-callback-service-live-consumer-worker\"}": {"f:env": {"k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}}}}}}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2024-01-12T02:19:41+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:revisionHistoryLimit": {}, "f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-callback-service-live-consumer-worker\"}": {"f:image": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-21T08:34:10+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:deployment.kubernetes.io/revision": {}}}, "f:status": {"f:availableReplicas": {}, "f:conditions": {"k:{\"type\":\"Available\"}": {"f:lastTransitionTime": {}, "f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}, "f:status": {}}, "k:{\"type\":\"Progressing\"}": {"f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}}}, "f:observedGeneration": {}, "f:readyReplicas": {}, "f:replicas": {}, "f:updatedReplicas": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-07-19T03:31:01+00:00"}], "name": "iluma-callback-service-live-consumer-worker", "namespace": "iluma-live", "resourceVersion": "817381456", "uid": "9ae095f3-8fb4-46e3-a2e9-29f130f9452f"}, "spec": {"progressDeadlineSeconds": 600, "replicas": 3, "revisionHistoryLimit": 10, "selector": {"matchLabels": {"app": "iluma-callback-service-live-consumer-worker", "environment": "production", "mode": "live"}}, "strategy": {"rollingUpdate": {"maxSurge": 2, "maxUnavailable": 0}, "type": "RollingUpdate"}, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-callback-service-live-consumer-worker", "environment": "production", "mode": "live"}, "name": "iluma-callback-service-live-consumer-worker"}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}, "podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-callback-service-live-consumer-worker"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 50}, {"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-callback-service-live-consumer-worker"]}]}, "topologyKey": "failure-domain.beta.kubernetes.io/zone"}, "weight": 50}]}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-callback-service", "--", "npm", "run", "start-consumer"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-callback-service-wrk"}, {"name": "DD_SERVICE", "value": "iluma-callback-service-wrk"}, {"name": "SERVICE_NAME", "value": "iluma-callback-service-wrk"}], "envFrom": [{"configMapRef": {"name": "iluma-callback-service-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-callback-service:f5db1c9-24.05.14-09.37-prod-live", "imagePullPolicy": "Always", "name": "iluma-callback-service-live-consumer-worker", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "Always", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-callback-service-live", "serviceAccountName": "iluma-callback-service-live", "terminationGracePeriodSeconds": 62, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}, "status": {"availableReplicas": 3, "conditions": [{"lastTransitionTime": "2022-02-07T02:39:24+00:00", "lastUpdateTime": "2024-05-21T08:34:30+00:00", "message": "ReplicaSet \"iluma-callback-service-live-consumer-worker-7f5cc6b5b9\" has successfully progressed.", "reason": "NewReplicaSetAvailable", "status": "True", "type": "Progressing"}, {"lastTransitionTime": "2024-07-19T03:31:01+00:00", "lastUpdateTime": "2024-07-19T03:31:01+00:00", "message": "Deployment has minimum availability.", "reason": "MinimumReplicasAvailable", "status": "True", "type": "Available"}], "observedGeneration": 45, "readyReplicas": 3, "replicas": 3, "updatedReplicas": 3}, "apiVersion": "apps/v1", "kind": "Deployment"}, {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"batch/v1\",\"kind\":\"CronJob\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-callback-service-live-cburlnotset-cron\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-callback-service-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-callback-service-live-cburlnotset-cron\",\"namespace\":\"iluma-live\"},\"spec\":{\"concurrencyPolicy\":\"Forbid\",\"failedJobsHistoryLimit\":1,\"jobTemplate\":{\"spec\":{\"activeDeadlineSeconds\":300,\"backoffLimit\":6,\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-callback-service-live-cburlnotset-cron\",\"environment\":\"production\",\"mode\":\"live\"}},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-callback-service\",\"--\",\"node\",\"dist/scripts/get_callback_url_not_set_client.js\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-callback-service-cron\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-callback-service-cron\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-callback-service-cron\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-callback-service-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-callback-service:f5db1c9-24.05.14-09.37-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-callback-service-live-cburlnotset-cron\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"restartPolicy\":\"OnFailure\",\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-callback-service-live\",\"terminationGracePeriodSeconds\":120,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}},\"schedule\":\"0 2 * * *\",\"startingDeadlineSeconds\":300,\"successfulJobsHistoryLimit\":3,\"suspend\":false}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2024-01-11T06:39:59+00:00", "generation": 6, "labels": {"app": "iluma-callback-service-live-cburlnotset-cron", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-callback-service-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:concurrencyPolicy": {}, "f:failedJobsHistoryLimit": {}, "f:jobTemplate": {"f:spec": {"f:activeDeadlineSeconds": {}, "f:backoffLimit": {}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-callback-service-live-cburlnotset-cron\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "f:schedule": {}, "f:startingDeadlineSeconds": {}, "f:suspend": {}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2024-01-12T02:19:41+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:jobTemplate": {"f:spec": {"f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-callback-service-live-cburlnotset-cron\"}": {"f:image": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "f:successfulJobsHistoryLimit": {}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-21T08:34:10+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:lastScheduleTime": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-08-13T02:00:00+00:00"}], "name": "iluma-callback-service-live-cburlnotset-cron", "namespace": "iluma-live", "resourceVersion": "840827730", "uid": "35c05112-8e8a-4993-abf6-8a81965cc9fc"}, "spec": {"concurrencyPolicy": "Forbid", "failedJobsHistoryLimit": 1, "jobTemplate": {"metadata": {}, "spec": {"activeDeadlineSeconds": 300, "backoffLimit": 6, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-callback-service-live-cburlnotset-cron", "environment": "production", "mode": "live"}}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-callback-service", "--", "node", "dist/scripts/get_callback_url_not_set_client.js"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-callback-service-cron"}, {"name": "DD_SERVICE", "value": "iluma-callback-service-cron"}, {"name": "SERVICE_NAME", "value": "iluma-callback-service-cron"}], "envFrom": [{"configMapRef": {"name": "iluma-callback-service-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-callback-service:f5db1c9-24.05.14-09.37-prod-live", "imagePullPolicy": "Always", "name": "iluma-callback-service-live-cburlnotset-cron", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "OnFailure", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-callback-service-live", "serviceAccountName": "iluma-callback-service-live", "terminationGracePeriodSeconds": 120, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}}, "schedule": "0 2 * * *", "startingDeadlineSeconds": 300, "successfulJobsHistoryLimit": 3, "suspend": false}, "status": {"lastScheduleTime": "2024-08-13T02:00:00+00:00"}, "apiVersion": "batch/v1", "kind": "<PERSON><PERSON><PERSON><PERSON>"}]}