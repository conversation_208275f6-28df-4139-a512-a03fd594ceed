{"apiVersion": "v1", "kind": "List", "items": [{"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"batch/v1\",\"kind\":\"CronJob\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-cft-service-live-refresh-ec-list-cron\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-cft-service-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-cft-service-live-refresh-ec-list-cron\",\"namespace\":\"iluma-live\"},\"spec\":{\"concurrencyPolicy\":\"Forbid\",\"failedJobsHistoryLimit\":1,\"jobTemplate\":{\"spec\":{\"activeDeadlineSeconds\":500,\"backoffLimit\":6,\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-cft-service-live-refresh-ec-list-cron\",\"environment\":\"production\",\"mode\":\"live\"}},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-cft-service\",\"--\",\"python\",\"parser/parser_ec.py\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-cft-service-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-cft-service:f8f64ac-24.05.15-05.31-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-cft-service-live-refresh-ec-list-cron\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"restartPolicy\":\"OnFailure\",\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-cft-service-live\",\"terminationGracePeriodSeconds\":500,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}},\"schedule\":\"0 1 1,15 * *\",\"startingDeadlineSeconds\":300,\"successfulJobsHistoryLimit\":3,\"suspend\":false}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2022-08-25T11:54:01+00:00", "generation": 14, "labels": {"app": "iluma-cft-service-live-refresh-ec-list-cron", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-cft-service-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "batch/v1beta1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:concurrencyPolicy": {}, "f:failedJobsHistoryLimit": {}, "f:jobTemplate": {"f:spec": {"f:activeDeadlineSeconds": {}, "f:backoffLimit": {}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-cft-service-live-refresh-ec-list-cron\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}}, "f:envFrom": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "f:schedule": {}, "f:suspend": {}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2024-04-28T10:32:18+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:jobTemplate": {"f:spec": {"f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-cft-service-live-refresh-ec-list-cron\"}": {"f:image": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "f:startingDeadlineSeconds": {}, "f:successfulJobsHistoryLimit": {}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-21T08:34:24+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:lastScheduleTime": {}, "f:lastSuccessfulTime": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-08-01T01:00:36+00:00"}], "name": "iluma-cft-service-live-refresh-ec-list-cron", "namespace": "iluma-live", "resourceVersion": "829902608", "uid": "e5a62e4d-de0c-4682-8cc5-d70a92ad1b37"}, "spec": {"concurrencyPolicy": "Forbid", "failedJobsHistoryLimit": 1, "jobTemplate": {"metadata": {}, "spec": {"activeDeadlineSeconds": 500, "backoffLimit": 6, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-cft-service-live-refresh-ec-list-cron", "environment": "production", "mode": "live"}}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-cft-service", "--", "python", "parser/parser_ec.py"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}], "envFrom": [{"configMapRef": {"name": "iluma-cft-service-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-cft-service:f8f64ac-24.05.15-05.31-prod-live", "imagePullPolicy": "Always", "name": "iluma-cft-service-live-refresh-ec-list-cron", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "OnFailure", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-cft-service-live", "serviceAccountName": "iluma-cft-service-live", "terminationGracePeriodSeconds": 500, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}}, "schedule": "0 1 1,15 * *", "startingDeadlineSeconds": 300, "successfulJobsHistoryLimit": 3, "suspend": false}, "status": {"lastScheduleTime": "2024-08-01T01:00:00+00:00", "lastSuccessfulTime": "2024-08-01T01:00:36+00:00"}, "apiVersion": "batch/v1", "kind": "<PERSON><PERSON><PERSON><PERSON>"}, {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"batch/v1\",\"kind\":\"CronJob\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-cft-service-live-refresh-mas-list-cron\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-cft-service-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-cft-service-live-refresh-mas-list-cron\",\"namespace\":\"iluma-live\"},\"spec\":{\"concurrencyPolicy\":\"Forbid\",\"failedJobsHistoryLimit\":1,\"jobTemplate\":{\"spec\":{\"activeDeadlineSeconds\":500,\"backoffLimit\":6,\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-cft-service-live-refresh-mas-list-cron\",\"environment\":\"production\",\"mode\":\"live\"}},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-cft-service\",\"--\",\"python\",\"parser/parser_mas.py\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-cft-service-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-cft-service:f8f64ac-24.05.15-05.31-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-cft-service-live-refresh-mas-list-cron\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"restartPolicy\":\"OnFailure\",\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-cft-service-live\",\"terminationGracePeriodSeconds\":500,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}},\"schedule\":\"0 4 1,15 * *\",\"startingDeadlineSeconds\":300,\"successfulJobsHistoryLimit\":3,\"suspend\":false}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2022-09-15T09:37:29+00:00", "generation": 14, "labels": {"app": "iluma-cft-service-live-refresh-mas-list-cron", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-cft-service-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "batch/v1beta1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:concurrencyPolicy": {}, "f:failedJobsHistoryLimit": {}, "f:jobTemplate": {"f:spec": {"f:activeDeadlineSeconds": {}, "f:backoffLimit": {}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-cft-service-live-refresh-mas-list-cron\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}}, "f:envFrom": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "f:schedule": {}, "f:suspend": {}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2024-04-28T10:32:18+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:jobTemplate": {"f:spec": {"f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-cft-service-live-refresh-mas-list-cron\"}": {"f:image": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "f:startingDeadlineSeconds": {}, "f:successfulJobsHistoryLimit": {}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-21T08:34:24+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:lastScheduleTime": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-08-01T04:00:00+00:00"}], "name": "iluma-cft-service-live-refresh-mas-list-cron", "namespace": "iluma-live", "resourceVersion": "830028663", "uid": "d3c2d967-53f1-4a2c-888d-477f9f6f618c"}, "spec": {"concurrencyPolicy": "Forbid", "failedJobsHistoryLimit": 1, "jobTemplate": {"metadata": {}, "spec": {"activeDeadlineSeconds": 500, "backoffLimit": 6, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-cft-service-live-refresh-mas-list-cron", "environment": "production", "mode": "live"}}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-cft-service", "--", "python", "parser/parser_mas.py"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}], "envFrom": [{"configMapRef": {"name": "iluma-cft-service-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-cft-service:f8f64ac-24.05.15-05.31-prod-live", "imagePullPolicy": "Always", "name": "iluma-cft-service-live-refresh-mas-list-cron", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "OnFailure", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-cft-service-live", "serviceAccountName": "iluma-cft-service-live", "terminationGracePeriodSeconds": 500, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}}, "schedule": "0 4 1,15 * *", "startingDeadlineSeconds": 300, "successfulJobsHistoryLimit": 3, "suspend": false}, "status": {"lastScheduleTime": "2024-08-01T04:00:00+00:00"}, "apiVersion": "batch/v1", "kind": "<PERSON><PERSON><PERSON><PERSON>"}, {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"batch/v1\",\"kind\":\"CronJob\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-cft-service-live-refresh-ofac-list-cron\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-cft-service-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-cft-service-live-refresh-ofac-list-cron\",\"namespace\":\"iluma-live\"},\"spec\":{\"concurrencyPolicy\":\"Forbid\",\"failedJobsHistoryLimit\":1,\"jobTemplate\":{\"spec\":{\"activeDeadlineSeconds\":500,\"backoffLimit\":6,\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-cft-service-live-refresh-ofac-list-cron\",\"environment\":\"production\",\"mode\":\"live\"}},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-cft-service\",\"--\",\"python\",\"parser/parser_ofac.py\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-cft-service-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-cft-service:f8f64ac-24.05.15-05.31-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-cft-service-live-refresh-ofac-list-cron\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"restartPolicy\":\"OnFailure\",\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-cft-service-live\",\"terminationGracePeriodSeconds\":500,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}},\"schedule\":\"0 3 1,15 * *\",\"startingDeadlineSeconds\":300,\"successfulJobsHistoryLimit\":3,\"suspend\":false}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2022-08-25T11:54:01+00:00", "generation": 14, "labels": {"app": "iluma-cft-service-live-refresh-ofac-list-cron", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-cft-service-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "batch/v1beta1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:concurrencyPolicy": {}, "f:failedJobsHistoryLimit": {}, "f:jobTemplate": {"f:spec": {"f:activeDeadlineSeconds": {}, "f:backoffLimit": {}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-cft-service-live-refresh-ofac-list-cron\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}}, "f:envFrom": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "f:schedule": {}, "f:suspend": {}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2024-04-28T10:32:18+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:jobTemplate": {"f:spec": {"f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-cft-service-live-refresh-ofac-list-cron\"}": {"f:image": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "f:startingDeadlineSeconds": {}, "f:successfulJobsHistoryLimit": {}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-21T08:34:24+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:lastScheduleTime": {}, "f:lastSuccessfulTime": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-08-01T03:00:51+00:00"}], "name": "iluma-cft-service-live-refresh-ofac-list-cron", "namespace": "iluma-live", "resourceVersion": "829984326", "uid": "a2c2afc5-417d-42f8-8aed-dd4de5219161"}, "spec": {"concurrencyPolicy": "Forbid", "failedJobsHistoryLimit": 1, "jobTemplate": {"metadata": {}, "spec": {"activeDeadlineSeconds": 500, "backoffLimit": 6, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-cft-service-live-refresh-ofac-list-cron", "environment": "production", "mode": "live"}}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-cft-service", "--", "python", "parser/parser_ofac.py"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}], "envFrom": [{"configMapRef": {"name": "iluma-cft-service-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-cft-service:f8f64ac-24.05.15-05.31-prod-live", "imagePullPolicy": "Always", "name": "iluma-cft-service-live-refresh-ofac-list-cron", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "OnFailure", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-cft-service-live", "serviceAccountName": "iluma-cft-service-live", "terminationGracePeriodSeconds": 500, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}}, "schedule": "0 3 1,15 * *", "startingDeadlineSeconds": 300, "successfulJobsHistoryLimit": 3, "suspend": false}, "status": {"lastScheduleTime": "2024-08-01T03:00:00+00:00", "lastSuccessfulTime": "2024-08-01T03:00:51+00:00"}, "apiVersion": "batch/v1", "kind": "<PERSON><PERSON><PERSON><PERSON>"}, {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"batch/v1\",\"kind\":\"CronJob\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-cft-service-live-refresh-ppatk-list-cron\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-cft-service-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-cft-service-live-refresh-ppatk-list-cron\",\"namespace\":\"iluma-live\"},\"spec\":{\"concurrencyPolicy\":\"Forbid\",\"failedJobsHistoryLimit\":1,\"jobTemplate\":{\"spec\":{\"activeDeadlineSeconds\":500,\"backoffLimit\":6,\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-cft-service-live-refresh-ppatk-list-cron\",\"environment\":\"production\",\"mode\":\"live\"}},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-cft-service\",\"--\",\"python\",\"parser/parser_ppatk.py\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-cft-service-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-cft-service:f8f64ac-24.05.15-05.31-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-cft-service-live-refresh-ppatk-list-cron\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"restartPolicy\":\"OnFailure\",\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-cft-service-live\",\"terminationGracePeriodSeconds\":500,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}},\"schedule\":\"0 2 * * *\",\"startingDeadlineSeconds\":300,\"successfulJobsHistoryLimit\":3,\"suspend\":false}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2022-08-25T11:54:01+00:00", "generation": 14, "labels": {"app": "iluma-cft-service-live-refresh-ppatk-list-cron", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-cft-service-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "batch/v1beta1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:concurrencyPolicy": {}, "f:failedJobsHistoryLimit": {}, "f:jobTemplate": {"f:spec": {"f:activeDeadlineSeconds": {}, "f:backoffLimit": {}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-cft-service-live-refresh-ppatk-list-cron\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}}, "f:envFrom": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "f:schedule": {}, "f:suspend": {}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2024-04-28T10:32:18+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:jobTemplate": {"f:spec": {"f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-cft-service-live-refresh-ppatk-list-cron\"}": {"f:image": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "f:startingDeadlineSeconds": {}, "f:successfulJobsHistoryLimit": {}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-21T08:34:24+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:lastScheduleTime": {}, "f:lastSuccessfulTime": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-08-13T02:00:16+00:00"}], "name": "iluma-cft-service-live-refresh-ppatk-list-cron", "namespace": "iluma-live", "resourceVersion": "840824800", "uid": "3fa8e198-a914-44d0-b0c6-fb1b0509a263"}, "spec": {"concurrencyPolicy": "Forbid", "failedJobsHistoryLimit": 1, "jobTemplate": {"metadata": {}, "spec": {"activeDeadlineSeconds": 500, "backoffLimit": 6, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-cft-service-live-refresh-ppatk-list-cron", "environment": "production", "mode": "live"}}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-cft-service", "--", "python", "parser/parser_ppatk.py"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}], "envFrom": [{"configMapRef": {"name": "iluma-cft-service-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-cft-service:f8f64ac-24.05.15-05.31-prod-live", "imagePullPolicy": "Always", "name": "iluma-cft-service-live-refresh-ppatk-list-cron", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "OnFailure", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-cft-service-live", "serviceAccountName": "iluma-cft-service-live", "terminationGracePeriodSeconds": 500, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}}, "schedule": "0 2 * * *", "startingDeadlineSeconds": 300, "successfulJobsHistoryLimit": 3, "suspend": false}, "status": {"lastScheduleTime": "2024-08-13T02:00:00+00:00", "lastSuccessfulTime": "2024-08-13T02:00:16+00:00"}, "apiVersion": "batch/v1", "kind": "<PERSON><PERSON><PERSON><PERSON>"}]}