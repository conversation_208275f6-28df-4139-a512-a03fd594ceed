{"apiVersion": "v1", "kind": "List", "items": [{"metadata": {"annotations": {"deployment.kubernetes.io/revision": "39", "helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"Deployment\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-ewallet-acc-validator-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\",\"namespace\":\"iluma-live\"},\"spec\":{\"replicas\":1,\"revisionHistoryLimit\":10,\"selector\":{\"matchLabels\":{\"app\":\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\",\"environment\":\"production\",\"mode\":\"live\"}},\"strategy\":{\"rollingUpdate\":{\"maxSurge\":2,\"maxUnavailable\":0},\"type\":\"RollingUpdate\"},\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\"},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}},\"podAntiAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\"]}]},\"topologyKey\":\"kubernetes.io/hostname\"},\"weight\":50},{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\"]}]},\"topologyKey\":\"failure-domain.beta.kubernetes.io/zone\"},\"weight\":50}]}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-ewallet-acc-validator\",\"--\",\"python\",\"src/queue_runners/sqs_queue_runner.py\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-ewallet-acc-validator-wrk\"},{\"name\":\"DD_AGENT_PORT\",\"value\":\"8126\"},{\"name\":\"DD_ENV\",\"value\":\"production-live\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-ewallet-acc-validator-wrk\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-ewallet-acc-validator-wrk\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-ewallet-acc-validator-live\"}}],\"image\":\"************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-ewallet-acc-validator:c6130b6-24.05.24-07.12-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-ewallet-acc-validator-live\",\"terminationGracePeriodSeconds\":62,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2021-09-29T11:46:50+00:00", "generation": 40, "labels": {"app": "iluma-ewallet-acc-validator-live-ewallet-worker-worker", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-ewallet-acc-validator-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:progressDeadlineSeconds": {}, "f:selector": {}, "f:strategy": {"f:rollingUpdate": {".": {}, "f:maxSurge": {}, "f:maxUnavailable": {}}, "f:type": {}}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}, "f:name": {}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}, "f:podAntiAffinity": {".": {}, "f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_AGENT_PORT\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_ENV\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "manager": "kubectl", "operation": "Update", "time": "2021-10-28T03:11:42+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:conditions": {".": {}, "k:{\"type\":\"Available\"}": {".": {}, "f:type": {}}, "k:{\"type\":\"Progressing\"}": {".": {}, "f:lastTransitionTime": {}, "f:status": {}, "f:type": {}}}}}, "manager": "kube-controller-manager", "operation": "Update", "time": "2023-01-27T08:59:44+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:spec": {"f:replicas": {}, "f:template": {"f:spec": {"f:containers": {"k:{\"name\":\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\"}": {"f:env": {"k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}}}}}}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2024-03-28T11:10:38+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:revisionHistoryLimit": {}, "f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-ewallet-acc-validator-live-ewallet-worker-worker\"}": {"f:image": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-24T07:20:34+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:deployment.kubernetes.io/revision": {}}}, "f:status": {"f:availableReplicas": {}, "f:conditions": {"k:{\"type\":\"Available\"}": {"f:lastTransitionTime": {}, "f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}, "f:status": {}}, "k:{\"type\":\"Progressing\"}": {"f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}}}, "f:observedGeneration": {}, "f:readyReplicas": {}, "f:replicas": {}, "f:updatedReplicas": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-07-19T03:31:01+00:00"}], "name": "iluma-ewallet-acc-validator-live-ewallet-worker-worker", "namespace": "iluma-live", "resourceVersion": "817381433", "uid": "4dc39f51-2bd5-4b67-ac20-c367aa06943b"}, "spec": {"progressDeadlineSeconds": 600, "replicas": 1, "revisionHistoryLimit": 10, "selector": {"matchLabels": {"app": "iluma-ewallet-acc-validator-live-ewallet-worker-worker", "environment": "production", "mode": "live"}}, "strategy": {"rollingUpdate": {"maxSurge": 2, "maxUnavailable": 0}, "type": "RollingUpdate"}, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-ewallet-acc-validator-live-ewallet-worker-worker", "environment": "production", "mode": "live"}, "name": "iluma-ewallet-acc-validator-live-ewallet-worker-worker"}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}, "podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-ewallet-acc-validator-live-ewallet-worker-worker"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 50}, {"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-ewallet-acc-validator-live-ewallet-worker-worker"]}]}, "topologyKey": "failure-domain.beta.kubernetes.io/zone"}, "weight": 50}]}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-ewallet-acc-validator", "--", "python", "src/queue_runners/sqs_queue_runner.py"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-ewallet-acc-validator-wrk"}, {"name": "DD_AGENT_PORT", "value": "8126"}, {"name": "DD_ENV", "value": "production-live"}, {"name": "DD_SERVICE", "value": "iluma-ewallet-acc-validator-wrk"}, {"name": "SERVICE_NAME", "value": "iluma-ewallet-acc-validator-wrk"}], "envFrom": [{"configMapRef": {"name": "iluma-ewallet-acc-validator-live"}}], "image": "************.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-ewallet-acc-validator:c6130b6-24.05.24-07.12-prod-live", "imagePullPolicy": "Always", "name": "iluma-ewallet-acc-validator-live-ewallet-worker-worker", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "Always", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-ewallet-acc-validator-live", "serviceAccountName": "iluma-ewallet-acc-validator-live", "terminationGracePeriodSeconds": 62, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}, "status": {"availableReplicas": 1, "conditions": [{"lastTransitionTime": "2021-09-29T11:46:50+00:00", "lastUpdateTime": "2024-05-24T07:20:49+00:00", "message": "ReplicaSet \"iluma-ewallet-acc-validator-live-ewallet-worker-worker-5bcf57ff8c\" has successfully progressed.", "reason": "NewReplicaSetAvailable", "status": "True", "type": "Progressing"}, {"lastTransitionTime": "2024-07-19T03:31:01+00:00", "lastUpdateTime": "2024-07-19T03:31:01+00:00", "message": "Deployment has minimum availability.", "reason": "MinimumReplicasAvailable", "status": "True", "type": "Available"}], "observedGeneration": 40, "readyReplicas": 1, "replicas": 1, "updatedReplicas": 1}, "apiVersion": "apps/v1", "kind": "Deployment"}]}