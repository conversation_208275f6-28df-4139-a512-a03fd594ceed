{"apiVersion": "v1", "kind": "List", "items": [{"metadata": {"annotations": {"deployment.kubernetes.io/revision": "25", "helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"apps/v1\",\"kind\":\"Deployment\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-signature-service-live-wrk-worker\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-signature-service-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-signature-service-live-wrk-worker\",\"namespace\":\"iluma-live\"},\"spec\":{\"replicas\":1,\"revisionHistoryLimit\":10,\"selector\":{\"matchLabels\":{\"app\":\"iluma-signature-service-live-wrk-worker\",\"environment\":\"production\",\"mode\":\"live\"}},\"strategy\":{\"rollingUpdate\":{\"maxSurge\":2,\"maxUnavailable\":0},\"type\":\"RollingUpdate\"},\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-signature-service-live-wrk-worker\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-signature-service-live-wrk-worker\"},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}},\"podAntiAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-signature-service-live-wrk-worker\"]}]},\"topologyKey\":\"kubernetes.io/hostname\"},\"weight\":50},{\"podAffinityTerm\":{\"labelSelector\":{\"matchExpressions\":[{\"key\":\"app\",\"operator\":\"In\",\"values\":[\"iluma-signature-service-live-wrk-worker\"]}]},\"topologyKey\":\"failure-domain.beta.kubernetes.io/zone\"},\"weight\":50}]}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-signature-service\",\"--\",\"npm\",\"run\",\"start-consumer\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-signature-service-wrk\"},{\"name\":\"DD_AGENT_PORT\",\"value\":\"8126\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-signature-service-wrk\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-signature-service-wrk\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-signature-service-live\"}}],\"image\":\"9854********.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-signature-service:dd05634-23.11.09-06.09-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-signature-service-live-wrk-worker\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-signature-service-live\",\"terminationGracePeriodSeconds\":62,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2022-08-05T13:07:39+00:00", "generation": 25, "labels": {"app": "iluma-signature-service-live-wrk-worker", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-signature-service-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:conditions": {".": {}, "k:{\"type\":\"Available\"}": {".": {}, "f:type": {}}, "k:{\"type\":\"Progressing\"}": {".": {}, "f:lastTransitionTime": {}, "f:status": {}, "f:type": {}}}}}, "manager": "kube-controller-manager", "operation": "Update", "time": "2023-01-31T08:36:53+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:progressDeadlineSeconds": {}, "f:replicas": {}, "f:selector": {}, "f:strategy": {"f:rollingUpdate": {".": {}, "f:maxSurge": {}, "f:maxUnavailable": {}}, "f:type": {}}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}, "f:name": {}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}, "f:podAntiAffinity": {".": {}, "f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-signature-service-live-wrk-worker\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_AGENT_PORT\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:image": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2023-11-14T09:55:46+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:revisionHistoryLimit": {}, "f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-23T09:49:08+00:00"}, {"apiVersion": "apps/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:deployment.kubernetes.io/revision": {}}}, "f:status": {"f:availableReplicas": {}, "f:conditions": {"k:{\"type\":\"Available\"}": {"f:lastTransitionTime": {}, "f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}, "f:status": {}}, "k:{\"type\":\"Progressing\"}": {"f:lastUpdateTime": {}, "f:message": {}, "f:reason": {}}}, "f:observedGeneration": {}, "f:readyReplicas": {}, "f:replicas": {}, "f:updatedReplicas": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-06-04T16:44:17+00:00"}], "name": "iluma-signature-service-live-wrk-worker", "namespace": "iluma-live", "resourceVersion": "773468691", "uid": "0cfb36fd-63f8-4380-8cad-1cc23f0e7c61"}, "spec": {"progressDeadlineSeconds": 600, "replicas": 1, "revisionHistoryLimit": 10, "selector": {"matchLabels": {"app": "iluma-signature-service-live-wrk-worker", "environment": "production", "mode": "live"}}, "strategy": {"rollingUpdate": {"maxSurge": 2, "maxUnavailable": 0}, "type": "RollingUpdate"}, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-signature-service-live-wrk-worker", "environment": "production", "mode": "live"}, "name": "iluma-signature-service-live-wrk-worker"}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}, "podAntiAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-signature-service-live-wrk-worker"]}]}, "topologyKey": "kubernetes.io/hostname"}, "weight": 50}, {"podAffinityTerm": {"labelSelector": {"matchExpressions": [{"key": "app", "operator": "In", "values": ["iluma-signature-service-live-wrk-worker"]}]}, "topologyKey": "failure-domain.beta.kubernetes.io/zone"}, "weight": 50}]}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-signature-service", "--", "npm", "run", "start-consumer"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-signature-service-wrk"}, {"name": "DD_AGENT_PORT", "value": "8126"}, {"name": "DD_SERVICE", "value": "iluma-signature-service-wrk"}, {"name": "SERVICE_NAME", "value": "iluma-signature-service-wrk"}], "envFrom": [{"configMapRef": {"name": "iluma-signature-service-live"}}], "image": "9854********.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-signature-service:dd05634-23.11.09-06.09-prod-live", "imagePullPolicy": "Always", "name": "iluma-signature-service-live-wrk-worker", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "Always", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-signature-service-live", "serviceAccountName": "iluma-signature-service-live", "terminationGracePeriodSeconds": 62, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}, "status": {"availableReplicas": 1, "conditions": [{"lastTransitionTime": "2022-08-05T13:07:39+00:00", "lastUpdateTime": "2024-05-23T09:49:10+00:00", "message": "ReplicaSet \"iluma-signature-service-live-wrk-worker-66f8cc9fdb\" has successfully progressed.", "reason": "NewReplicaSetAvailable", "status": "True", "type": "Progressing"}, {"lastTransitionTime": "2024-06-04T16:44:17+00:00", "lastUpdateTime": "2024-06-04T16:44:17+00:00", "message": "Deployment has minimum availability.", "reason": "MinimumReplicasAvailable", "status": "True", "type": "Available"}], "observedGeneration": 25, "readyReplicas": 1, "replicas": 1, "updatedReplicas": 1}, "apiVersion": "apps/v1", "kind": "Deployment"}, {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "kubectl.kubernetes.io/last-applied-configuration": "{\"apiVersion\":\"batch/v1\",\"kind\":\"CronJob\",\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"kubernetes.io/change-cause\":\"\"},\"labels\":{\"app\":\"iluma-signature-service-live-start-bulk-sign-cron\",\"argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com\":\"iluma-signature-service-live\",\"environment\":\"production\",\"mode\":\"live\"},\"name\":\"iluma-signature-service-live-start-bulk-sign-cron\",\"namespace\":\"iluma-live\"},\"spec\":{\"concurrencyPolicy\":\"Forbid\",\"failedJobsHistoryLimit\":1,\"jobTemplate\":{\"spec\":{\"activeDeadlineSeconds\":3600,\"backoffLimit\":6,\"template\":{\"metadata\":{\"annotations\":{\"helm.sh/chart\":\"xendit-deployment-2.3.0\",\"linkerd.io/inject\":\"disabled\"},\"labels\":{\"app\":\"iluma-signature-service-live-start-bulk-sign-cron\",\"environment\":\"production\",\"mode\":\"live\"}},\"spec\":{\"affinity\":{\"nodeAffinity\":{\"preferredDuringSchedulingIgnoredDuringExecution\":[{\"preference\":{\"matchExpressions\":[{\"key\":\"workload\",\"operator\":\"In\",\"values\":[\"worker\"]}]},\"weight\":1}],\"requiredDuringSchedulingIgnoredDuringExecution\":{\"nodeSelectorTerms\":[{\"matchExpressions\":[{\"key\":\"mode\",\"operator\":\"In\",\"values\":[\"live\"]}]}]}}},\"containers\":[{\"command\":[\"chamber\",\"exec\",\"production/live/iluma-signature-service\",\"--\",\"node\",\"dist/src/crons/bulkSign.js\"],\"env\":[{\"name\":\"CHAMBER_KMS_KEY_ALIAS\",\"value\":\"parameter_store_key\"},{\"name\":\"CHAMBER_AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"AWS_REGION\",\"value\":\"ap-southeast-1\"},{\"name\":\"DD_AGENT_HOST\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"DOGSTATSD_HOST_IP\",\"valueFrom\":{\"fieldRef\":{\"fieldPath\":\"status.hostIP\"}}},{\"name\":\"APP_NAME\",\"value\":\"iluma-signature-service-cron\"},{\"name\":\"DD_AGENT_PORT\",\"value\":\"8126\"},{\"name\":\"DD_SERVICE\",\"value\":\"iluma-signature-service-cron\"},{\"name\":\"SERVICE_NAME\",\"value\":\"iluma-signature-service-cron\"}],\"envFrom\":[{\"configMapRef\":{\"name\":\"iluma-signature-service-live\"}}],\"image\":\"9854********.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-signature-service:dd05634-23.11.09-06.09-prod-live\",\"imagePullPolicy\":\"Always\",\"name\":\"iluma-signature-service-live-start-bulk-sign-cron\",\"resources\":{\"limits\":{\"memory\":\"1024Mi\"},\"requests\":{\"cpu\":\"201m\",\"memory\":\"300Mi\"}}}],\"restartPolicy\":\"OnFailure\",\"securityContext\":{\"fsGroup\":65534},\"serviceAccountName\":\"iluma-signature-service-live\",\"terminationGracePeriodSeconds\":120,\"tolerations\":[{\"effect\":\"NoSchedule\",\"key\":\"mode\",\"operator\":\"Equal\",\"value\":\"live\"}]}}}},\"schedule\":\"0 2 * * *\",\"startingDeadlineSeconds\":300,\"successfulJobsHistoryLimit\":3,\"suspend\":false}}\n", "kubernetes.io/change-cause": ""}, "creationTimestamp": "2022-09-01T09:22:57+00:00", "generation": 6, "labels": {"app": "iluma-signature-service-live-start-bulk-sign-cron", "argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": "iluma-signature-service-live", "environment": "production", "mode": "live"}, "managedFields": [{"apiVersion": "batch/v1beta1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:concurrencyPolicy": {}, "f:failedJobsHistoryLimit": {}, "f:jobTemplate": {"f:spec": {"f:activeDeadlineSeconds": {}, "f:backoffLimit": {}, "f:template": {"f:metadata": {"f:annotations": {".": {}, "f:linkerd.io/inject": {}}, "f:labels": {".": {}, "f:app": {}, "f:environment": {}, "f:mode": {}}}, "f:spec": {"f:affinity": {".": {}, "f:nodeAffinity": {".": {}, "f:requiredDuringSchedulingIgnoredDuringExecution": {}}}, "f:containers": {"k:{\"name\":\"iluma-signature-service-live-start-bulk-sign-cron\"}": {".": {}, "f:command": {}, "f:env": {".": {}, "k:{\"name\":\"APP_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_AWS_REGION\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"CHAMBER_KMS_KEY_ALIAS\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_AGENT_HOST\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"DD_AGENT_PORT\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DD_SERVICE\"}": {".": {}, "f:name": {}, "f:value": {}}, "k:{\"name\":\"DOGSTATSD_HOST_IP\"}": {".": {}, "f:name": {}, "f:valueFrom": {".": {}, "f:fieldRef": {}}}, "k:{\"name\":\"SERVICE_NAME\"}": {".": {}, "f:name": {}, "f:value": {}}}, "f:envFrom": {}, "f:image": {}, "f:imagePullPolicy": {}, "f:name": {}, "f:resources": {".": {}, "f:limits": {".": {}, "f:memory": {}}, "f:requests": {".": {}, "f:cpu": {}, "f:memory": {}}}, "f:terminationMessagePath": {}, "f:terminationMessagePolicy": {}}}, "f:dnsPolicy": {}, "f:restartPolicy": {}, "f:schedulerName": {}, "f:securityContext": {".": {}, "f:fsGroup": {}}, "f:serviceAccount": {}, "f:serviceAccountName": {}, "f:tolerations": {}}}}}, "f:schedule": {}, "f:suspend": {}}}, "manager": "kubectl-client-side-apply", "operation": "Update", "time": "2023-11-14T09:55:46+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}, "f:kubectl.kubernetes.io/last-applied-configuration": {}, "f:kubernetes.io/change-cause": {}}, "f:labels": {"f:argocd-trident-iluma-production-0.ap-southeast-1.il.tidnex.com": {}}}, "f:spec": {"f:jobTemplate": {"f:spec": {"f:template": {"f:metadata": {"f:annotations": {"f:helm.sh/chart": {}}}, "f:spec": {"f:affinity": {"f:nodeAffinity": {"f:preferredDuringSchedulingIgnoredDuringExecution": {}}}, "f:terminationGracePeriodSeconds": {}}}}}, "f:startingDeadlineSeconds": {}, "f:successfulJobsHistoryLimit": {}}}, "manager": "argocd-controller", "operation": "Update", "time": "2024-05-23T09:49:08+00:00"}, {"apiVersion": "batch/v1", "fieldsType": "FieldsV1", "fieldsV1": {"f:status": {"f:lastScheduleTime": {}, "f:lastSuccessfulTime": {}}}, "manager": "kube-controller-manager", "operation": "Update", "subresource": "status", "time": "2024-08-13T02:00:19+00:00"}], "name": "iluma-signature-service-live-start-bulk-sign-cron", "namespace": "iluma-live", "resourceVersion": "840824841", "uid": "7f62b8be-0762-4383-91fe-aa49b9233d2c"}, "spec": {"concurrencyPolicy": "Forbid", "failedJobsHistoryLimit": 1, "jobTemplate": {"metadata": {}, "spec": {"activeDeadlineSeconds": 3600, "backoffLimit": 6, "template": {"metadata": {"annotations": {"helm.sh/chart": "xendit-deployment-2.3.0", "linkerd.io/inject": "disabled"}, "labels": {"app": "iluma-signature-service-live-start-bulk-sign-cron", "environment": "production", "mode": "live"}}, "spec": {"affinity": {"nodeAffinity": {"preferredDuringSchedulingIgnoredDuringExecution": [{"preference": {"matchExpressions": [{"key": "workload", "operator": "In", "values": ["worker"]}]}, "weight": 1}], "requiredDuringSchedulingIgnoredDuringExecution": {"nodeSelectorTerms": [{"matchExpressions": [{"key": "mode", "operator": "In", "values": ["live"]}]}]}}}, "containers": [{"command": ["chamber", "exec", "production/live/iluma-signature-service", "--", "node", "dist/src/crons/bulkSign.js"], "env": [{"name": "CHAMBER_KMS_KEY_ALIAS", "value": "parameter_store_key"}, {"name": "CHAMBER_AWS_REGION", "value": "ap-southeast-1"}, {"name": "AWS_REGION", "value": "ap-southeast-1"}, {"name": "DD_AGENT_HOST", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "DOGSTATSD_HOST_IP", "valueFrom": {"fieldRef": {"apiVersion": "v1", "fieldPath": "status.hostIP"}}}, {"name": "APP_NAME", "value": "iluma-signature-service-cron"}, {"name": "DD_AGENT_PORT", "value": "8126"}, {"name": "DD_SERVICE", "value": "iluma-signature-service-cron"}, {"name": "SERVICE_NAME", "value": "iluma-signature-service-cron"}], "envFrom": [{"configMapRef": {"name": "iluma-signature-service-live"}}], "image": "9854********.dkr.ecr.ap-southeast-1.amazonaws.com/iluma/iluma-signature-service:dd05634-23.11.09-06.09-prod-live", "imagePullPolicy": "Always", "name": "iluma-signature-service-live-start-bulk-sign-cron", "resources": {"limits": {"memory": "1Gi"}, "requests": {"cpu": "201m", "memory": "300Mi"}}, "terminationMessagePath": "/dev/termination-log", "terminationMessagePolicy": "File"}], "dnsPolicy": "ClusterFirst", "restartPolicy": "OnFailure", "schedulerName": "default-scheduler", "securityContext": {"fsGroup": 65534}, "serviceAccount": "iluma-signature-service-live", "serviceAccountName": "iluma-signature-service-live", "terminationGracePeriodSeconds": 120, "tolerations": [{"effect": "NoSchedule", "key": "mode", "operator": "Equal", "value": "live"}]}}}}, "schedule": "0 2 * * *", "startingDeadlineSeconds": 300, "successfulJobsHistoryLimit": 3, "suspend": false}, "status": {"lastScheduleTime": "2024-08-13T02:00:00+00:00", "lastSuccessfulTime": "2024-08-13T02:00:19+00:00"}, "apiVersion": "batch/v1", "kind": "<PERSON><PERSON><PERSON><PERSON>"}]}