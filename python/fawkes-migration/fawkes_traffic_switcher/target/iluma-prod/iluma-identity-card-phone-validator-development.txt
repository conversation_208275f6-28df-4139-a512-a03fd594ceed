INFO:root:Running in dry_run=True
INFO:root:Creating and validating traffic switch target name=iluma-identity-card-phone-validator-prod-dev.ap-southeast-1.il.tidnex.com
INFO:botocore.tokens:Loading cached SSO token for xendit
INFO:botocore.tokens:Loading cached SSO token for xendit
INFO:root:Starting traffic switch for name=iluma-identity-card-phone-validator-prod-dev.ap-southeast-1.il.tidnex.com type=CNAME strategy=TrafficSwitchStrategy.TRAEFIK_PRIVATE source_set_id=mass_conversion source_aws_profile=iluma source_zone_id=/hostedzone/Z01395981AMCRCR6ZSELB target_set_id=fawkes target_aws_profile=iluma target_zone_id=/hostedzone/Z01395981AMCRCR6ZSELB num_iteration=5 total_duration=360 rollback=False
INFO:root:Traffic switch dry_run=True name=iluma-identity-card-phone-validator-prod-dev.ap-southeast-1.il.tidnex.com strategy=TrafficSwitchStrategy.TRAEFIK_PRIVATE iteration=1 weight=20 duration=90
INFO:root:Traffic switch dry_run=True name=iluma-identity-card-phone-validator-prod-dev.ap-southeast-1.il.tidnex.com strategy=TrafficSwitchStrategy.TRAEFIK_PRIVATE iteration=2 weight=40 duration=90
INFO:root:Traffic switch dry_run=True name=iluma-identity-card-phone-validator-prod-dev.ap-southeast-1.il.tidnex.com strategy=TrafficSwitchStrategy.TRAEFIK_PRIVATE iteration=3 weight=60 duration=90
INFO:root:Traffic switch dry_run=True name=iluma-identity-card-phone-validator-prod-dev.ap-southeast-1.il.tidnex.com strategy=TrafficSwitchStrategy.TRAEFIK_PRIVATE iteration=4 weight=80 duration=90
INFO:root:Traffic switch dry_run=True name=iluma-identity-card-phone-validator-prod-dev.ap-southeast-1.il.tidnex.com strategy=TrafficSwitchStrategy.TRAEFIK_PRIVATE iteration=5 weight=100 duration=0