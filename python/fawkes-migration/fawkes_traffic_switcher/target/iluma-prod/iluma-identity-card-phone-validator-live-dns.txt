(venv) ➜  checkDNS git:(il-trident-fawkes-prod-live-migration) ✗ python3  main_hosts_check.py --host iluma-identity-card-phone-validator-prod-live.ap-southeast-1.il.tidnex.com > iluma-identity-card-phone-validator-live.txt
* Process Hosts  ******************************* Starting Checks ***************************************** *
* Process Hosts                                                                       Number of host(s): 1 *
* R53 Resolves                                                                         R53 Record resolves *
* R53 Resolves                                        Number of resolved host(s), to continue checks on: 1 *
* R53 Resolves                                                             Number of unresolved host(s): 0 *
* A Records                                                DNS chain contains IP addresses only (A record) *
* A Records                                                               Domains with A type record(s): 0 *
* A Records                                                  Number of domains without A type record(s): 1 *
* Self-servicen          Check DNS chain has: traefik-public AND cdn.cloudflare.net NOT elb.amazonaws.com. *
* Self-servicen                                Number of domain(s) not good for self-service (big-bang): 0 *
* Self-servicen      Number of domain(s) to do further checks and determine if they can be self-service: 1 *
* CDN -- CF.NET         Checking if at least one host in the resolution chain contains cdn.cloudflare.net. *
* CDN -- CF.NET                                       Number of domain(s) containing cdn.cloudflare.net: 0 *
* CDN -- CF.NET                                   Number of domain(s) not containing cdn.cloudflare.net: 1 *
* Traefik pub/elb                                DNS chain has: traefik-public AND elb.amazonaws.com only. *
* Traefik pub/elb      Number of domain(s) matching traefik-pub, aws.com, and elb. Needs next CF checks: 1 *
* Traefik pub/elb    Number of domain(s) unmatched traefik-pub, aws.com, and elb. Edge case investigate: 0 *
* Fawkes LB                                                   DNS chain has: traefik-public AND fawkes LB. *
* Fawkes LB                                   Number of domain(s) matching traefik-public and fawkes LB: 0 *
* Fawkes LB                               Number of domain(s) not matching traefik-public and fawkes LB: 0 *
* Traefik Priv NLB                                    DNS chain has: traefik-priv and specified NLB names. *
* Traefik Priv NLB                    Number of domain(s) matching traefik-priv and specified NLB names: 0 *
* Traefik Priv NLB                Number of domain(s) not matching traefik-priv and specified NLB names: 0 *
* Traefik Internal NLB                   Chain has: traefik-internal and AWS region and private NLB names. *
* Traefik Internal NLB           Domain(s) matching traefik-internal, AWS region, and private NLB names: 0 *
* Traefik Internal NLB       Domain(s) not matching traefik-internal, AWS region, and private NLB names: 0 *
* Traefik Internal NLB                            Number of domain(s) matching traefik-internal and ELB: 0 *
* Traefik Internal NLB                        Number of domain(s) not matching traefik-internal and ELB: 0 *
* CF pages                                  * DNS chain has: cdn.cloudflare.net only and Is on CF pages? * *
* CF pages                                             Number of domain(s) pointing to Cloudflare pages: 0 *
* CF pages   Number of domain(s) with cdn.cloudflare.net in the resolution chain that need 'Ferry Boat': 0 *
* Traefik cf nlb                                               * CF DNS of host points to traefik-cf NLB * *
* Traefik cf nlb                         Number of domain(s) that are edge cases and need investigation: 0 *
* Traefik cf nlb                                         Number of domain(s) pointing to Traefik-CF NLB: 1 *
* ******************************************* Summary **************************************************** *
*                                             All hosts pass the checks, self-service can be done on them. *
*    iluma-identity-card-phone-validator-prod-live.ap-southeast-1.il.tidnex.com: Ready for traffic switch! *
* ******************************************** Exiting *************************************************** *