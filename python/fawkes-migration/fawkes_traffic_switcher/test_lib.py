import sys

sys.path.append("../library")

import lib

from infra_r53 import R53RecordSet
from cloudflare.types.dns import CNAMERecord
from cloudflare.types.load_balancers import LoadBalancer, DefaultPools, Pool, Origin


def test_infer_strategy_private(mocker):
    mocker.patch(
        "infra_lib.get_ip_list", return_value=["*********", "*********", "*********"]
    )
    lib.infer_strategy("api.xendit.co") == "TRAEFIK_PRIVATE"


def test_infer_strategy_public(mocker):
    mocker.patch(
        "infra_lib.get_ip_list",
        return_value=["**********", "**************", "************"],
    )
    lib.infer_strategy("api.xendit.co") == "TRAEFIK_PUBLIC"


def test_infer_strategy_ferry_boat(mocker):
    mocker.patch(
        "infra_lib.get_ip_list",
        return_value=["*************", "*************"],
    )
    lib.infer_strategy("api.xendit.co") == "FERRY_BOAT"


def test_infer_strategy_ferry_none(mocker):
    mocker.patch(
        "infra_lib.get_ip_list",
        return_value=["*************", "*************"],
    )
    lib.infer_strategy("api.xendit.co") is None


def test_infer_trident_r53_set_id_none(mocker):
    mocked_record = []
    mocker.patch("infra_r53.get_record_set", return_value=mocked_record)
    assert lib.infer_trident_r53_set_id("mocked") is None


def test_infer_trident_r53_set_id(mocker):
    mocked_record = [
        R53RecordSet(
            aws_profile="xendit",
            zone_id="XXXXXX",
            record_set={"Weight": 100, "SetIdentifier": "sg"},
        ),
        R53RecordSet(
            aws_profile="xendit",
            zone_id="XXXXXX",
            record_set={"Weight": 0, "SetIdentifier": "fawkes"},
        ),
    ]
    mocker.patch("infra_r53.get_record_set", return_value=mocked_record)
    assert lib.infer_trident_r53_set_id("mocked") == "sg"


def test_infer_trident_r53_set_id_multiple(mocker):
    mocked_record = [
        R53RecordSet(
            aws_profile="xendit",
            zone_id="XXXXXX",
            record_set={"Weight": 0, "SetIdentifier": "sg"},
        ),
        R53RecordSet(
            aws_profile="xendit",
            zone_id="XXXXXX",
            record_set={"Weight": 100, "SetIdentifier": "mass_conversion"},
        ),
        R53RecordSet(
            aws_profile="xendit",
            zone_id="XXXXXX",
            record_set={"Weight": 0, "SetIdentifier": "fawkes"},
        ),
    ]
    mocker.patch("infra_r53.get_record_set", return_value=mocked_record)

    assert lib.infer_trident_r53_set_id("mocked") == "mass_conversion"


def test_infer_trident_cf_origin_none(mocker):
    mocker.patch("infra_cf.get_cf_record", return_value=[])

    assert lib.infer_trident_cf_origin("mocked") is None


def test_infer_trident_cf_origin_proxied(mocker):
    content = "api.xendit.co"
    mocked_response = [
        CNAMERecord(proxied=True, content=content, name=content, type="CNAME"),
    ]
    mocker.patch("infra_cf.get_cf_record", return_value=mocked_response)
    mocker.patch("fawkes.is_routed_to_trident_public", return_value=True)

    assert lib.infer_trident_cf_origin("mocked") == content


def test_infer_trident_cf_origin_not_proxied(mocker):
    content = "api.xendit.co"
    mocked_response = [
        CNAMERecord(proxied=False, content=content, name=content, type="CNAME"),
    ]
    mocked_lb = LoadBalancer(default_pools=[DefaultPools("xxx")])
    mocked_pool = Pool(origins=[Origin(address=content, weight=1.0)])

    mocker.patch("infra_cf.get_cf_record", return_value=mocked_response)
    mocker.patch("infra_cf.get_cf_lb", return_value=mocked_lb)
    mocker.patch("infra_cf.get_cf_pool", return_value=mocked_pool)
    mocker.patch("fawkes.is_routed_to_trident_public", return_value=True)

    assert lib.infer_trident_cf_origin("mocked") == content


def test_infer_fawkes_cf_origin_none(mocker):
    mocker.patch("lib.infer_trident_cf_origin", return_value=None)

    assert lib.infer_fawkes_cf_origin("mocked") is None


def test_infer_fawkes_cf_origin(mocker):
    mocker.patch(
        "lib.infer_trident_cf_origin",
        return_value="trfk-cf-xnd-sg-stg-aws-0-7ba8e8b3ce9b7165.elb.ap-southeast-1.amazonaws.com",
    )

    assert (
        lib.infer_fawkes_cf_origin("mocked")
        == "xnd-sg-stg-aws-0-cf.sg.live.nonpci.stg.tidnex.dev"
    )
