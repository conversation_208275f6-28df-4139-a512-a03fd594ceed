import sys
import logging

import click

sys.path.append("../library")

from model import globals

@click.group(context_settings={"show_default": True})
@click.option("--debug", default=False, is_flag=True, help="Enable debug logging")
@click.option(
    "--source-cluster",
    required=True,
    help="Source EKS cluster name",
)
@click.option(
    "--dest-cluster",
    required=True,
    help="Destination EKS cluster name",
)
@click.option(
    "--region",
    required=True,
    help="AWS region where cluster is located",
)
@click.option(
    "--aws-profile",
    required=True,
    help="AWS profile name where cluster is located",
)
@click.option(
    "--namespace",
    "-n",
    help="Cluster namespace to target",
)
@click.option(
    "--service",
    "-s",
    help="Application name to target",
)
def cli(**kwargs):
    globals.register_inputs(**kwargs)

@cli.command()
def check(**kwargs):
    globals.register_inputs(**kwargs)

@cli.group()
@click.option(
    "--dry-run",
    default=True,
    help="Flag to indicate whether the script will run in dry run mode",
)
def migrate(**kwargs):
    globals.register_inputs(**kwargs)
    logging.getLogger().setLevel(logging.DEBUG if globals.Inputs.debug else logging.INFO)

from model.service_components import service_role
migrate.add_command(service_role.migrate, name='service-role')

from model.service_components import workers_crons
migrate.add_command(workers_crons.cli, name='workers-and-crons')

import r53_weighted_converter.main as r53_weighted_converter
migrate.add_command(r53_weighted_converter.main, name='r53-weighted-converter')

from model.service_components import r53_fawkes_destination
migrate.add_command(r53_fawkes_destination.migrate, name='r53-fawkes-destination')

import fawkes_traffic_switcher.main as fawkes_traffic_switcher
migrate.add_command(fawkes_traffic_switcher.main, name='traffic-switch')

logging.basicConfig(format="[%(levelname)s] %(message)s")
logging.getLogger("botocore.credentials").disabled = True
logging.getLogger("httpx").disabled = True

if __name__ == '__main__':
    cli()
