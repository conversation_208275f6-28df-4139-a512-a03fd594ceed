- chamber_policy: terraform-20240111020508159200000001
  chamber_services:
  - xendit-infra
  - service-discovery
  - ach-adapter
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: ach-adapter-live
  namespace: banking-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/ach-adapter-live-production-ap-southeast-3
  role_max_session_duration: 3600
  role_name: ach-adapter-live-production-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20240111020508159200000001:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/ach-adapter/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: ach-adapter
  sns_sqs_policy: null
- chamber_policy: terraform-20230901065958745400000001
  chamber_services:
  - xendit-infra
  - service-discovery
  - payment-merchant-connector
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: development
  name: payment-merchant-connector-development
  namespace: cashpay-dev
  pci_scope: non_pci
  repo: xendit/cash-payment-merchant-gateway
  role_arn: arn:aws:iam::************:role/payment-merchant-connector-development-production-ap-southeast-3
  role_max_session_duration: 3600
  role_name: payment-merchant-connector-development-production-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20230901065958745400000001:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/development/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/development/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/development/payment-merchant-connector/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: payment-merchant-connector
  sns_sqs_policy: null
- chamber_policy: terraform-20230911041040744700000001
  chamber_services:
  - xendit-infra
  - service-discovery
  - payment-merchant-connector
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: payment-merchant-connector-live
  namespace: cashpay-live
  pci_scope: non_pci
  repo: xendit/cash-payment-merchant-gateway
  role_arn: arn:aws:iam::************:role/payment-merchant-connector-live-production-ap-southeast-3
  role_max_session_duration: 3600
  role_name: payment-merchant-connector-live-production-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20230911041040744700000001:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/payment-merchant-connector/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: payment-merchant-connector
  sns_sqs_policy: null
- chamber_policy: terraform-20240620034636808300000001
  chamber_services:
  - xendit-infra
  - service-discovery
  - incident-alert-service
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: incident-alert-service-live
  namespace: dev-platform-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/incident-alert-service-live-production-ap-southeast-3
  role_max_session_duration: 3600
  role_name: incident-alert-service-live-production-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20240620034636808300000001:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/incident-alert-service/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
    terraform-20240620073554975500000001:
      Statement:
      - Action:
        - sqs:StartMessageMoveTask
        - sqs:SendMessageBatch
        - sqs:SendMessage
        - sqs:ReceiveMessage
        - sqs:ListQueueTags
        - sqs:ListMessageMoveTasks
        - sqs:ListDeadLetterSourceQueues
        - sqs:GetQueueUrl
        - sqs:GetQueueAttributes
        - sqs:DeleteMessageBatch
        - sqs:DeleteMessage
        - sqs:ChangeMessageVisibilityBatch
        - sqs:ChangeMessageVisibility
        - sqs:CancelMessageMoveTask
        Effect: Allow
        Resource:
        - arn:aws:sqs:ap-southeast-1:************:dev-platform_incident-alert-service_production-live_notifications_queue
        - arn:aws:sqs:ap-southeast-1:************:dev-platform_incident-alert-service_production-live_dlq_notifications_queue
      - Action:
        - kms:GenerateDataKey*
        - kms:Encrypt
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-1:************:key/3977d110-f1e7-4d28-915c-bcd3b3110b69
      Version: '2012-10-17'
  service_name: incident-alert-service
  sns_sqs_policy: terraform-20240620073554975500000001
- chamber_policy: terraform-20230721072539862900000001
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-bank-connector-bni-dd
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: development
  name: id-bank-connector-bni-dd-development
  namespace: direct-debit-dev
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-bank-connector-bni-dd-dev-prod-jk
  role_max_session_duration: 3600
  role_name: id-bank-connector-bni-dd-dev-prod-jk
  role_path: /
  role_policy:
    terraform-20230721072539862900000001:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/development/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/development/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/development/id-bank-connector-bni-dd/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-bank-connector-bni-dd
  sns_sqs_policy: null
- chamber_policy: terraform-20230726024605072500000002
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-bni
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies:
  - id-disb-bank-connector-bni_policy_ap-southeast-3_production_live
  mode: live
  name: id-disb-bank-connector-bni-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-bni-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-bni-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    id-disb-bank-connector-bni_policy_ap-southeast-3_production_live:
      Statement:
      - Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:ListSubscriptionsByTopic
        Effect: Allow
        Resource:
        - arn:aws:sns:*:************:payouts_*
      - Action:
        - sqs:DeleteMessage
        - sqs:GetQueueUrl
        - sqs:ChangeMessageVisibility
        - sqs:SendMessageBatch
        - sqs:UntagQueue
        - sqs:ReceiveMessage
        - sqs:SendMessage
        - sqs:GetQueueAttributes
        - sqs:ListQueueTags
        - sqs:TagQueue
        - sqs:ListDeadLetterSourceQueues
        - sqs:DeleteMessageBatch
        - sqs:PurgeQueue
        - sqs:DeleteQueue
        - sqs:CreateQueue
        - sqs:ChangeMessageVisibilityBatch
        - sqs:SetQueueAttributes
        Effect: Allow
        Resource:
        - arn:aws:sqs:*:************:payouts_id-*-id-bni-dbc_prod-live_*
      - Action:
        - kms:Encrypt
        - kms:Decrypt
        - kms:GenerateDataKey*
        - kms:DescribeKey
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-1:************:key/0e63c4ba-98d5-4e3e-afd4-a73977ccec60
        - arn:aws:kms:ap-southeast-3:************:key/69fa210e-5895-4ac4-8b88-a1a7781f7daa
      Version: '2012-10-17'
    terraform-20230726024605072500000002:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-bni/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-bni
  sns_sqs_policy: null
- chamber_policy: terraform-20240718084930689500000003
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-bni-migration
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: id-disb-bank-connector-bni-migration-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: xendit/disbursement-bank-connector
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-bni-mig-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-bni-mig-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20240718084930689500000003:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-bni-migration/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-bni-migration
  sns_sqs_policy: null
- chamber_policy: terraform-20230829125010343300000001
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-bni-rtgs
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies:
  - id-disb-bank-connector-bni-rtgs_policy_ap-southeast-3_production_live
  mode: live
  name: id-disb-bank-connector-bni-rtgs-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-bni-rtgs-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-bni-rtgs-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    id-disb-bank-connector-bni-rtgs_policy_ap-southeast-3_production_live:
      Statement:
      - Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:ListSubscriptionsByTopic
        Effect: Allow
        Resource:
        - arn:aws:sns:*:************:payouts_*
      - Action:
        - sqs:DeleteMessage
        - sqs:GetQueueUrl
        - sqs:ChangeMessageVisibility
        - sqs:SendMessageBatch
        - sqs:UntagQueue
        - sqs:ReceiveMessage
        - sqs:SendMessage
        - sqs:GetQueueAttributes
        - sqs:ListQueueTags
        - sqs:TagQueue
        - sqs:ListDeadLetterSourceQueues
        - sqs:DeleteMessageBatch
        - sqs:PurgeQueue
        - sqs:DeleteQueue
        - sqs:CreateQueue
        - sqs:ChangeMessageVisibilityBatch
        - sqs:SetQueueAttributes
        Effect: Allow
        Resource:
        - arn:aws:sqs:*:************:payouts_id-*-id-bni-rtgs-dbc_prod-live_*
      - Action:
        - kms:Encrypt
        - kms:Decrypt
        - kms:GenerateDataKey*
        - kms:DescribeKey
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-1:************:key/0e63c4ba-98d5-4e3e-afd4-a73977ccec60
        - arn:aws:kms:ap-southeast-3:************:key/69fa210e-5895-4ac4-8b88-a1a7781f7daa
      Version: '2012-10-17'
    terraform-20230829125010343300000001:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-bni-rtgs/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-bni-rtgs
  sns_sqs_policy: null
- chamber_policy: terraform-20240718084930664300000001
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-bni-rtgs-migration
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: id-disb-bank-connector-bni-rtgs-migration-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: xendit/disbursement-bank-connector
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-bni-rtgs-mig-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-bni-rtgs-mig-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20240718084930664300000001:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-bni-rtgs-migration/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-bni-rtgs-migration
  sns_sqs_policy: null
- chamber_policy: terraform-20230807025459882600000002
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-bri
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies:
  - id-disb-bank-connector-bri_policy_ap-southeast-3_production_live
  mode: live
  name: id-disb-bank-connector-bri-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-bri-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-bri-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    id-disb-bank-connector-bri_policy_ap-southeast-3_production_live:
      Statement:
      - Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:ListSubscriptionsByTopic
        Effect: Allow
        Resource:
        - arn:aws:sns:*:************:payouts_*
      - Action:
        - sqs:DeleteMessage
        - sqs:GetQueueUrl
        - sqs:ChangeMessageVisibility
        - sqs:SendMessageBatch
        - sqs:UntagQueue
        - sqs:ReceiveMessage
        - sqs:SendMessage
        - sqs:GetQueueAttributes
        - sqs:ListQueueTags
        - sqs:TagQueue
        - sqs:ListDeadLetterSourceQueues
        - sqs:DeleteMessageBatch
        - sqs:PurgeQueue
        - sqs:DeleteQueue
        - sqs:CreateQueue
        - sqs:ChangeMessageVisibilityBatch
        - sqs:SetQueueAttributes
        Effect: Allow
        Resource:
        - arn:aws:sqs:*:************:payouts_id-*-id-bri-dbc_prod-live_*
      - Action:
        - kms:Encrypt
        - kms:Decrypt
        - kms:GenerateDataKey*
        - kms:DescribeKey
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-1:************:key/0e63c4ba-98d5-4e3e-afd4-a73977ccec60
        - arn:aws:kms:ap-southeast-3:************:key/69fa210e-5895-4ac4-8b88-a1a7781f7daa
      Version: '2012-10-17'
    terraform-20230807025459882600000002:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-bri/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-bri
  sns_sqs_policy: null
- chamber_policy: terraform-20240718084930674500000002
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-bri-migration
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: id-disb-bank-connector-bri-migration-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: xendit/disbursement-bank-connector
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-bri-migration-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-bri-migration-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20240718084930674500000002:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-bri-migration/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-bri-migration
  sns_sqs_policy: null
- chamber_policy: terraform-20230531075105454900000002
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-bss
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies:
  - id-disb-bank-connector-bss_policy_ap-southeast-3_production_live
  mode: live
  name: id-disb-bank-connector-bss-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-bss-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-bss-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    id-disb-bank-connector-bss_policy_ap-southeast-3_production_live:
      Statement:
      - Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:ListSubscriptionsByTopic
        Effect: Allow
        Resource:
        - arn:aws:sns:*:************:payouts_*
      - Action:
        - sqs:DeleteMessage
        - sqs:GetQueueUrl
        - sqs:ChangeMessageVisibility
        - sqs:SendMessageBatch
        - sqs:UntagQueue
        - sqs:ReceiveMessage
        - sqs:SendMessage
        - sqs:GetQueueAttributes
        - sqs:ListQueueTags
        - sqs:TagQueue
        - sqs:ListDeadLetterSourceQueues
        - sqs:DeleteMessageBatch
        - sqs:PurgeQueue
        - sqs:DeleteQueue
        - sqs:CreateQueue
        - sqs:ChangeMessageVisibilityBatch
        - sqs:SetQueueAttributes
        Effect: Allow
        Resource:
        - arn:aws:sqs:*:************:payouts_id-*-id-bss-dbc_prod-live_*
      - Action:
        - kms:Encrypt
        - kms:Decrypt
        - kms:GenerateDataKey*
        - kms:DescribeKey
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-1:************:key/0e63c4ba-98d5-4e3e-afd4-a73977ccec60
        - arn:aws:kms:ap-southeast-3:************:key/69fa210e-5895-4ac4-8b88-a1a7781f7daa
      Version: '2012-10-17'
    terraform-20230531075105454900000002:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-bss/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-bss
  sns_sqs_policy: null
- chamber_policy: terraform-20240718084931446500000009
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-bss-migration
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: id-disb-bank-connector-bss-migration-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: xendit/disbursement-bank-connector
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-bss-mig-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-bss-mig-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20240718084931446500000009:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-bss-migration/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-bss-migration
  sns_sqs_policy: null
- chamber_policy: terraform-20230925065100856600000002
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-cimb
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies:
  - id-disb-bank-connector-cimb_policy_ap-southeast-3_production_live
  mode: live
  name: id-disb-bank-connector-cimb-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-cimb-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-cimb-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    id-disb-bank-connector-cimb_policy_ap-southeast-3_production_live:
      Statement:
      - Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:ListSubscriptionsByTopic
        Effect: Allow
        Resource:
        - arn:aws:sns:*:************:payouts_*
      - Action:
        - sqs:DeleteMessage
        - sqs:GetQueueUrl
        - sqs:ChangeMessageVisibility
        - sqs:SendMessageBatch
        - sqs:UntagQueue
        - sqs:ReceiveMessage
        - sqs:SendMessage
        - sqs:GetQueueAttributes
        - sqs:ListQueueTags
        - sqs:TagQueue
        - sqs:ListDeadLetterSourceQueues
        - sqs:DeleteMessageBatch
        - sqs:PurgeQueue
        - sqs:DeleteQueue
        - sqs:CreateQueue
        - sqs:ChangeMessageVisibilityBatch
        - sqs:SetQueueAttributes
        Effect: Allow
        Resource:
        - arn:aws:sqs:*:************:payouts_id-*-id-cimb-dbc_prod-live_*
      - Action:
        - kms:Encrypt
        - kms:Decrypt
        - kms:GenerateDataKey*
        - kms:DescribeKey
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-1:************:key/0e63c4ba-98d5-4e3e-afd4-a73977ccec60
        - arn:aws:kms:ap-southeast-3:************:key/69fa210e-5895-4ac4-8b88-a1a7781f7daa
      Version: '2012-10-17'
    terraform-20230925065100856600000002:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-cimb/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-cimb
  sns_sqs_policy: null
- chamber_policy: terraform-20240718084930925400000008
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-cimb-migration
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: id-disb-bank-connector-cimb-migration-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: xendit/disbursement-bank-connector
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-cimb-mig-live-prod-ap-se-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-cimb-mig-live-prod-ap-se-3
  role_path: /
  role_policy:
    terraform-20240718084930925400000008:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-cimb-migration/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-cimb-migration
  sns_sqs_policy: null
- chamber_policy: terraform-20240718084930696100000005
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-dbs
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: id-disb-bank-connector-dbs-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-dbs-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-dbs-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20240718084930696100000005:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-dbs/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
    terraform-20240718094550900700000002:
      Statement:
      - Action:
        - sqs:ReceiveMessage
        - sqs:ListQueueTags
        - sqs:ListDeadLetterSourceQueues
        - sqs:GetQueueUrl
        - sqs:GetQueueAttributes
        - sqs:DeleteMessage
        - sqs:ChangeMessageVisibilityBatch
        - sqs:ChangeMessageVisibility
        Effect: Allow
        Resource:
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-dbc_prod-live_validate-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-dbc_prod-live_route-chg-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-dbc_prod-live_retry-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-dbc_prod-live_recon-txi-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-dbc_prod-live_recon-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-dbc_prod-live_inq-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-dbc_prod-live_exec-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-dbc_prod-live_create-queue
      - Action:
        - sns:Publish
        - sns:ListSubscriptionsByTopic
        - sns:GetTopicAttributes
        Effect: Allow
        Resource:
        - arn:aws:sns:ap-southeast-3:************:payouts_idr-droutes_prod-live-topic
        - arn:aws:sns:ap-southeast-3:************:payouts_idr-drecons_prod-live-topic
        - arn:aws:sns:ap-southeast-3:************:payouts_idr-dns_prod-live-topic
        - arn:aws:sns:ap-southeast-3:************:payouts_idr-dbc_prod-live-topic
        - arn:aws:sns:ap-southeast-3:************:payouts_id-syf-id-dbs-dbc_prod-live-topic
      - Action:
        - kms:GenerateDataKey*
        - kms:Encrypt
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-3:************:key/69fa210e-5895-4ac4-8b88-a1a7781f7daa
        - arn:aws:kms:ap-southeast-1:************:key/0e63c4ba-98d5-4e3e-afd4-a73977ccec60
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-dbs
  sns_sqs_policy: terraform-20240718094550900700000002
- chamber_policy: terraform-20240718084930689600000004
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-dbs-rtgs
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: id-disb-bank-connector-dbs-rtgs-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-dbs-rtgs-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-dbs-rtgs-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20240718084930689600000004:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-dbs-rtgs/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
    terraform-20240718094550900600000001:
      Statement:
      - Action:
        - sqs:ReceiveMessage
        - sqs:ListQueueTags
        - sqs:ListDeadLetterSourceQueues
        - sqs:GetQueueUrl
        - sqs:GetQueueAttributes
        - sqs:DeleteMessage
        - sqs:ChangeMessageVisibilityBatch
        - sqs:ChangeMessageVisibility
        Effect: Allow
        Resource:
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-rtgs-dbc_prod-live_validate-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-rtgs-dbc_prod-live_route-chg-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-rtgs-dbc_prod-live_retry-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-rtgs-dbc_prod-live_recon-txi-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-rtgs-dbc_prod-live_recon-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-rtgs-dbc_prod-live_inq-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-rtgs-dbc_prod-live_exec-queue
        - arn:aws:sqs:ap-southeast-3:************:payouts_id-syf-id-dbs-rtgs-dbc_prod-live_create-queue
      - Action:
        - sns:Publish
        - sns:ListSubscriptionsByTopic
        - sns:GetTopicAttributes
        Effect: Allow
        Resource:
        - arn:aws:sns:ap-southeast-3:************:payouts_idr-droutes_prod-live-topic
        - arn:aws:sns:ap-southeast-3:************:payouts_idr-drecons_prod-live-topic
        - arn:aws:sns:ap-southeast-3:************:payouts_idr-dns_prod-live-topic
        - arn:aws:sns:ap-southeast-3:************:payouts_idr-dbc_prod-live-topic
        - arn:aws:sns:ap-southeast-3:************:payouts_id-syf-id-dbs-rtgs-dbc_prod-live-topic
      - Action:
        - kms:GenerateDataKey*
        - kms:Encrypt
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-3:************:key/69fa210e-5895-4ac4-8b88-a1a7781f7daa
        - arn:aws:kms:ap-southeast-1:************:key/0e63c4ba-98d5-4e3e-afd4-a73977ccec60
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-dbs-rtgs
  sns_sqs_policy: terraform-20240718094550900600000001
- chamber_policy: terraform-20230807062517237000000002
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-mandiri
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies:
  - id-disb-bank-connector-mandiri_policy_ap-southeast-3_production_live
  mode: live
  name: id-disb-bank-connector-mandiri-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-mandiri-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-mandiri-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    id-disb-bank-connector-mandiri_policy_ap-southeast-3_production_live:
      Statement:
      - Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:ListSubscriptionsByTopic
        Effect: Allow
        Resource:
        - arn:aws:sns:*:************:payouts_*
      - Action:
        - sqs:DeleteMessage
        - sqs:GetQueueUrl
        - sqs:ChangeMessageVisibility
        - sqs:SendMessageBatch
        - sqs:UntagQueue
        - sqs:ReceiveMessage
        - sqs:SendMessage
        - sqs:GetQueueAttributes
        - sqs:ListQueueTags
        - sqs:TagQueue
        - sqs:ListDeadLetterSourceQueues
        - sqs:DeleteMessageBatch
        - sqs:PurgeQueue
        - sqs:DeleteQueue
        - sqs:CreateQueue
        - sqs:ChangeMessageVisibilityBatch
        - sqs:SetQueueAttributes
        Effect: Allow
        Resource:
        - arn:aws:sqs:*:************:payouts_id-*-id-mandiri-dbc_prod-live_*
      - Action:
        - kms:Encrypt
        - kms:Decrypt
        - kms:GenerateDataKey*
        - kms:DescribeKey
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-1:************:key/0e63c4ba-98d5-4e3e-afd4-a73977ccec60
        - arn:aws:kms:ap-southeast-3:************:key/69fa210e-5895-4ac4-8b88-a1a7781f7daa
      Version: '2012-10-17'
    terraform-20230807062517237000000002:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-mandiri/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-mandiri
  sns_sqs_policy: null
- chamber_policy: terraform-2024071808493144850000000a
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-mandiri-migration
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: id-disb-bank-connector-mandiri-migration-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: xendit/disbursement-bank-connector
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-mandiri-mig-live-prod-ap-se-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-mandiri-mig-live-prod-ap-se-3
  role_path: /
  role_policy:
    terraform-2024071808493144850000000a:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-mandiri-migration/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-mandiri-migration
  sns_sqs_policy: null
- chamber_policy: terraform-20230614074349895800000002
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-permata-bp
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies:
  - id-disb-bank-connector-permata-bp_policy_ap-southeast-3_production_live
  mode: live
  name: id-disb-bank-connector-permata-bp-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-permata-bp-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-permata-bp-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    id-disb-bank-connector-permata-bp_policy_ap-southeast-3_production_live:
      Statement:
      - Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:ListSubscriptionsByTopic
        Effect: Allow
        Resource:
        - arn:aws:sns:*:************:payouts_*
      - Action:
        - sqs:DeleteMessage
        - sqs:GetQueueUrl
        - sqs:ChangeMessageVisibility
        - sqs:SendMessageBatch
        - sqs:UntagQueue
        - sqs:ReceiveMessage
        - sqs:SendMessage
        - sqs:GetQueueAttributes
        - sqs:ListQueueTags
        - sqs:TagQueue
        - sqs:ListDeadLetterSourceQueues
        - sqs:DeleteMessageBatch
        - sqs:PurgeQueue
        - sqs:DeleteQueue
        - sqs:CreateQueue
        - sqs:ChangeMessageVisibilityBatch
        - sqs:SetQueueAttributes
        Effect: Allow
        Resource:
        - arn:aws:sqs:*:************:payouts_id-*-id-permata-bill-payment-dbc_prod-live_*
      - Action:
        - kms:Encrypt
        - kms:Decrypt
        - kms:GenerateDataKey*
        - kms:DescribeKey
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-1:************:key/0e63c4ba-98d5-4e3e-afd4-a73977ccec60
        - arn:aws:kms:ap-southeast-3:************:key/69fa210e-5895-4ac4-8b88-a1a7781f7daa
      Version: '2012-10-17'
    terraform-20230614074349895800000002:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-permata-bp/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-permata-bp
  sns_sqs_policy: null
- chamber_policy: terraform-20240718084930698800000007
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-permata-bp-migration
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: id-disb-bank-connector-permata-bp-migration-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: xendit/disbursement-bank-connector
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-permata-bp-mig-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-permata-bp-mig-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20240718084930698800000007:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-permata-bp-migration/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-permata-bp-migration
  sns_sqs_policy: null
- chamber_policy: terraform-20230412064459126400000002
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-permata
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies:
  - id-disb-bank-connector-permata_policy_ap-southeast-3_production_live
  mode: live
  name: id-disb-bank-connector-permata-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-permata-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-permata-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    id-disb-bank-connector-permata_policy_ap-southeast-3_production_live:
      Statement:
      - Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:ListSubscriptionsByTopic
        Effect: Allow
        Resource:
        - arn:aws:sns:*:************:payouts_*
      - Action:
        - sqs:DeleteMessage
        - sqs:GetQueueUrl
        - sqs:ChangeMessageVisibility
        - sqs:SendMessageBatch
        - sqs:UntagQueue
        - sqs:ReceiveMessage
        - sqs:SendMessage
        - sqs:GetQueueAttributes
        - sqs:ListQueueTags
        - sqs:TagQueue
        - sqs:ListDeadLetterSourceQueues
        - sqs:DeleteMessageBatch
        - sqs:PurgeQueue
        - sqs:DeleteQueue
        - sqs:CreateQueue
        - sqs:ChangeMessageVisibilityBatch
        - sqs:SetQueueAttributes
        Effect: Allow
        Resource:
        - arn:aws:sqs:*:************:payouts_id-*-id-permata-dbc_prod-live_*
      - Action:
        - kms:Encrypt
        - kms:Decrypt
        - kms:GenerateDataKey*
        - kms:DescribeKey
        Effect: Allow
        Resource:
        - arn:aws:kms:ap-southeast-1:************:key/0e63c4ba-98d5-4e3e-afd4-a73977ccec60
        - arn:aws:kms:ap-southeast-3:************:key/69fa210e-5895-4ac4-8b88-a1a7781f7daa
      Version: '2012-10-17'
    terraform-20230412064459126400000002:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-permata/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-permata
  sns_sqs_policy: null
- chamber_policy: terraform-20240718084930696800000006
  chamber_services:
  - xendit-infra
  - service-discovery
  - id-disb-bank-connector-permata-migration
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: id-disb-bank-connector-permata-migration-live
  namespace: disbursement-live
  pci_scope: non_pci
  repo: xendit/disbursement-bank-connector
  role_arn: arn:aws:iam::************:role/id-disb-bank-connector-permata-mig-live-prod-ap-southeast-3
  role_max_session_duration: 3600
  role_name: id-disb-bank-connector-permata-mig-live-prod-ap-southeast-3
  role_path: /
  role_policy:
    terraform-20240718084930696800000006:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/id-disb-bank-connector-permata-migration/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: id-disb-bank-connector-permata-migration
  sns_sqs_policy: null
- chamber_policy: terraform-20240216070627721500000001
  chamber_services:
  - xendit-infra
  - url-shortener-service
  - service-discovery
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: development
  name: url-shortener-service-development
  namespace: invoice-dev
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/url-shortener-service-development-ap-southeast-3-non-pci
  role_max_session_duration: 3600
  role_name: url-shortener-service-development-ap-southeast-3-non-pci
  role_path: /
  role_policy:
    terraform-20240216070627721500000001:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/development/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/development/url-shortener-service/*
        - arn:aws:ssm:us-west-2:************:parameter/production/development/service-discovery/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: url-shortener-service
  sns_sqs_policy: null
- chamber_policy: terraform-20240220090630354000000003
  chamber_services:
  - xendit-infra
  - url-shortener-ui
  - service-discovery
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: development
  name: url-shortener-ui-development
  namespace: invoice-dev
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/url-shortener-ui-development-ap-southeast-3-non-pci
  role_max_session_duration: 3600
  role_name: url-shortener-ui-development-ap-southeast-3-non-pci
  role_path: /
  role_policy:
    terraform-20240220090630354000000003:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/development/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/development/url-shortener-ui/*
        - arn:aws:ssm:us-west-2:************:parameter/production/development/service-discovery/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: url-shortener-ui
  sns_sqs_policy: null
- chamber_policy: terraform-2024031907551163940000000c
  chamber_services:
  - xendit-infra
  - url-shortener-service
  - service-discovery
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: url-shortener-service-live
  namespace: invoice-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/url-shortener-service-live-ap-southeast-3-non-pci
  role_max_session_duration: 3600
  role_name: url-shortener-service-live-ap-southeast-3-non-pci
  role_path: /
  role_policy:
    terraform-2024031907551163940000000c:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/url-shortener-service/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: url-shortener-service
  sns_sqs_policy: null
- chamber_policy: terraform-2024031907551161620000000b
  chamber_services:
  - xendit-infra
  - url-shortener-ui
  - service-discovery
  clusters:
  - xnd-jk-prod-aws-0
  - xnd-jk-prod-aws-1
  custom_policies: []
  mode: live
  name: url-shortener-ui-live
  namespace: invoice-live
  pci_scope: non_pci
  repo: 'null'
  role_arn: arn:aws:iam::************:role/url-shortener-ui-live-ap-southeast-3-non-pci
  role_max_session_duration: 3600
  role_name: url-shortener-ui-live-ap-southeast-3-non-pci
  role_path: /
  role_policy:
    terraform-2024031907551161620000000b:
      Statement:
      - Action:
        - ssm:GetParametersByPath
        - ssm:GetParameters
        - kms:DescribeKey
        - kms:Decrypt
        Effect: Allow
        Resource:
        - arn:aws:ssm:us-west-2:************:parameter/production/live/xendit-infra/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/url-shortener-ui/*
        - arn:aws:ssm:us-west-2:************:parameter/production/live/service-discovery/*
        - arn:aws:kms:us-west-2:************:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
      - Action:
        - ssm:DescribeParameters
        - kms:ListKeys
        - kms:ListAliases
        Effect: Allow
        Resource:
        - '*'
      Version: '2012-10-17'
  service_name: url-shortener-ui
  sns_sqs_policy: null
