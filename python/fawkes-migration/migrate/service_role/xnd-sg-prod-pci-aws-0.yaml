  - namespace: api-dev-pci
    name: api-crypto-signatures-development
    mode: development
    repo: 'null'
    role_arn: arn:aws:iam::420361828844:role/api-crypto-signatures-development-production-pci
    service_name: api-crypto-signatures
    role_name: api-crypto-signatures-development-production-pci
    pci_scope: pci
    clusters:
      - xnd-sg-prod-pci-aws-0
      - xnd-sg-prod-pci-aws-0
    role_path: /
    role_max_session_duration: 3600
    role_policy:
      terraform-20220428084036205100000001:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - ssm:GetParametersByPath
              - ssm:GetParameters
              - kms:DescribeKey
              - kms:Decrypt
            Resource:
              - arn:aws:ssm:us-west-2:420361828844:parameter/production/development/xendit-infra/*
              - arn:aws:ssm:us-west-2:420361828844:parameter/production/development/service-discovery/*
              - arn:aws:ssm:us-west-2:420361828844:parameter/production/development/api-crypto-signatures/*
              - arn:aws:kms:us-west-2:420361828844:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
          - Effect: Allow
            Action:
              - ssm:DescribeParameters
              - kms:ListKeys
              - kms:ListAliases
            Resource:
              - '*'
    chamber_policy: terraform-20220428084036205100000001
    chamber_services:
      - xendit-infra
      - service-discovery
      - api-crypto-signatures
    chamber_modes:
      - development
      - development
      - development
    sns_sqs_policy: null
    custom_policies: []
  - namespace: api-dev-pci
    name: api-logs-service-development
    mode: development
    repo: 'null'
    role_arn: arn:aws:iam::420361828844:role/api-logs-service-development-production-pci
    service_name: api-logs-service
    role_name: api-logs-service-development-production-pci
    pci_scope: pci
    clusters:
      - xnd-sg-prod-pci-aws-0
      - xnd-sg-prod-pci-aws-0
    role_path: /
    role_max_session_duration: 3600
    role_policy:
      terraform-20220610072618762500000001:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - ssm:GetParametersByPath
              - ssm:GetParameters
              - kms:DescribeKey
              - kms:Decrypt
            Resource:
              - arn:aws:ssm:us-west-2:420361828844:parameter/production/development/xendit-infra/*
              - arn:aws:ssm:us-west-2:420361828844:parameter/production/development/service-discovery/*
              - arn:aws:ssm:us-west-2:420361828844:parameter/production/development/api-logs-service/*
              - arn:aws:kms:us-west-2:420361828844:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
          - Effect: Allow
            Action:
              - ssm:DescribeParameters
              - kms:ListKeys
              - kms:ListAliases
            Resource:
              - '*'
    chamber_policy: terraform-20220610072618762500000001
    chamber_services:
      - xendit-infra
      - service-discovery
      - api-logs-service
    chamber_modes:
      - development
      - development
      - development
    sns_sqs_policy: null
    custom_policies: []
  - namespace: cashpay-dev-pci
    name: money-in-healthcheck-development
    mode: development
    repo: 'null'
    role_arn: arn:aws:iam::420361828844:role/money-in-healthcheck-development-prod
    service_name: money-in-healthcheck
    role_name: money-in-healthcheck-development-prod
    pci_scope: pci
    clusters:
      - xnd-sg-prod-pci-aws-0
      - xnd-sg-prod-pci-aws-0
    role_path: /
    role_max_session_duration: 3600
    role_policy:
      terraform-20240801082610599600000001:
        Version: '2012-10-17'
        Statement:
          - Action:
              - ssm:GetParametersByPath
              - ssm:GetParameters
              - kms:DescribeKey
              - kms:Decrypt
            Effect: Allow
            Resource:
              - arn:aws:ssm:us-west-2:420361828844:parameter/production/development/xendit-infra/*
              - arn:aws:ssm:us-west-2:420361828844:parameter/production/development/service-discovery/*
              - arn:aws:ssm:us-west-2:420361828844:parameter/production/development/money-in-healthcheck/*
              - arn:aws:kms:us-west-2:420361828844:key/758b231e-04bd-4f52-b1fe-b9d3efe576f2
          - Action:
              - ssm:DescribeParameters
              - kms:ListKeys
              - kms:ListAliases
            Effect: Allow
            Resource:
              - '*'
    chamber_policy: terraform-20240801082610599600000001
    chamber_services:
      - xendit-infra
      - service-discovery
      - money-in-healthcheck
    chamber_modes:
      - development
      - development
      - development
    sns_sqs_policy: null
    custom_policies: []
