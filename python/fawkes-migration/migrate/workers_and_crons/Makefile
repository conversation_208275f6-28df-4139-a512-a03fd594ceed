aws_region = ap-southeast-1
aws_profile = xendit-staging
source_cluster = trident-staging-0
dest_cluster = xnd-sg-stg-aws-0
namespace = dev-platform-live
application = remittance-money-in-service-development
workload =

main_cmd = python main.py \
	--aws-region $(aws_region) \
	--aws-profile $(aws_profile) \
	--source-cluster $(source_cluster) \
	--dest-cluster $(dest_cluster) \
	--namespace $(namespace) \
	--application $(application) \
	$(if $(debug),--debug,) \

help:
	python main.py --help

# Print main command, useful for running your own sub-commands in shell but using the options defined above
# e.g. `$(make cmd) migrate --dry-run --workload foo`
cmd:
	@echo $(main_cmd)

repl:
	$(main_cmd) repl

list:
	$(main_cmd) list

migrate:
	$(main_cmd) migrate \
		$(if $(dry_run),--dry-run,) \
		$(if $(workload),--workload $(workload),)

test:
	pytest -v
