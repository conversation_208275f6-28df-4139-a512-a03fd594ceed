# Migration of Workers and Cronjobs

Interactive:

1. Run `python main.py <options> repl`. Give appropriate values for the options
  `--source-cluster`,
  `--dest-cluster`,
  `--aws-profile`,
  `--aws-region`,
  `--namespace`,
  and `--application`.
   - Alternatively, edit the corresponding configurations in the Makefile and run `make repl`.
1. In the interactive prompt, run these commands in order:
   1. `list` - Show worker and cron statuses in the clusters
   1. `backup` - Save a backup of workloads in current directory. If needed, use `rollback` to restore from backup.
   1. `migrate` - Migrate workers and crons between clusters.
      Idempotent, can be run multiple times to retry after encountering errors.
      - Add `--dry-run` to preview workloads to be migrated.
      - Add `--workload <workload>` to migrate one worker or cronjob only.

Non-interactive:

1. Run `python main.py <options>` as above but instead of `repl`, specify the sub-command to run. Mainly useful to repeat migration steps when debugging pod failures.
   - For convenience, `python main.py <options>` can also be replaced with `$(make cmd)` to generate and run the command based on Makefile configuration.
