import logging

import kubernetes
from .workload import Deployment, CronJob


class Cluster:
    def __init__(self, name, client_factory: kubernetes.client.ApiClient):
        self.name = name
        self.client_factory = client_factory
        self.namespace = None
        self.deployments = None
        self.cronjobs = None
        self.applications = None

    def loadFromApi(self, namespace):
        timeout = {"_request_timeout": (5, 60)}

        logging.debug("Retrieving applications")
        apps = kubernetes.client.CustomObjectsApi(
            self.client_factory()
        ).list_namespaced_custom_object(
            "argoproj.io", "v1alpha1", "argocd", "applications", **timeout
        )
        self.applications = {
            a["metadata"]["name"]: Application(self, a) for a in apps["items"]
        }

        logging.debug("Retrieving namespace")
        self.namespace = kubernetes.client.CoreV1Api(
            self.client_factory()
        ).read_namespace(namespace, **timeout)

        logging.debug("Retrieving deployments")
        deploys = kubernetes.client.AppsV1Api(
            self.client_factory()
        ).list_namespaced_deployment(namespace, **timeout)
        self.deployments = {d.metadata.name: Deployment(self, d) for d in deploys.items}

        logging.debug("Retrieving cronjobs")
        cronjobs = kubernetes.client.BatchV1Api(
            self.client_factory()
        ).list_namespaced_cron_job(namespace, **timeout)
        self.cronjobs = {cj.metadata.name: CronJob(self, cj) for cj in cronjobs.items}

        # Assign workloads to applications
        for k, v in self.deployments.items():
            if app := v.application_name:
                self.applications[app].deployments[k] = v
        for k, v in self.cronjobs.items():
            if app := v.application_name:
                self.applications[app].cronjobs[k] = v

    @property
    def worker_deployments(self):
        return {k: v for k, v in self.deployments.items() if k.endswith("-worker")}

    def saveSnapshot(self, application):
        snapshot = {
            "apiVersion": "v1",
            "kind": "List",
            "items": [
                self.client_factory.client.sanitize_for_serialization(d.model)
                | {"apiVersion": "apps/v1", "kind": "Deployment"}
                for d in self.applications[application].worker_deployments.values()
            ]
            + [
                self.client_factory.client.sanitize_for_serialization(cj.model)
                | {"apiVersion": "batch/v1", "kind": "CronJob"}
                for cj in self.applications[application].cronjobs.values()
            ],
        }
        return snapshot

    def rollbackFromSnapshot(self, snapshot, remove_label=None):
        patch_label = (
            {"metadata": {"labels": {"enable-workers-and-crons": None}}}
            if remove_label
            else {}
        )

        for item in snapshot["items"]:
            match item["kind"]:
                case "Deployment":
                    logging.info(
                        "{}: Setting deployment {} to {} replicas".format(
                            self.name,
                            item["metadata"]["name"],
                            item["spec"]["replicas"],
                        )
                    )
                    kubernetes.client.AppsV1Api(
                        self.client_factory()
                    ).patch_namespaced_deployment(
                        item["metadata"]["name"],
                        self.namespace.metadata.name,
                        {"spec": {"replicas": item["spec"]["replicas"]}} | patch_label,
                    )
                case "CronJob":
                    logging.info(
                        "{}: Setting cronjob {} to suspend={}".format(
                            self.name, item["metadata"]["name"], item["spec"]["suspend"]
                        )
                    )
                    kubernetes.client.BatchV1Api(
                        self.client_factory()
                    ).patch_namespaced_cron_job(
                        item["metadata"]["name"],
                        self.namespace.metadata.name,
                        {"spec": {"suspend": item["spec"]["suspend"]}} | patch_label,
                    )
                case _:
                    raise ValueError("Unknown object kind in snapshot")


class Application:
    def __init__(self, cluster, model):
        self.cluster = cluster
        self.model = model
        self.name = model["metadata"]["name"]
        self.deployments = {}
        self.cronjobs = {}

    @property
    def worker_deployments(self):
        return {k: v for k, v in self.deployments.items() if k.endswith("-worker")}
