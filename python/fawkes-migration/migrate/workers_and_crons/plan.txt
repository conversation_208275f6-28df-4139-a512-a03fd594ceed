An execution plan has been generated and is shown below.
Resource actions are indicated with the following symbols:
! update in-place
 <= read (data resources)

Terraform will perform the following actions:

  # aws_route_table.redis_routes will be updated in-place
! resource "aws_route_table" "redis_routes" {
        arn              = "arn:aws:ec2:ap-southeast-1:705506614808:route-table/rtb-03da1446cc57b4594"
        id               = "rtb-03da1446cc57b4594"
        owner_id         = "705506614808"
        propagating_vgws = []
        route            = [
            {
                carrier_gateway_id         = ""
                cidr_block                 = "**********/16"
                destination_prefix_list_id = ""
                egress_only_gateway_id     = ""
                gateway_id                 = ""
                instance_id                = ""
                ipv6_cidr_block            = ""
                local_gateway_id           = ""
                nat_gateway_id             = ""
                network_interface_id       = ""
                transit_gateway_id         = ""
                vpc_endpoint_id            = ""
                vpc_peering_connection_id  = "pcx-029b43a077f0e0e4a"
            },
            {
                carrier_gateway_id         = ""
                cidr_block                 = "**********/16"
                destination_prefix_list_id = ""
                egress_only_gateway_id     = ""
                gateway_id                 = ""
                instance_id                = ""
                ipv6_cidr_block            = ""
                local_gateway_id           = ""
                nat_gateway_id             = ""
                network_interface_id       = ""
                transit_gateway_id         = ""
                vpc_endpoint_id            = ""
                vpc_peering_connection_id  = "pcx-030fa378c962dd47e"
            },
            {
                carrier_gateway_id         = ""
                cidr_block                 = "*********/16"
                destination_prefix_list_id = ""
                egress_only_gateway_id     = ""
                gateway_id                 = ""
                instance_id                = ""
                ipv6_cidr_block            = ""
                local_gateway_id           = ""
                nat_gateway_id             = ""
                network_interface_id       = ""
                transit_gateway_id         = ""
                vpc_endpoint_id            = ""
                vpc_peering_connection_id  = "pcx-0548fd1d8d64678ff"
            },
        ]
!       tags             = {
            "Environment" = "staging"
            "Mode"        = "development"
            "Name"        = "direct-debit-staging-development-redis"
-           "Owner"       = "team-direct-debit" -> null
-           "cost:owner"  = "team-direct-debit" -> null
-           "cost:team"   = "team-direct-debit" -> null
        }
!       tags_all         = {
            "Environment"      = "staging"
            "ManagedBy"        = "terraform"
            "Mode"             = "development"
            "Name"             = "direct-debit-staging-development-redis"
!           "Owner"            = "team-direct-debit" -> "team-payments-beta"
            "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-dev"
            "cost:environment" = "staging"
            "cost:mode"        = "development"
!           "cost:owner"       = "team-direct-debit" -> "team-payments-beta"
            "cost:product"     = "direct-debit"
            "cost:service"     = "direct-debit"
!           "cost:team"        = "team-direct-debit" -> "team-payments-beta"
        }
        vpc_id           = "vpc-0e767d1d42329247e"
    }

  # aws_ssm_parameter.redis_auth will be updated in-place
! resource "aws_ssm_parameter" "redis_auth" {
        arn         = "arn:aws:ssm:us-west-2:705506614808:parameter/staging/development/iss/direct-debit/redis-auth"
        data_type   = "text"
        description = "Redis Auth Token"
        id          = "/staging/development/iss/direct-debit/redis-auth"
        key_id      = "alias/parameter_store_key"
        name        = "/staging/development/iss/direct-debit/redis-auth"
!       tags        = {
-           "Owner"      = "team-direct-debit" -> null
-           "cost:owner" = "team-direct-debit" -> null
-           "cost:team"  = "team-direct-debit" -> null
        }
!       tags_all    = {
            "ManagedBy"        = "terraform"
!           "Owner"            = "team-direct-debit" -> "team-payments-beta"
            "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-dev"
            "cost:environment" = "staging"
            "cost:mode"        = "development"
!           "cost:owner"       = "team-direct-debit" -> "team-payments-beta"
            "cost:product"     = "direct-debit"
            "cost:service"     = "direct-debit"
!           "cost:team"        = "team-direct-debit" -> "team-payments-beta"
        }
        tier        = "Standard"
        type        = "SecureString"
        value       = (sensitive value)
        version     = 1
    }

  # module.redis.data.aws_ssm_parameter.redis_auth[0] will be read during apply
  # (config refers to values not yet known)
 <= data "aws_ssm_parameter" "redis_auth"  {
!       arn             = "arn:aws:ssm:us-west-2:705506614808:parameter/staging/development/iss/direct-debit/redis-auth" -> (known after apply)
!       id              = "/staging/development/iss/direct-debit/redis-auth" -> (known after apply)
        name            = "/staging/development/iss/direct-debit/redis-auth"
!       type            = "SecureString" -> (known after apply)
!       value           = (sensitive value)
!       version         = 1 -> (known after apply)
-       with_decryption = true -> null
    }

  # module.redis.aws_subnet.redis_subnets[0] will be updated in-place
! resource "aws_subnet" "redis_subnets" {
        arn                                            = "arn:aws:ec2:ap-southeast-1:705506614808:subnet/subnet-0dee26031947ac083"
        assign_ipv6_address_on_creation                = false
        availability_zone                              = "ap-southeast-1a"
        availability_zone_id                           = "apse1-az2"
        cidr_block                                     = "**********/28"
        enable_dns64                                   = false
        enable_resource_name_dns_a_record_on_launch    = false
        enable_resource_name_dns_aaaa_record_on_launch = false
        id                                             = "subnet-0dee26031947ac083"
        ipv6_native                                    = false
        map_customer_owned_ip_on_launch                = false
        map_public_ip_on_launch                        = false
        owner_id                                       = "705506614808"
        private_dns_hostname_type_on_launch            = "ip-name"
        tags                                           = {
            "Environment" = "staging"
            "Mode"        = "development"
            "Name"        = "direct-debit-staging-development-redis-1"
        }
!       tags_all                                       = {
            "Environment"      = "staging"
+           "ManagedBy"        = "terraform"
            "Mode"             = "development"
            "Name"             = "direct-debit-staging-development-redis-1"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-dev"
+           "cost:environment" = "staging"
+           "cost:mode"        = "development"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }
        vpc_id                                         = "vpc-0e767d1d42329247e"
    }

  # module.redis.aws_subnet.redis_subnets[1] will be updated in-place
! resource "aws_subnet" "redis_subnets" {
        arn                                            = "arn:aws:ec2:ap-southeast-1:705506614808:subnet/subnet-00c753cf91e6854ea"
        assign_ipv6_address_on_creation                = false
        availability_zone                              = "ap-southeast-1b"
        availability_zone_id                           = "apse1-az1"
        cidr_block                                     = "**********/28"
        enable_dns64                                   = false
        enable_resource_name_dns_a_record_on_launch    = false
        enable_resource_name_dns_aaaa_record_on_launch = false
        id                                             = "subnet-00c753cf91e6854ea"
        ipv6_native                                    = false
        map_customer_owned_ip_on_launch                = false
        map_public_ip_on_launch                        = false
        owner_id                                       = "705506614808"
        private_dns_hostname_type_on_launch            = "ip-name"
        tags                                           = {
            "Environment" = "staging"
            "Mode"        = "development"
            "Name"        = "direct-debit-staging-development-redis-2"
        }
!       tags_all                                       = {
            "Environment"      = "staging"
+           "ManagedBy"        = "terraform"
            "Mode"             = "development"
            "Name"             = "direct-debit-staging-development-redis-2"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-dev"
+           "cost:environment" = "staging"
+           "cost:mode"        = "development"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }
        vpc_id                                         = "vpc-0e767d1d42329247e"
    }

  # module.redis.aws_subnet.redis_subnets[2] will be updated in-place
! resource "aws_subnet" "redis_subnets" {
        arn                                            = "arn:aws:ec2:ap-southeast-1:705506614808:subnet/subnet-0698ff9540c34145e"
        assign_ipv6_address_on_creation                = false
        availability_zone                              = "ap-southeast-1c"
        availability_zone_id                           = "apse1-az3"
        cidr_block                                     = "**********/28"
        enable_dns64                                   = false
        enable_resource_name_dns_a_record_on_launch    = false
        enable_resource_name_dns_aaaa_record_on_launch = false
        id                                             = "subnet-0698ff9540c34145e"
        ipv6_native                                    = false
        map_customer_owned_ip_on_launch                = false
        map_public_ip_on_launch                        = false
        owner_id                                       = "705506614808"
        private_dns_hostname_type_on_launch            = "ip-name"
        tags                                           = {
            "Environment" = "staging"
            "Mode"        = "development"
            "Name"        = "direct-debit-staging-development-redis-3"
        }
!       tags_all                                       = {
            "Environment"      = "staging"
+           "ManagedBy"        = "terraform"
            "Mode"             = "development"
            "Name"             = "direct-debit-staging-development-redis-3"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-dev"
+           "cost:environment" = "staging"
+           "cost:mode"        = "development"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }
        vpc_id                                         = "vpc-0e767d1d42329247e"
    }

  # module.redis.module.redis.aws_elasticache_parameter_group.default[0] will be updated in-place
! resource "aws_elasticache_parameter_group" "default" {
        arn         = "arn:aws:elasticache:ap-southeast-1:705506614808:parametergroup:direct-debit-staging-development-redis"
        description = "Managed by Terraform"
        family      = "redis5.0"
        id          = "direct-debit-staging-development-redis"
        name        = "direct-debit-staging-development-redis"
        tags        = {}
!       tags_all    = {
+           "ManagedBy"        = "terraform"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-dev"
+           "cost:environment" = "staging"
+           "cost:mode"        = "development"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }

        parameter {
            name  = "notify-keyspace-events"
            value = "AE"
        }
    }

  # module.redis.module.redis.aws_elasticache_replication_group.default[0] will be updated in-place
! resource "aws_elasticache_replication_group" "default" {
        apply_immediately             = true
        arn                           = "arn:aws:elasticache:ap-southeast-1:705506614808:replicationgroup:direct-debit-staging-development-redis"
        at_rest_encryption_enabled    = true
!       auth_token                    = (sensitive value)
        auto_minor_version_upgrade    = true
        automatic_failover_enabled    = false
        availability_zones            = [
            "ap-southeast-1a",
        ]
        cluster_enabled               = false
        data_tiering_enabled          = false
        engine                        = "redis"
        engine_version                = "5.0.6"
        engine_version_actual         = "5.0.6"
        id                            = "direct-debit-staging-development-redis"
        maintenance_window            = "sun:19:00-sun:20:00"
        member_clusters               = [
            "direct-debit-staging-development-redis-001",
        ]
        multi_az_enabled              = false
        node_type                     = "cache.t3.micro"
        number_cache_clusters         = 1
        parameter_group_name          = "direct-debit-staging-development-redis"
        port                          = 6379
        primary_endpoint_address      = "master.direct-debit-staging-development-redis.xdm6nb.apse1.cache.amazonaws.com"
        reader_endpoint_address       = "replica.direct-debit-staging-development-redis.xdm6nb.apse1.cache.amazonaws.com"
        replication_group_description = "direct-debit-staging-development-redis"
        replication_group_id          = "direct-debit-staging-development-redis"
        security_group_ids            = [
            "sg-0574dfeef4f85e4be",
        ]
        security_group_names          = []
        snapshot_retention_limit      = 7
        snapshot_window               = "18:00-19:00"
        subnet_group_name             = "direct-debit-staging-development-redis"
!       tags                          = {
            "Attributes"  = "redis"
            "Environment" = "staging"
            "Mode"        = "development"
            "Name"        = "direct-debit-staging-development-redis"
-           "cost_owner"  = "team-payment-beta" -> null
        }
!       tags_all                      = {
            "Attributes"       = "redis"
            "Environment"      = "staging"
+           "ManagedBy"        = "terraform"
            "Mode"             = "development"
            "Name"             = "direct-debit-staging-development-redis"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-dev"
+           "cost:environment" = "staging"
+           "cost:mode"        = "development"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
-           "cost_owner"       = "team-payment-beta" -> null
        }
        transit_encryption_enabled    = true
        user_group_ids                = []

        cluster_mode {
            num_node_groups         = 1
            replicas_per_node_group = 0
        }
    }

  # module.redis.module.redis.aws_elasticache_subnet_group.default[0] will be updated in-place
! resource "aws_elasticache_subnet_group" "default" {
        arn         = "arn:aws:elasticache:ap-southeast-1:705506614808:subnetgroup:direct-debit-staging-development-redis"
        description = "Managed by Terraform"
        id          = "direct-debit-staging-development-redis"
        name        = "direct-debit-staging-development-redis"
        subnet_ids  = [
            "subnet-00c753cf91e6854ea",
            "subnet-0698ff9540c34145e",
            "subnet-0dee26031947ac083",
        ]
        tags        = {}
!       tags_all    = {
+           "ManagedBy"        = "terraform"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-dev"
+           "cost:environment" = "staging"
+           "cost:mode"        = "development"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }
    }

  # module.redis.module.redis.aws_security_group.default[0] will be updated in-place
! resource "aws_security_group" "default" {
        arn                    = "arn:aws:ec2:ap-southeast-1:705506614808:security-group/sg-0574dfeef4f85e4be"
        description            = "Managed by Terraform"
        egress                 = [
            {
                cidr_blocks      = [
                    "0.0.0.0/0",
                ]
                description      = "Allow all egress traffic"
                from_port        = 0
                ipv6_cidr_blocks = []
                prefix_list_ids  = []
                protocol         = "-1"
                security_groups  = []
                self             = false
                to_port          = 0
            },
        ]
        id                     = "sg-0574dfeef4f85e4be"
        ingress                = [
            {
                cidr_blocks      = [
                    "**********/16",
                ]
                description      = "Allow inbound traffic from CIDR blocks"
                from_port        = 6379
                ipv6_cidr_blocks = []
                prefix_list_ids  = []
                protocol         = "tcp"
                security_groups  = []
                self             = false
                to_port          = 6379
            },
            {
                cidr_blocks      = [
                    "*********/24",
                    "**********/16",
                ]
                description      = "Allow inbound traffic from CIDR blocks"
                from_port        = 6379
                ipv6_cidr_blocks = []
                prefix_list_ids  = []
                protocol         = "tcp"
                security_groups  = []
                self             = false
                to_port          = 6379
            },
            {
                cidr_blocks      = [
                    "**********/24",
                ]
                description      = "Allow inbound traffic from CIDR blocks"
                from_port        = 6379
                ipv6_cidr_blocks = []
                prefix_list_ids  = []
                protocol         = "tcp"
                security_groups  = []
                self             = false
                to_port          = 6379
            },
        ]
        name                   = "direct-debit-staging-development-redis"
        owner_id               = "705506614808"
        revoke_rules_on_delete = false
!       tags                   = {
            "Attributes"  = "redis"
            "Environment" = "staging"
            "Mode"        = "development"
            "Name"        = "direct-debit-staging-development-redis"
-           "Owner"       = "team-direct-debit" -> null
-           "cost:owner"  = "team-direct-debit" -> null
-           "cost:team"   = "team-direct-debit" -> null
        }
!       tags_all               = {
            "Attributes"       = "redis"
            "Environment"      = "staging"
            "ManagedBy"        = "terraform"
            "Mode"             = "development"
            "Name"             = "direct-debit-staging-development-redis"
!           "Owner"            = "team-direct-debit" -> "team-payments-beta"
            "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-dev"
            "cost:environment" = "staging"
            "cost:mode"        = "development"
!           "cost:owner"       = "team-direct-debit" -> "team-payments-beta"
            "cost:product"     = "direct-debit"
            "cost:service"     = "direct-debit"
!           "cost:team"        = "team-direct-debit" -> "team-payments-beta"
        }
        vpc_id                 = "vpc-0e767d1d42329247e"
    }

Plan: 0 to add, 9 to change, 0 to destroy.An execution plan has been generated and is shown below.
Resource actions are indicated with the following symbols:
! update in-place
 <= read (data resources)

Terraform will perform the following actions:

  # aws_route_table.redis_routes will be updated in-place
! resource "aws_route_table" "redis_routes" {
        arn              = "arn:aws:ec2:ap-southeast-1:705506614808:route-table/rtb-001ab33f86db09adf"
        id               = "rtb-001ab33f86db09adf"
        owner_id         = "705506614808"
        propagating_vgws = []
        route            = [
            {
                carrier_gateway_id         = ""
                cidr_block                 = "**********/16"
                destination_prefix_list_id = ""
                egress_only_gateway_id     = ""
                gateway_id                 = ""
                instance_id                = ""
                ipv6_cidr_block            = ""
                local_gateway_id           = ""
                nat_gateway_id             = ""
                network_interface_id       = ""
                transit_gateway_id         = ""
                vpc_endpoint_id            = ""
                vpc_peering_connection_id  = "pcx-029b43a077f0e0e4a"
            },
            {
                carrier_gateway_id         = ""
                cidr_block                 = "**********/16"
                destination_prefix_list_id = ""
                egress_only_gateway_id     = ""
                gateway_id                 = ""
                instance_id                = ""
                ipv6_cidr_block            = ""
                local_gateway_id           = ""
                nat_gateway_id             = ""
                network_interface_id       = ""
                transit_gateway_id         = ""
                vpc_endpoint_id            = ""
                vpc_peering_connection_id  = "pcx-030fa378c962dd47e"
            },
            {
                carrier_gateway_id         = ""
                cidr_block                 = "*********/16"
                destination_prefix_list_id = ""
                egress_only_gateway_id     = ""
                gateway_id                 = ""
                instance_id                = ""
                ipv6_cidr_block            = ""
                local_gateway_id           = ""
                nat_gateway_id             = ""
                network_interface_id       = ""
                transit_gateway_id         = ""
                vpc_endpoint_id            = ""
                vpc_peering_connection_id  = "pcx-0548fd1d8d64678ff"
            },
        ]
!       tags             = {
            "Environment" = "staging"
            "Mode"        = "live"
            "Name"        = "direct-debit-staging-live-redis"
-           "Owner"       = "team-direct-debit" -> null
-           "cost:owner"  = "team-direct-debit" -> null
-           "cost:team"   = "team-direct-debit" -> null
        }
!       tags_all         = {
            "Environment"      = "staging"
            "ManagedBy"        = "terraform"
            "Mode"             = "live"
            "Name"             = "direct-debit-staging-live-redis"
!           "Owner"            = "team-direct-debit" -> "team-payments-beta"
            "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-live"
            "cost:environment" = "staging"
            "cost:mode"        = "live"
!           "cost:owner"       = "team-direct-debit" -> "team-payments-beta"
            "cost:product"     = "direct-debit"
            "cost:service"     = "direct-debit"
!           "cost:team"        = "team-direct-debit" -> "team-payments-beta"
        }
        vpc_id           = "vpc-0e767d1d42329247e"
    }

  # aws_ssm_parameter.redis_auth will be updated in-place
! resource "aws_ssm_parameter" "redis_auth" {
        arn         = "arn:aws:ssm:us-west-2:705506614808:parameter/staging/live/iss/direct-debit/redis-auth"
        data_type   = "text"
        description = "Redis Auth Token"
        id          = "/staging/live/iss/direct-debit/redis-auth"
        key_id      = "alias/parameter_store_key"
        name        = "/staging/live/iss/direct-debit/redis-auth"
!       tags        = {
-           "Owner"      = "team-direct-debit" -> null
-           "cost:owner" = "team-direct-debit" -> null
-           "cost:team"  = "team-direct-debit" -> null
        }
!       tags_all    = {
            "ManagedBy"        = "terraform"
!           "Owner"            = "team-direct-debit" -> "team-payments-beta"
            "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-live"
            "cost:environment" = "staging"
            "cost:mode"        = "live"
!           "cost:owner"       = "team-direct-debit" -> "team-payments-beta"
            "cost:product"     = "direct-debit"
            "cost:service"     = "direct-debit"
!           "cost:team"        = "team-direct-debit" -> "team-payments-beta"
        }
        tier        = "Standard"
        type        = "SecureString"
        value       = (sensitive value)
        version     = 2
    }

  # module.redis.data.aws_ssm_parameter.redis_auth will be read during apply
  # (config refers to values not yet known)
 <= data "aws_ssm_parameter" "redis_auth"  {
!       arn             = "arn:aws:ssm:us-west-2:705506614808:parameter/staging/live/iss/direct-debit/redis-auth" -> (known after apply)
!       id              = "/staging/live/iss/direct-debit/redis-auth" -> (known after apply)
        name            = "/staging/live/iss/direct-debit/redis-auth"
!       type            = "SecureString" -> (known after apply)
!       value           = (sensitive value)
!       version         = 2 -> (known after apply)
-       with_decryption = true -> null
    }

  # module.redis.aws_subnet.redis_subnets[0] will be updated in-place
! resource "aws_subnet" "redis_subnets" {
        arn                                            = "arn:aws:ec2:ap-southeast-1:705506614808:subnet/subnet-066febdf5cedebb23"
        assign_ipv6_address_on_creation                = false
        availability_zone                              = "ap-southeast-1a"
        availability_zone_id                           = "apse1-az2"
        cidr_block                                     = "***********/28"
        enable_dns64                                   = false
        enable_resource_name_dns_a_record_on_launch    = false
        enable_resource_name_dns_aaaa_record_on_launch = false
        id                                             = "subnet-066febdf5cedebb23"
        ipv6_native                                    = false
        map_customer_owned_ip_on_launch                = false
        map_public_ip_on_launch                        = false
        owner_id                                       = "705506614808"
        private_dns_hostname_type_on_launch            = "ip-name"
        tags                                           = {
            "Name" = "direct-debit-staging-live-redis-1"
        }
!       tags_all                                       = {
+           "ManagedBy"        = "terraform"
            "Name"             = "direct-debit-staging-live-redis-1"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-live"
+           "cost:environment" = "staging"
+           "cost:mode"        = "live"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }
        vpc_id                                         = "vpc-0e767d1d42329247e"
    }

  # module.redis.aws_subnet.redis_subnets[1] will be updated in-place
! resource "aws_subnet" "redis_subnets" {
        arn                                            = "arn:aws:ec2:ap-southeast-1:705506614808:subnet/subnet-0bc2826150ed6fce2"
        assign_ipv6_address_on_creation                = false
        availability_zone                              = "ap-southeast-1b"
        availability_zone_id                           = "apse1-az1"
        cidr_block                                     = "***********/28"
        enable_dns64                                   = false
        enable_resource_name_dns_a_record_on_launch    = false
        enable_resource_name_dns_aaaa_record_on_launch = false
        id                                             = "subnet-0bc2826150ed6fce2"
        ipv6_native                                    = false
        map_customer_owned_ip_on_launch                = false
        map_public_ip_on_launch                        = false
        owner_id                                       = "705506614808"
        private_dns_hostname_type_on_launch            = "ip-name"
        tags                                           = {
            "Name" = "direct-debit-staging-live-redis-2"
        }
!       tags_all                                       = {
+           "ManagedBy"        = "terraform"
            "Name"             = "direct-debit-staging-live-redis-2"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-live"
+           "cost:environment" = "staging"
+           "cost:mode"        = "live"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }
        vpc_id                                         = "vpc-0e767d1d42329247e"
    }

  # module.redis.aws_subnet.redis_subnets[2] will be updated in-place
! resource "aws_subnet" "redis_subnets" {
        arn                                            = "arn:aws:ec2:ap-southeast-1:705506614808:subnet/subnet-00246b49d679c7da3"
        assign_ipv6_address_on_creation                = false
        availability_zone                              = "ap-southeast-1c"
        availability_zone_id                           = "apse1-az3"
        cidr_block                                     = "***********/28"
        enable_dns64                                   = false
        enable_resource_name_dns_a_record_on_launch    = false
        enable_resource_name_dns_aaaa_record_on_launch = false
        id                                             = "subnet-00246b49d679c7da3"
        ipv6_native                                    = false
        map_customer_owned_ip_on_launch                = false
        map_public_ip_on_launch                        = false
        owner_id                                       = "705506614808"
        private_dns_hostname_type_on_launch            = "ip-name"
        tags                                           = {
            "Name" = "direct-debit-staging-live-redis-3"
        }
!       tags_all                                       = {
+           "ManagedBy"        = "terraform"
            "Name"             = "direct-debit-staging-live-redis-3"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-live"
+           "cost:environment" = "staging"
+           "cost:mode"        = "live"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }
        vpc_id                                         = "vpc-0e767d1d42329247e"
    }

  # module.redis.module.redis.aws_elasticache_parameter_group.default[0] will be updated in-place
! resource "aws_elasticache_parameter_group" "default" {
        arn         = "arn:aws:elasticache:ap-southeast-1:705506614808:parametergroup:direct-debit-staging-live-redis"
        description = "Managed by Terraform"
        family      = "redis5.0"
        id          = "direct-debit-staging-live-redis"
        name        = "direct-debit-staging-live-redis"
        tags        = {}
!       tags_all    = {
+           "ManagedBy"        = "terraform"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-live"
+           "cost:environment" = "staging"
+           "cost:mode"        = "live"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }
    }

  # module.redis.module.redis.aws_elasticache_replication_group.default[0] will be updated in-place
! resource "aws_elasticache_replication_group" "default" {
        apply_immediately             = true
        arn                           = "arn:aws:elasticache:ap-southeast-1:705506614808:replicationgroup:direct-debit-staging-live-redis"
        at_rest_encryption_enabled    = true
!       auth_token                    = (sensitive value)
        auto_minor_version_upgrade    = true
        automatic_failover_enabled    = false
        availability_zones            = [
            "ap-southeast-1a",
        ]
        cluster_enabled               = false
        data_tiering_enabled          = false
        engine                        = "redis"
        engine_version                = "5.0.6"
        engine_version_actual         = "5.0.6"
        id                            = "direct-debit-staging-live-redis"
        maintenance_window            = "sun:19:00-sun:20:00"
        member_clusters               = [
            "direct-debit-staging-live-redis-001",
        ]
        multi_az_enabled              = false
        node_type                     = "cache.t3.micro"
        number_cache_clusters         = 1
        parameter_group_name          = "direct-debit-staging-live-redis"
        port                          = 6379
        primary_endpoint_address      = "master.direct-debit-staging-live-redis.xdm6nb.apse1.cache.amazonaws.com"
        reader_endpoint_address       = "replica.direct-debit-staging-live-redis.xdm6nb.apse1.cache.amazonaws.com"
        replication_group_description = "direct-debit-staging-live-redis"
        replication_group_id          = "direct-debit-staging-live-redis"
        security_group_ids            = [
            "sg-008ac90809539c9d8",
        ]
        security_group_names          = []
        snapshot_retention_limit      = 7
        snapshot_window               = "18:00-19:00"
        subnet_group_name             = "direct-debit-staging-live-redis"
        tags                          = {
            "Attributes"  = "redis"
            "Environment" = "staging"
            "Mode"        = "live"
            "Name"        = "direct-debit-staging-live-redis"
        }
!       tags_all                      = {
            "Attributes"       = "redis"
            "Environment"      = "staging"
+           "ManagedBy"        = "terraform"
            "Mode"             = "live"
            "Name"             = "direct-debit-staging-live-redis"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-live"
+           "cost:environment" = "staging"
+           "cost:mode"        = "live"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }
        transit_encryption_enabled    = true
        user_group_ids                = []

        cluster_mode {
            num_node_groups         = 1
            replicas_per_node_group = 0
        }
    }

  # module.redis.module.redis.aws_elasticache_subnet_group.default[0] will be updated in-place
! resource "aws_elasticache_subnet_group" "default" {
        arn         = "arn:aws:elasticache:ap-southeast-1:705506614808:subnetgroup:direct-debit-staging-live-redis"
        description = "Managed by Terraform"
        id          = "direct-debit-staging-live-redis"
        name        = "direct-debit-staging-live-redis"
        subnet_ids  = [
            "subnet-00246b49d679c7da3",
            "subnet-066febdf5cedebb23",
            "subnet-0bc2826150ed6fce2",
        ]
        tags        = {}
!       tags_all    = {
+           "ManagedBy"        = "terraform"
+           "Owner"            = "team-payments-beta"
+           "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-live"
+           "cost:environment" = "staging"
+           "cost:mode"        = "live"
+           "cost:owner"       = "team-payments-beta"
+           "cost:product"     = "direct-debit"
+           "cost:service"     = "direct-debit"
+           "cost:team"        = "team-payments-beta"
        }
    }

  # module.redis.module.redis.aws_security_group.default[0] will be updated in-place
! resource "aws_security_group" "default" {
        arn                    = "arn:aws:ec2:ap-southeast-1:705506614808:security-group/sg-008ac90809539c9d8"
        description            = "Managed by Terraform"
        egress                 = [
            {
                cidr_blocks      = [
                    "0.0.0.0/0",
                ]
                description      = "Allow all egress traffic"
                from_port        = 0
                ipv6_cidr_blocks = []
                prefix_list_ids  = []
                protocol         = "-1"
                security_groups  = []
                self             = false
                to_port          = 0
            },
        ]
        id                     = "sg-008ac90809539c9d8"
        ingress                = [
            {
                cidr_blocks      = [
                    "**********/16",
                ]
                description      = "Allow inbound traffic from CIDR blocks"
                from_port        = 6379
                ipv6_cidr_blocks = []
                prefix_list_ids  = []
                protocol         = "tcp"
                security_groups  = []
                self             = false
                to_port          = 6379
            },
            {
                cidr_blocks      = [
                    "**********/16",
                    "*********/24",
                ]
                description      = "Allow inbound traffic from CIDR blocks"
                from_port        = 6379
                ipv6_cidr_blocks = []
                prefix_list_ids  = []
                protocol         = "tcp"
                security_groups  = []
                self             = false
                to_port          = 6379
            },
        ]
        name                   = "direct-debit-staging-live-redis"
        owner_id               = "705506614808"
        revoke_rules_on_delete = false
!       tags                   = {
            "Attributes"  = "redis"
            "Environment" = "staging"
            "Mode"        = "live"
            "Name"        = "direct-debit-staging-live-redis"
-           "Owner"       = "team-direct-debit" -> null
-           "cost:owner"  = "team-direct-debit" -> null
-           "cost:team"   = "team-direct-debit" -> null
        }
!       tags_all               = {
            "Attributes"       = "redis"
            "Environment"      = "staging"
            "ManagedBy"        = "terraform"
            "Mode"             = "live"
            "Name"             = "direct-debit-staging-live-redis"
!           "Owner"            = "team-direct-debit" -> "team-payments-beta"
            "TFProject"        = "github.com/xendit/xendit-infrastructure//terraform/aws/staging/ap-southeast-1/storage/redis/direct-debit-live"
            "cost:environment" = "staging"
            "cost:mode"        = "live"
!           "cost:owner"       = "team-direct-debit" -> "team-payments-beta"
            "cost:product"     = "direct-debit"
            "cost:service"     = "direct-debit"
!           "cost:team"        = "team-direct-debit" -> "team-payments-beta"
        }
        vpc_id                 = "vpc-0e767d1d42329247e"
    }

Plan: 0 to add, 9 to change, 0 to destroy.