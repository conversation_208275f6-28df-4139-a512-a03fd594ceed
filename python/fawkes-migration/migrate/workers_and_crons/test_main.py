import pytest
from unittest.mock import Mock, call
import copy

import kubernetes

from . import main
from . import cluster
from . import workload


def setup(replicas: list[int], suspends: list[bool]):
    namespace = "namespace1"
    G = main.Globals

    G.source_cluster = cluster.Cluster("cluster1", <PERSON>ck())
    c1 = G.source_cluster
    c1.namespace = namespace

    deployments = [
        Mock(
            **{
                "metadata.name": "app1-worker",
                "metadata.namespace": namespace,
                "metadata.labels": {f"argocd.{c1.name}": "app1"},
                "spec.replicas": r,
            }
        )
        for r in replicas
    ]
    cronjobs = [
        Mock(
            **{
                "metadata.name": "app1-cron",
                "metadata.namespace": namespace,
                "metadata.labels": {f"argocd.{c1.name}": "app1"},
                "spec.suspend": s,
            }
        )
        for s in suspends
    ]
    c1.deployments = {"app1-worker": workload.Deployment(c1, deployments[0])}
    c1.cronjobs = {"app1-cron": workload.CronJob(c1, cronjobs[0])}

    c1.applications = {"app1": cluster.Application(c1, {"metadata": {"name": "app1"}})}
    c1.applications["app1"].deployments = {k: v for k, v in c1.deployments.items()}
    c1.applications["app1"].cronjobs = {k: v for k, v in c1.cronjobs.items()}

    c2 = G.dest_cluster = copy.deepcopy(G.source_cluster)
    c2.name = "cluster2"
    c2.deployments["app1-worker"] = workload.Deployment(c2, deployments[1])
    c2.cronjobs["app1-cron"] = workload.CronJob(c2, cronjobs[1])

    main.Inputs.application = "app1"
    main.Inputs.namespace = namespace


@pytest.fixture()
def mock_k8s_client():
    return Mock()


@pytest.fixture(autouse=True)
def mock_defaults(monkeypatch, mock_k8s_client):
    monkeypatch.setattr(main, "load_cluster_data", Mock())
    monkeypatch.setattr(kubernetes, "client", mock_k8s_client)


def patch_deployment(cluster, namespace, name, replicas):
    return (
        call.AppsV1Api(cluster.client_factory())
        .patch_namespaced_deployment(
            name,
            namespace,
            {"spec": {"replicas": replicas}}
            if replicas > 0
            else {
                "spec": {"replicas": 0},
                "metadata": {"labels": {"enable-workers-and-crons": None}},
            },
        )
        .call_list()
    )


def patch_cronjob(cluster, namespace, name, suspend):
    return (
        call.BatchV1Api(cluster.client_factory())
        .patch_namespaced_cron_job(
            name,
            namespace,
            {"spec": {"suspend": False}}
            if not suspend
            else {
                "spec": {"suspend": True},
                "metadata": {"labels": {"enable-workers-and-crons": None}},
            },
        )
        .call_list()
    )


def test_rollback(mock_k8s_client):
    """Move workloads from dest_cluster to source_cluster."""
    setup([0, 2], [True, False])
    main.rollback.callback(False, None, True, True)

    assert mock_k8s_client.mock_calls == patch_deployment(
        main.Globals.source_cluster, "namespace1", "app1-worker", 2
    ) + patch_deployment(
        main.Globals.dest_cluster, "namespace1", "app1-worker", 0
    ) + patch_cronjob(
        main.Globals.dest_cluster, "namespace1", "app1-cron", True
    ) + patch_cronjob(main.Globals.source_cluster, "namespace1", "app1-cron", False)


def test_rollback_dry_run(mock_k8s_client):
    """dry_run should do nothing."""
    setup([0, 2], [True, False])
    main.rollback.callback(True, None, True, True)

    assert not mock_k8s_client.mock_calls


def test_rollback_workload_name(mock_k8s_client):
    """Only rollback the specified workload."""
    setup([0, 2], [True, False])
    main.rollback.callback(False, "app1-worker", True, True)

    assert mock_k8s_client.mock_calls == patch_deployment(
        main.Globals.source_cluster, "namespace1", "app1-worker", 2
    ) + patch_deployment(main.Globals.dest_cluster, "namespace1", "app1-worker", 0)


def test_rollback_idempotent(mock_k8s_client):
    """Already moved, disable destination workloads."""
    setup([2, 0], [False, True])
    main.rollback.callback(False, None, True, True)

    assert mock_k8s_client.mock_calls == patch_deployment(
        main.Globals.dest_cluster, "namespace1", "app1-worker", 0
    ) + patch_cronjob(
        main.Globals.dest_cluster, "namespace1", "app1-cron", True
    ) + patch_cronjob(main.Globals.source_cluster, "namespace1", "app1-cron", False)


def test_rollback_warn(monkeypatch, mock_k8s_client):
    """Both sides are disabled, show a warning."""
    monkeypatch.setattr(main, "logging", Mock(wraps=main.logging))
    setup([0, 0], [True, True])
    main.rollback.callback(False, None, True, True)

    main.logging.warning.assert_called_with(
        "Deployment %s has zero replicas in both clusters!"
        " Please confirm this is intentional, if not then scale up the source deployment.",
        "app1-worker",
    )
    assert mock_k8s_client.mock_calls == patch_cronjob(
        main.Globals.dest_cluster, "namespace1", "app1-cron", True
    ) + patch_cronjob(main.Globals.source_cluster, "namespace1", "app1-cron", False)
