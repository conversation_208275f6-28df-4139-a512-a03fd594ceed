import asyncio
import logging

import kubernetes
import kubernetes_asyncio


class Workload:
    _m = None
    cluster = None

    @property
    def application_name(self):
        if self._m.metadata.labels is None:
            return None

        argocd_labels = [
            k for k in self._m.metadata.labels.keys() if k.startswith("argocd")
        ]
        if not argocd_labels:
            return None
        assert (
            len(argocd_labels) == 1
        ), f"Found multiple argocd labels on {self._m.metadata.name}"
        return self._m.metadata.labels[argocd_labels[0]]

    @property
    def model(self):
        return self._m

    @model.setter
    def model(self, m):
        self._m = m

    @property
    def client_factory(self):
        return self.cluster.client_factory

    @property
    def name(self):
        return self._m.metadata.name


class Deployment(Workload):
    def __init__(self, cluster, data: kubernetes.client.models.V1Deployment):
        self._m = data
        self.desired_replicas = data.spec.replicas
        self.cluster = cluster

    def scale(self, replicas, set_label=None, remove_label=None):
        assert not (
            set_label and remove_label
        ), "Cannot set_label and remove_label simultaneously"

        logging.info(
            f"{self.cluster.name}: Scaling deployment {self._m.metadata.name} to {replicas} replicas"
        )

        patch_label = {}
        if set_label:
            patch_label = {"metadata": {"labels": {"enable-workers-and-crons": "true"}}}
        elif remove_label:
            patch_label = {"metadata": {"labels": {"enable-workers-and-crons": None}}}

        kubernetes.client.AppsV1Api(self.client_factory()).patch_namespaced_deployment(
            self._m.metadata.name,
            self._m.metadata.namespace,
            {"spec": {"replicas": replicas}} | patch_label,
        )
        self.desired_replicas = replicas
        self._m.spec.replicas = replicas

    def pods_available(self):
        replicas = [
            self._m.status.available_replicas,
            self._m.status.replicas,
            self._m.status.updated_replicas,
            self._m.spec.replicas,
            # self.desired_replicas,
        ]
        # Status attrs may be None, convert to 0
        replicas = [r or 0 for r in replicas]
        logging.debug(replicas)
        return all(replicas[0] == v for v in replicas[1:])

    async def watch_pods_available(
        self,
        list_resource_version=None,
        timeout=None,
    ):
        async_client = self.client_factory.get_async_client()

        async with async_client:
            apps = kubernetes_asyncio.client.AppsV1Api(async_client)
            async with kubernetes_asyncio.watch.Watch().stream(
                apps.list_namespaced_deployment,
                self._m.metadata.namespace,
                field_selector=f"metadata.name={self._m.metadata.name}",
                resource_version=list_resource_version,
                timeout_seconds=timeout,
            ) as stream:
                async for event in stream:
                    if event["type"] not in ["ADDED", "MODIFIED"]:
                        logging.warning(f'Ignoring event type {event["type"]}')
                        continue
                    # This replaces the kubernetes.client.V1Deployment with kubernetes_asyncio.client.V1Deployment
                    self._m = event["object"]
                    logging.debug(self._m.status)
                    logging.info(
                        "%s/%s replicas available",
                        self._m.status.available_replicas,
                        self._m.status.replicas,
                    )

                    yield self.pods_available()

    async def wait_pods_available(
        self, list_resource_version=None, fail_timeout=60, success_timeout=15
    ):
        logging.info(
            f"Waiting for deployment {self._m.metadata.name} to be fully available, timeout in {fail_timeout}s"
        )

        try:
            pods_available = False
            async with asyncio.timeout(fail_timeout) as fail_timer, asyncio.timeout(
                None
            ) as success_timer:
                fail_when = fail_timer.when()
                async for pods_available in self.watch_pods_available(
                    list_resource_version, timeout=fail_timeout
                ):
                    if pods_available:
                        if self.desired_replicas == 0:
                            return True
                        else:
                            # Some workers don't have health checks, pods can go from available -> unavailable. Wait for pod status to stabilize.
                            logging.info(
                                "Pods fully available, waiting another %ss",
                                success_timeout,
                            )
                            fail_timer.reschedule(None)
                            success_timer.reschedule(
                                asyncio.get_event_loop().time() + success_timeout
                            )
                    else:
                        fail_timer.reschedule(fail_when)
                        success_timer.reschedule(None)

        except asyncio.TimeoutError:
            if pods_available:
                return True
            else:
                raise DeploymentUnavailableError(
                    "Migration failed, please check pod status."
                )

    @property
    def containers(self):
        return [c for c in self._m.spec.template.spec.containers]


class DeploymentUnavailableError(Exception):
    pass


class CronJob(Workload):
    def __init__(self, cluster, data: kubernetes.client.models.V1CronJob):
        self._m = data
        self.cluster = cluster

    def set_suspended(self, suspend: bool, set_label=None, remove_label=None):
        assert not (
            set_label and remove_label
        ), "Cannot set_label and remove_label simultaneously"

        logging.info(
            "%s: Setting cronjob %s suspend=%s",
            self.cluster.name,
            self._m.metadata.name,
            suspend,
        )

        patch_label = {}
        if set_label:
            patch_label = {"metadata": {"labels": {"enable-workers-and-crons": "true"}}}
        elif remove_label:
            patch_label = {"metadata": {"labels": {"enable-workers-and-crons": None}}}

        kubernetes.client.BatchV1Api(self.client_factory()).patch_namespaced_cron_job(
            self._m.metadata.name,
            self._m.metadata.namespace,
            {"spec": {"suspend": suspend}} | patch_label,
        )
        self._m.spec.suspend = suspend
