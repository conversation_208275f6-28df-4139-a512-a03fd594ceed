import functools
import logging

import kubernetes

from k8s_client import K8SClientFactory
from model.workload import Namespace

class Cluster:
    def __init__(self, name, client_factory: K8SClientFactory):
        self.name = name
        self.client_factory = client_factory
        self.namespaces = {}

    @functools.cached_property
    def applications(self):
        logging.debug("Retrieving applications")
        apps = self.api_call(
            kubernetes.client.CustomObjectsApi, "list_namespaced_custom_object",
            "argoproj.io", "v1alpha1", "argocd", "applications"
        )
        return {
            a["metadata"]["name"]: Application(self, a) for a in apps["items"]
        }

    def get_namespace(self, name):
        return self.namespaces.setdefault(name, Namespace(self, name))

    @property
    def client(self) -> kubernetes.client.ApiClient:
        return self.client_factory()

    @property
    def dynamic_client(self):
        return kubernetes.dynamic.DynamicClient(self.client)

    def get_api_resource(self, **kwargs):
        return self.dynamic_client.resources.get(**kwargs)

    @functools.cache
    def get_resource_instances(self, resource : kubernetes.dynamic.Resource, **kwargs):
        return resource.get(**kwargs)

    @functools.cache
    def api_call(self, api, method, *args, **kwargs):
        return getattr(api(self.client), method)(*args, _request_timeout=(5, 60), **kwargs)

    @property
    def worker_deployments(self):
        return {k: v for k, v in self.deployments.items() if k.endswith("-worker")}

    def saveSnapshot(self, application):
        snapshot = {
            "apiVersion": "v1",
            "kind": "List",
            "items": [
                self.client_factory.client.sanitize_for_serialization(d.model)
                | {"apiVersion": "apps/v1", "kind": "Deployment"}
                for d in self.applications[application].worker_deployments.values()
            ]
            + [
                self.client_factory.client.sanitize_for_serialization(cj.model)
                | {"apiVersion": "batch/v1", "kind": "CronJob"}
                for cj in self.applications[application].cronjobs.values()
            ],
        }
        return snapshot

    def rollbackFromSnapshot(self, snapshot, remove_label=None):
        patch_label = (
            {"metadata": {"labels": {"enable-workers-and-crons": None}}}
            if remove_label
            else {}
        )

        for item in snapshot["items"]:
            match item["kind"]:
                case "Deployment":
                    logging.info(
                        "{}: Setting deployment {} to {} replicas".format(
                            self.name,
                            item["metadata"]["name"],
                            item["spec"]["replicas"],
                        )
                    )
                    kubernetes.client.AppsV1Api(
                        self.client
                    ).patch_namespaced_deployment(
                        item["metadata"]["name"],
                        self.namespace.metadata.name,
                        {"spec": {"replicas": item["spec"]["replicas"]}} | patch_label,
                    )
                case "CronJob":
                    logging.info(
                        "{}: Setting cronjob {} to suspend={}".format(
                            self.name, item["metadata"]["name"], item["spec"]["suspend"]
                        )
                    )
                    kubernetes.client.BatchV1Api(
                        self.client
                    ).patch_namespaced_cron_job(
                        item["metadata"]["name"],
                        self.namespace.metadata.name,
                        {"spec": {"suspend": item["spec"]["suspend"]}} | patch_label,
                    )
                case _:
                    raise ValueError("Unknown object kind in snapshot")


class Application:
    def __init__(self, cluster, model):
        self.cluster = cluster
        self.model = model
        self.name = model["metadata"]["name"]

    @property
    def target_namespace(self):
        return self.model["spec"]["destination"]["namespace"]

    @property
    def deployments(self):
        return {
            r["name"]: self.cluster.get_namespace(self.target_namespace).deployments[r["name"]]
            for r in self.resources if r["kind"] == "Deployment"
        }

    @property
    def worker_deployments(self):
        return {k: v for k, v in self.deployments.items() if k.endswith("-worker")}

    @property
    def cronjobs(self):
        return {
            r["name"]: self.cluster.get_namespace(self.target_namespace).cronjobs[r["name"]]
            for r in self.resources if r["kind"] == "CronJob"
        }

    @property
    def resources(self):
        return self.model["status"]["resources"]
