import functools

import boto3

from .cluster import Cluster
from k8s_client import K8SClientFactory

class Inputs(object):
    debug = None
    service = None
    namespace = None
    source_cluster = None
    dest_cluster = None
    region = None
    aws_profile = None
    dry_run = None

    def __getattribute__(self, name):
        v = super().__getattribute__(name)
        if v is None:
            raise ValueError(f"Input {name} is not set")
        return v

def register_inputs(**kwargs):
    for k, v in kwargs.items():
        if v is not None:
            setattr(Inputs, k, v)

class Cache:
    def __init__(self):
        pass

    def source_client_factory(self):
        return K8SClientFactory(
            Inputs.aws_profile,
            Inputs.region,
            Inputs.source_cluster,
        )

    def dest_client_factory(self):
        return K8SClientFactory(
            Inputs.aws_profile,
            Inputs.region,
            Inputs.dest_cluster,
        )

    @functools.cached_property
    def source_cluster(self):
        c = Cluster(
            Inputs.source_cluster,
            self.source_client_factory(),
        )
        return c

    @functools.cached_property
    def dest_cluster(self):
        c = Cluster(
            Inputs.dest_cluster,
            self.dest_client_factory(),
        )
        return c

    @functools.cached_property
    def aws_session(self):
        return boto3.Session(region_name=Inputs.region, profile_name=Inputs.aws_profile)

cache = Cache()
