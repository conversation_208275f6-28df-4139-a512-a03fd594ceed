from functools import cached_property

class Service:
    """
    A Xendit microservice.
    """

    name = None
    source_cluster = None
    dest_cluster = None

    def __init__(self, name, source_cluster, dest_cluster):
        self.name = name
        self.source_cluster = source_cluster
        self.dest_cluster = dest_cluster

    @cached_property
    def source_cluster_app(self):
        pass

    @cached_property
    def dest_cluster_app(self):
        pass

    @cached_property
    def teamcentric_data_route53(self):
        pass

    @cached_property
    def teamcentric_data_cloudflare(self):
        pass
