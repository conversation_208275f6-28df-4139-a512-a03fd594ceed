import sys
import logging

import click

import fawkes
import infra_cf
import infra_lib
import infra_r53
import team_centric
from . import base
from . import r53_fawkes_destination_lib as lib
from model import globals

class NotExistException(Exception):
    pass


class SkipException(Exception):
    pass

@click.command
def migrate(**params):
    Route53FawkesDestination().migrate(params)


class Route53FawkesDestination(base.ServiceComponent):
    def migrate(self, params):
        cluster = globals.Inputs.source_cluster
        dry_run = globals.Inputs.dry_run
        namespace = globals.Inputs.namespace
        service = globals.Inputs.service

        mode = None
        if namespace.endswith("dev"):
            mode = "development"
        elif namespace.endswith("live"):
            mode = "live"
        assert mode

        logging.info(
            f"Running the script in dry-run={dry_run} mode={mode} namespace={namespace}"
        )

        # Get all ingress route from the source cluster
        ingress_routes = infra_lib.get_all_ingress_routes(globals.cache.source_cluster.client)

        skip_unresolvable = 0
        skip_cf = 0
        ok_count = 0
        warning_count = 0

        total = len(ingress_routes)

        # Filter based on service
        ingress_routes = [ir for ir in ingress_routes if infra_lib.match_name(service, ir[1])]

        # Filter based on mode
        ingress_routes = [ir for ir in ingress_routes if infra_lib.match_mode(ir[1], mode)]

        # Filter based on namespaces
        ingress_routes = [
            ir for ir in ingress_routes if infra_lib.match_namespace(namespace, ir[1])
        ]

        processed_ingress_routes = ingress_routes
        total_processed = len(processed_ingress_routes)

        for ir in processed_ingress_routes:
            host = ir[3]
            namespace = ir[1]

            ip_addresses = infra_lib.get_ip_list(host)

            if len(ip_addresses) == 0:
                logging.info(
                    f"Skipping ingress since it's not resolvable to any ip addresses host={host}"
                )
                skip_unresolvable += 1
                continue

            global_ip = infra_lib.is_global_ip(ip_addresses)
            private_ip = infra_lib.is_private_ip(ip_addresses)

            if private_ip or (global_ip and infra_lib.is_aws_ip(ip_addresses)):
                # Handle both private IPs and global AWS IPs
                record_sets = infra_r53.get_record_set(host)
                weighted_index = infra_r53.get_weighted_index(record_sets)

                if weighted_index >= 0:
                    # It's expected that Route53 record host exists
                    # Add fawkes destination
                    stack = team_centric.get_stack_by_ns(namespace)
                    if stack is None:
                        logging.warning(
                            f"Can not find stack host={host} namespace={namespace}"
                        )
                        warning_count += 1
                        continue

                    set_id = "fawkes"
                    zone_id = record_sets[weighted_index].zone_id
                    fawkes_record = None
                    gen_import = False

                    for r in record_sets:
                        record_set = r.record_set
                        if record_set.get("SetIdentifier", "") == set_id:
                            fawkes_record = record_set
                            gen_import = True
                            break

                    if fawkes_record is None:
                        # Set appropriate resource record value based on IP type
                        resource_value = None
                        if private_ip:
                            fawkes_cluster = fawkes.get_fawkes_cluster(cluster)
                            resource_value = fawkes.get_traefik_domain(fawkes_cluster, "priv")
                        else:  # global_ip and on AWS
                            resource_value = f"{host}.cdn.cloudflare.net"

                        fawkes_record = {
                            "Name": host,
                            "Type": record_sets[weighted_index].record_set["Type"],
                            "SetIdentifier": set_id,
                            "Weight": 0,
                            "ResourceRecords": [
                                {"Value": resource_value},
                            ],
                        }

                    logging.info(
                          f"Update/Insert Fawkes destination for stack={stack} host={host}"
                      )
                    result = lib.upsert_fawkes_destination(
                        stack,
                        fawkes_record,
                        zone_id,
                        gen_import=gen_import,
                        dry_run=dry_run,
                    )

                    if result:
                        ok_count += 1
                    else:
                        warning_count += 1
                else:
                    # No record on Route53 with name equal to host
                    logging.warning(
                        f"Primary destination record is not exist on Route53 host={host}"
                    )
                    warning_count += 1
            elif global_ip and infra_cf.is_cloudflare_ip(ip_addresses):
                # Hostname is exposed to internet and resides on Cloudflare
                # No need to create Route53 fawkes destination
                logging.info(
                    f"Skipping ingress since it's already on Cloudflare host={host}"
                )
                skip_cf += 1
            elif global_ip:
                logging.warning(
                    f"Host is global but neither on Cloudflare nor on AWS host={host} global={global_ip} on_aws={infra_lib.is_aws_ip(ip_addresses)} on_cf={infra_cf.is_cloudflare_ip(ip_addresses)}"
                )
                warning_count += 1
            else:
                logging.warning(
                    f"Host ip addresses are neither global nor private host={host}"
                )
                warning_count += 1

        logging.info(
            f"Summary: total_ingress={total} processed_ingress={total_processed} ok={ok_count} warning={warning_count} skip_cf={skip_cf} skip_unresolvable={skip_unresolvable}"
        )
