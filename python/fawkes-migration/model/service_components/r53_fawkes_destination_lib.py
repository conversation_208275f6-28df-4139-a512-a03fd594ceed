import infra_lib
import logging
import team_centric


def upsert_fawkes_destination(
    stack: str,
    record_set: dict,
    zone_id: str,
    gen_import: bool = False,
    dry_run: bool = True,
) -> bool:
    """Add route53 record with weighted routing to team centric

    Args:
        stack: Stack name where record will be added
        record_set: Record set that will be added or updated
        gen_import: Flag to decide whether to generate Terraform import block
        dry_run: Flag to decide whether to run in dry run mode

    Return:
        bool: True if host is added to team centric. False otherwise
    """
    skip_team_centric = False
    host = record_set["Name"].rstrip(".")
    input_file = team_centric.get_r53_input(host)
    if input_file is None:
        logging.warning(
            f"Input file does not exist for hostname={host} in folder {stack}"
        )
        return False
    input = team_centric.read_input(stack, input_file)
    set_id = record_set["SetIdentifier"]
    weight = record_set["Weight"]
    host_prefix = host.removesuffix(f".{input["zone_name"]}")
    target_index = infra_lib.get_index(host_prefix, input["records"])

    values = [v["Value"] for v in record_set["ResourceRecords"]]

    record_values = {
        "weight": weight,
        "values": values,
    }

    if target_index >= 0:
        # Host already exists on team centric input
        if "weighted" in input["records"][target_index]:
            # Weighted record exists on team centric input
            weighted = input["records"][target_index]["weighted"]

            if weighted.get("values") is None:
                weighted["values"] = {}

            weighted["values"][set_id] = record_values
        else:
            logging.warning(
                f"Fawkes destination is not added to team centric since existing entry is not of type weighted host={host}"
            )
            skip_team_centric = True
    else:
        logging.info(
            f"Fawkes destination is not added to team centric since the primary destination does not exist host={host}"
        )
        skip_team_centric = True

    if not skip_team_centric:
        logging.info(
            f"Adding Fawkes destination to team centric host={host} set_id={set_id} stack={stack} input_file={input_file}"
        )
        if not dry_run:
            team_centric.write_input(input, stack, input_file, sort_keys=False)

            if gen_import:
                zone_module = input["zone_name"].replace(".", "_")
                zone_id = zone_id.removeprefix("/hostedzone/")
                type = record_set["Type"]
                destination = (
                    f"aws-route53/{input["zone_name"]}/import-{host}-{set_id}.tf"
                )

                import_template = f"""\
import {{
  to = module.route53_{zone_module}.module.this["{host_prefix}"].aws_route53_record.weighted["{set_id}"]
  id = "{zone_id}_{host}_{type}_{set_id}"
}}
"""

                team_centric.write_file(import_template, stack, destination)

    return not skip_team_centric
