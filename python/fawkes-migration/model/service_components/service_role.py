import sys
import logging
import re

import click
import ruamel.yaml

from . import service_role_lib as lib
import fawkes
import infra_lib
import team_centric

from . import base
from model import globals


class NotExistException(Exception):
    pass


class SkipException(Exception):
    pass


@click.command
@click.option(
    "--add-dest-cluster",
    default=True,
    help="Flag to indicate whether to add destination cluster into service role entry",
)
@click.option(
    "--force-import",
    default=False,
    help="Flag to indicate whether to generate Terraform import block for existing entry",
)
@click.option(
    "--exclude",
    default=[],
    multiple=True,
    help="List of service account name to exclude. Default to exclude none",
)
@click.option(
    "--write-irsa",
    default=True,
    help="Flag to indicate whether to dump IRSA raw data to yaml file",
)
def migrate(**params):
    IamRoleServiceAccount().migrate(params)


class IamRoleServiceAccount(base.ServiceComponent):
    def filter_irsa(self, input_irsa, mode, namespace, name, exclude):
        irsa = input_irsa

        # Filter irsa based on mode
        irsa = [sa for sa in irsa if infra_lib.match_mode(sa["namespace"], mode)]

        # Filter irsa based on namespace
        irsa = [sa for sa in irsa if lib.match_name(sa["namespace"], namespace)]

        # Filter irsa based on name
        irsa = [sa for sa in irsa if lib.match_name(sa["name"], name)]

        # Exclude irsa based on name
        irsa = [sa for sa in irsa if not lib.match_name(sa["name"], exclude)]

        # normalized_end_index = total if end_index == sys.maxsize else end_index
        # eligible_irsa = irsa[start_index:normalized_end_index]
        return irsa

    def migrate(self, params):
        cluster = globals.Inputs.source_cluster
        aws_profile = globals.Inputs.aws_profile
        dry_run = globals.Inputs.dry_run
        namespace = globals.Inputs.namespace
        name = globals.Inputs.service
        write_irsa = params["write_irsa"]
        exclude = params["exclude"]

        mode = None
        if namespace.endswith("dev"):
            mode = "development"
        elif namespace.endswith("live"):
            mode = "live"
        assert mode

        logging.info(
            f"Running the script in dry_run={dry_run} mode={mode} namespace={namespace} name={name} excludes={exclude}"
        )

        irsa = lib.get_all_irsa(globals.cache.source_cluster.client)
        ok_count, skip_count, nonexist_count = [0]*3
        total = len(irsa)

        eligible_irsa = self.filter_irsa(irsa, mode, namespace, name, exclude)
        total_processed = len(eligible_irsa)

        import_entries = {}
        for sa in eligible_irsa:
            try:
                team_centric_input, import_entries = self.process_service_account(
                    sa, params
                )
                ok_count += 1

            except SkipException:
                skip_count += 1
            except NotExistException:
                nonexist_count += 1
                sa["role_policy"] = {}
                sa["role_path"] = ""
                sa["chamber_services"] = []
                logging.warning(
                    f"Role does not exist name={sa['role_name']} aws_profile={aws_profile}"
                )

        if not dry_run:
            for stack in import_entries.keys():
                destination = f"aws-service-role/imports-{cluster}.tf"

                logging.info(
                    f"Generating terraform import block stack={stack} destination={destination}"
                )
                tf_import = lib.render_tf_import(
                    lib.get_org(aws_profile),
                    lib.get_environment(cluster),
                    import_entries[stack],
                )
                team_centric.write_file(tf_import, stack, destination)

        if write_irsa:
            # Write IRSA to a yaml file
            with open(f"{cluster}.yaml", "w") as f:
                ruamel.yaml.representer.RoundTripRepresenter.ignore_aliases = (
                    lambda x, y: True
                )

                yaml = ruamel.yaml.YAML()
                yaml.indent(mapping=2, sequence=4, offset=2)
                yaml.preserve_quotes = True
                yaml.width = 10240

                yaml.dump(irsa, f)

        logging.info(
            f"Summary: total={total} processed={total_processed} ok={ok_count} nonexist={nonexist_count}"
        )

    def get_iam_role_and_policies(self, iam_client, role_name):
        iam = iam_client
        try:
            iam_role = iam.get_role(RoleName=role_name)["Role"]
        except iam.exceptions.NoSuchEntityException:
            raise NotExistException

        def get_policies():
            for policy_name in iam.list_role_policies(RoleName=role_name)[
                "PolicyNames"
            ]:
                yield iam.get_role_policy(RoleName=role_name, PolicyName=policy_name)

        return iam_role, get_policies()

    def process_service_account(self, sa, params):
        cluster = globals.Inputs.source_cluster
        session = globals.cache.aws_session
        iam = session.client("iam")
        add_dest_cluster = params["add_dest_cluster"]
        force_import = params["force_import"]
        dry_run = globals.Inputs.dry_run

        input_file = "input-service-role.yaml"
        output_file = input_file
        import_entries = {}

        aws_profile = session.profile_name

        namespace = sa["namespace"]

        sa["service_name"] = re.sub("-(development|live)$", "", sa["name"])

        sa["role_name"] = sa["role_arn"].split("/")[1]
        logging.info("role_name=%s", sa["role_name"])

        if len(sa["role_name"]) > 64:
            raise NotExistException

        if add_dest_cluster:
            sa["clusters"] = [
                cluster,
                fawkes.get_fawkes_cluster(cluster),
            ]
        else:
            sa["clusters"] = [
                cluster,
            ]

        sa["pci_scope"] = "non_pci"
        if namespace.endswith("pci"):
            sa["pci_scope"] = "pci"
        elif namespace.endswith("cde"):
            sa["pci_scope"] = "cde"

        iam_role, policies = self.get_iam_role_and_policies(iam, sa["role_name"])

        sa["role_path"] = iam_role["Path"]
        sa["role_max_session_duration"] = iam_role["MaxSessionDuration"]
        sa["role_policy"] = {}

        for role_policy in policies:
            policy_name = role_policy["PolicyName"]
            sa["role_policy"][policy_name] = role_policy["PolicyDocument"]

            for statement in sa["role_policy"][policy_name]["Statement"]:
                if "Sid" in statement and statement["Sid"] == "":
                    del statement["Sid"]

                if isinstance(statement["Resource"], str):
                    temp = statement["Resource"]
                    statement["Resource"] = [temp]

                if isinstance(statement["Action"], str):
                    temp = statement["Action"]
                    statement["Action"] = [temp]

        sa["chamber_policy"] = lib.find_chamber_policy(sa["role_policy"])
        sa["chamber_services"] = lib.extract_chamber_services(sa["role_policy"])
        sa["chamber_modes"] = lib.extract_chamber_modes(sa["role_policy"])

        if not lib.check_modes_consistency(sa["chamber_modes"]):
            logging.error(
                f"Chamber modes are not consistent on IAM policy name={sa['name']} role_name={sa['role_name']}"
            )
            sys.exit(1)

        if sa["mode"] != sa["chamber_modes"][0]:
            logging.warning(
                "Mode is different between namespace and chamber. Prefer mode from chamber"
            )
            sa["mode"] = sa["chamber_modes"][0]

        sa["sns_sqs_policy"] = lib.find_irsa_sns_sqs_policy(sa["role_policy"])
        sa["custom_policies"] = lib.find_irsa_custom_policies(sa["role_policy"])

        stack = team_centric.get_stack_by_ns(sa["namespace"])

        if stack is None:
            logging.error(f"Could not translate to stack namespace={sa['namespace']}")
            sys.exit(1)

        if not sa["mode"]:
            raise SkipException

        team_centric_input = team_centric.read_input(stack, input_file)
        service_role_input = (
            team_centric_input.setdefault("aws", {})
            .setdefault(lib.get_org(aws_profile), {})
            .setdefault("global", {})
            .setdefault("service_role", {})
            .setdefault(lib.get_environment(cluster), [])
        )
        role_index = lib.get_role_index(service_role_input, sa["role_name"])

        new_entry = role_index == -1
        if new_entry:
            entry = dict(service_name=sa["service_name"])

            if sa["pci_scope"] != "non_pci":
                entry["pci_scope"] = sa["pci_scope"]
            service_role_input.append(entry)
        else:
            entry = service_role_input[role_index]

        entry.update(
            mode=sa["mode"],
            clusters=sorted(set(entry.get("clusters", []) + sa["clusters"])),
            role_name=sa["role_name"],
        )

        # Set service_accounts value
        entry.setdefault("service_accounts", [])
        if not lib.contains_sa(
            entry["service_accounts"],
            sa["name"],
            sa["namespace"],
        ):
            sa_entry = {"name": sa["name"], "namespace": sa["namespace"]}
            entry["service_accounts"].append(sa_entry)

        # Set max_session_duration when its values is not 3600
        if sa["role_max_session_duration"] != 3600:
            entry["max_session_duration"] = sa["role_max_session_duration"]

        # Set chamber_services value
        # Only add chamber_services when it's different from the service name
        # Chamber services xendit-infra and service-discovery are automatically added by Terraform module
        input_chamber_services = entry.setdefault("chamber_services", [])
        chamber_services = set(sa["chamber_services"]) | set(input_chamber_services)
        chamber_services -= set(
            ["xendit-infra", "service-discovery", sa["service_name"]]
        )
        if len(chamber_services) > 0:
            entry["chamber_services"] = list(chamber_services)
        else:
            del entry["chamber_services"]

        # Set values related to IRSA sns and sqs
        if sa["sns_sqs_policy"] is not None:
            entry["enable_sns_sqs"] = True
            kms_key_arns = lib.extract_kms_keys(sa["role_policy"])

            if len(kms_key_arns):
                sns_sqs_kms_key_arn = [
                    key for key in kms_key_arns if session.region_name in key
                ][0]
                kms_region = session.region_name
                if not new_entry:
                    # May be different from current region
                    sns_sqs_kms_key_arn = kms_key_arns[0]
                    kms_region = sns_sqs_kms_key_arn.split(":")[3]

                kms_client = session.client("kms", region_name=kms_region)
                kms_alises = kms_client.list_aliases(KeyId=sns_sqs_kms_key_arn)[
                    "Aliases"
                ]

                sns_sqs_kms_key = None
                for alias in kms_alises:
                    if "sns-sqs" in alias["AliasName"]:
                        sns_sqs_kms_key = alias["AliasName"]
                        break

                if sns_sqs_kms_key:
                    entry["sns_sqs_kms_key"] = sns_sqs_kms_key

                if len(kms_key_arns) > 1:
                    additional_kms_keys = set(kms_key_arns) - set([sns_sqs_kms_key_arn])
                    entry["sns_sqs_kms_key_arns"] = list(additional_kms_keys)

            published_sns = lib.extract_published_sns(sa["role_policy"])

            if len(published_sns) > 0:
                entry["published_sns_arns"] = published_sns

            consumed_sqs = lib.extract_consumed_sqs(sa["role_policy"])

            if len(consumed_sqs) > 0:
                entry["consumed_sqs_arns"] = consumed_sqs

            published_sqs = lib.extract_published_sqs(sa["role_policy"])

            if len(published_sqs) > 0:
                entry["published_sqs_arns"] = published_sqs

        # Set value for role_custom_policies
        for custom_policy in sa["custom_policies"]:
            role_custom_policy = entry.setdefault("role_custom_policies", {})
            role_custom_policy[custom_policy] = sa["role_policy"][custom_policy]

        if new_entry or force_import:
            import_entry = {
                "role_name": sa["role_name"],
                "chamber_policy": sa["chamber_policy"],
                "custom_policies": sa["custom_policies"],
                "sns_sqs_policy": sa["sns_sqs_policy"],
            }

            if stack not in import_entries:
                import_entries[stack] = []

            import_entries[stack].append(import_entry)

        # Write result to service role input file under team centric directory
        logging.info(
            f"Migrating IRSA management to team centric stack={stack} role_name={sa['role_name']} aws_profile={aws_profile}"
        )

        if not dry_run:
            team_centric.write_input(team_centric_input, stack, output_file, False)

        return team_centric_input, import_entries
