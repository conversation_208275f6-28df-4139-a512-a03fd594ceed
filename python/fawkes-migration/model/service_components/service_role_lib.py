import re

import boto3
import kubernetes
from jinja2 import Environment, FileSystemLoader

KMS_PERMISSIONS = set(
    [
        "kms:Encrypt",
        "kms:Decrypt",
        "kms:GenerateDataKey*",
        "kms:DescribeKey",
    ]
)

PUBLISH_SNS_PERMISSIONS = set(
    [
        "sns:Publish",
        "sns:GetTopicAttributes",
        "sns:ListSubscriptionsByTopic",
    ]
)

CONSUME_SQS_PERMISSIONS = set(
    [
        "sqs:GetQueueAttributes",
        "sqs:GetQueueUrl",
        "sqs:ListDeadLetterSourceQueues",
        "sqs:ListQueueTags",
        "sqs:ReceiveMessage",
        "sqs:DeleteMessage",
        "sqs:ChangeMessageVisibility",
        "sqs:ChangeMessageVisibilityBatch",
    ]
)

PUBLISH_SQS_PERMISSIONS = set(
    [
        "sqs:CancelMessageMoveTask",
        "sqs:ChangeMessageVisibility",
        "sqs:ChangeMessageVisibilityBatch",
        "sqs:DeleteMessage",
        "sqs:GetQueueAttributes",
        "sqs:GetQueueUrl",
        "sqs:ListDeadLetterSourceQueues",
        "sqs:ListDeadLetterSourceQueues",
        "sqs:ListMessageMoveTasks",
        "sqs:ListQueueTags",
        "sqs:ReceiveMessage",
        "sqs:SendMessage",
        "sqs:StartMessageMoveTask",
    ]
)


def match_name(name: str, eligible_names: list[str]) -> bool:
    """Check if given name is part of eligible names

    Use prefix match to check if given name matches with any item in eligible names

    Args:
        name: Name to be checked
        eligible_names: List of names prefix

    Returns:
        bool: True when given name is part of eligible names. False otherwise.
    """
    result = False

    if "all" in eligible_names:
        result = True
    else:
        for prefix in eligible_names:
            if name.startswith(prefix):
                result = True
                break

    return result


def get_all_irsa(k8s_client) -> list[dict]:
    """Get all service accounts with IRSA annotation

    Args:
        k8s_client: Instance of Kubernetes client

    Returns:
        list[dict]: List of service accounts
    """
    result = []

    v1 = kubernetes.client.CoreV1Api(k8s_client)

    # Fetch all service accounts across all namespaces
    all_service_accounts = v1.list_service_account_for_all_namespaces()

    for sa in all_service_accounts.items:
        if (
            sa.metadata.annotations
            and "eks.amazonaws.com/role-arn" in sa.metadata.annotations
        ):
            result.append(
                {
                    "namespace": sa.metadata.namespace,
                    "name": sa.metadata.name,
                    "mode": sa.metadata.labels.get("mode", "null")
                    if sa.metadata.labels
                    else "null",
                    "repo": sa.metadata.annotations.get("project_repo", "null")
                    if sa.metadata.annotations
                    else "null",
                    "role_arn": sa.metadata.annotations["eks.amazonaws.com/role-arn"]
                    if sa.metadata.annotations
                    and "eks.amazonaws.com/role-arn" in sa.metadata.annotations
                    else "null",
                }
            )

    return result


def get_role_detail(role_name: str, aws_profile: str):
    """Get role path and attached role policies from given IAM Role.

    Args:
        role_name: Name of IAM Role.

    Returns:
        A dict of role name, role path, and list of attached policies. For example:

        {role_name: "example-role",
        role_path: "/",
        role_policies: ["example-policy"]}
    """
    result = {"role_name": role_name, "role_path": "", "role_policies": []}
    session = boto3.Session(profile_name=aws_profile)
    iam = session.client("iam")

    try:
        role_path = iam.get_role(RoleName=role_name)["Role"]["Path"]
        role_policies = iam.list_role_policies(RoleName=role_name)
        result["role_policies"] = role_policies["PolicyNames"]
        result["role_path"] = role_path
    except Exception:
        pass

    return result


def get_role_index(service_accounts: list[str], role_name: str):
    """Get index of given role name from list of given service accounts.

    Args:
        service_accounts: List of service accounts details.
        role_name: Name of the IAM Role.

    Returns:
        int: Index of role_name in the service_accounts. Returns -1 if not found.
    """
    result = -1

    for idx, sa in enumerate(service_accounts):
        if sa["role_name"] == role_name:
            result = idx
            break

    return result


def contains_sa(service_accounts: list[dict], name: str, namespace: str):
    """Check whether service account is in the given list of service account.

    Args:
        service_accounts: List of service accounts where search will be performed.
        name: Name of the service account to be searched.
        namespace: Namespace of the service account.

    Returns:
        bool: True if given name is found on the service_accounts. Otherwise it returns false.
    """
    result = False

    for sa in service_accounts:
        if sa["name"] == name and sa["namespace"] == namespace:
            result = True
            break

    return result


def find_chamber_policy(policies: dict[str]) -> str:
    """Find chamber policy in the given list of policies.

    Args:
        policies: Dictionary of policy name and policy document.

    Returns:
        str: Name of chamber policy. Return None if not found
    """

    result = None

    for k, v in policies.items():
        if (
            "ssm:GetParametersByPath" in v["Statement"][0]["Action"]
            or "ssm:GetParameters" in v["Statement"][0]["Action"]
        ):
            result = k
            break

    return result


def extract_chamber_services(policies: dict[str]) -> list[str]:
    """Extract chamber services from list of IAM Policies

    Use regex capture to extract chamber_services value from IAM Policies

    Args:
        policies: List of IAM Policies details

    Returns:
        list[str]: List of chamber services extracted from policies
    """
    chamber_services = []

    chamber_policy = find_chamber_policy(policies)

    if chamber_policy is not None:
        for statement in policies[chamber_policy]["Statement"]:
            if isinstance(statement["Resource"], list):
                for resource in statement["Resource"]:
                    matching = re.findall(
                        r".*:parameter\/(production|staging)\/(development|live)\/([^*]+)\*$",
                        resource,
                    )
                    for item in matching:
                        chamber_services.append(item[2].rstrip("/"))

    return chamber_services


def extract_chamber_modes(policies: dict[str]) -> list[str]:
    """Extract chamber modes from list of IAM Policies

    Use regex capture to extract chamber services modes from IAM Policies

    Args:
        policies: List of IAM Policies details

    Returns:
        list[str]: List of chamber services modes extracted from policies
    """
    chamber_modes = []

    chamber_policy = find_chamber_policy(policies)

    if chamber_policy is not None:
        for statement in policies[chamber_policy]["Statement"]:
            if isinstance(statement["Resource"], list):
                for resource in statement["Resource"]:
                    matching = re.findall(
                        r".*:parameter\/(production|staging)\/(development|live)\/([^*]+)\*$",
                        resource,
                    )
                    for item in matching:
                        chamber_modes.append(item[1].rstrip("/"))

    return chamber_modes


def check_modes_consistency(modes: list[str]) -> bool:
    """Check if all values are the same on given list

    Args:
        modes: List of modes

    Return:
        bool: True if all values are either development or live. False otherwise
    """
    result = False

    if len(modes) > 0:
        baseline = modes[0]

        if baseline == "development" or baseline == "live":
            modes_result = []

            for mode in modes:
                modes_result.append(mode == baseline)

            result = all(modes_result)

    return result


def get_org(aws_profile: str) -> str:
    """Get organization from given aws profile

    Args:
        aws_profile: AWS profile name

    Returns:
        str: organization
    """
    result = ""

    if aws_profile.startswith("xendit"):
        result = "xendit"
    elif aws_profile.startswith("instamoney"):
        result = "instamoney"
    elif aws_profile.startswith("iluma"):
        result = "iluma"
    elif aws_profile.startswith("gmf"):
        result = "gmf"

    return result


def get_environment(cluster: str) -> str:
    """Get environment from given cluster

    Args:
        cluster: cluster name

    Returns:
        str: environment name
    """
    result = ""

    if "-production" in cluster:
        result = "production"
    elif "-prod" in cluster:
        result = "production"
    elif "-staging" in cluster:
        result = "staging"
    elif "-stg" in cluster:
        result = "staging"

    return result


def render_tf_import(org: str, env: str, import_entries: list) -> str:
    file_loader = FileSystemLoader("./model/service_components/templates")
    template_env = Environment(loader=file_loader)
    template = template_env.get_template("imports-tf.j2")

    module_name = {
        "xendit-production": "xnd",
        "xendit-staging": "xnd_stg",
        "instamoney-production": "im",
        "instamoney-staging": "im_stg",
        "iluma-production": "il",
        "iluma-staging": "il_stg",
        "gmf-production": "gmf",
    }[f"{org}-{env}"]

    processed_roles = []
    distinct_import_entries = []

    for entry in import_entries:
        if entry["role_name"] not in processed_roles:
            processed_roles.append(entry["role_name"])
            distinct_import_entries.append(entry)

    return template.render(roles=distinct_import_entries, module_name=module_name)


def find_irsa_sns_sqs_policy(policies: dict[str]) -> str:
    """Find sns and sqs policy in the given list of policies.

    IRSA sns and sqs policy has the following criteria
    - policy name starts with terraform-
    - It has KMS permission
    - At least one of the following permission
      - publish to SNS
      - consume from SQS
      - publish to SQS

    Args:
        policies: Dictionary of policy name and policy document.

    Returns:
        str: Name of sns and sqs policy. Return None if not found
    """

    result = None

    for k, v in policies.items():
        if k.startswith("terraform-"):
            contains_kms = []
            contains_publish_sns = []
            contains_consume_sqs = []
            contains_publish_sqs = []
            contains_unknown = []

            for statement in v["Statement"]:
                is_kms = KMS_PERMISSIONS == set(statement["Action"])
                is_publish_sns = PUBLISH_SNS_PERMISSIONS == set(statement["Action"])
                is_consume_sqs = CONSUME_SQS_PERMISSIONS == set(statement["Action"])
                is_publish_sqs = (
                    PUBLISH_SQS_PERMISSIONS & set(statement["Action"])
                    == PUBLISH_SQS_PERMISSIONS
                )

                if is_kms:
                    contains_kms.append(is_kms)
                elif is_publish_sns:
                    contains_publish_sns.append(is_publish_sns)
                elif is_consume_sqs:
                    contains_consume_sqs.append(is_consume_sqs)
                elif is_publish_sqs:
                    contains_publish_sqs.append(is_publish_sqs)
                else:
                    contains_unknown.append(True)

            if not any(contains_unknown):
                if any(contains_kms) and (
                    any(contains_publish_sns)
                    or any(contains_consume_sqs)
                    or any(contains_publish_sqs)
                ):
                    result = k
                    break

    return result


def find_irsa_custom_policies(policies: dict[str]) -> list[str]:
    """Find list of custom IAM policies

    Args:
        policies: Dictionary of policy name and policy document.

    Returns:
        list[str]: Name of custom IAM policies. Return empty list if there is no custom policy
    """
    result = []
    chamber_policy = find_chamber_policy(policies)
    sns_sqs_policy = find_irsa_sns_sqs_policy(policies)

    for item in policies.keys():
        if item != chamber_policy and item != sns_sqs_policy:
            result.append(item)

    return result


def extract_kms_keys(policies: dict[str]) -> list[str]:
    """Extract KMS Keys from list of IAM Policies

    Use regex capture to extract kms keys value from IAM Policies

    Args:
        policies: List of IAM Policies details

    Returns:
        list[str]: List of kms keys extracted from policies
    """
    result = []
    sns_sqs_policy = find_irsa_sns_sqs_policy(policies)

    if sns_sqs_policy is not None:
        for statement in policies[sns_sqs_policy]["Statement"]:
            if KMS_PERMISSIONS == set(statement["Action"]):
                res = statement["Resource"]

                if isinstance(res, str):
                    result.append(res)
                else:
                    result.extend(res)

                break

    return result


def extract_published_sns(policies: dict[str]) -> list[str]:
    """Extract SNS topics ARNs that are used for publishing from list of IAM Policies

    Use regex capture to extract topics ARNs

    Args:
        policies: List of IAM Policies details

    Returns:
        list[str]: List of SNS topics ARNs
    """
    result = []
    sns_sqs_policy = find_irsa_sns_sqs_policy(policies)

    if sns_sqs_policy is not None:
        for statement in policies[sns_sqs_policy]["Statement"]:
            if PUBLISH_SNS_PERMISSIONS == set(statement["Action"]):
                res = statement["Resource"]

                if isinstance(res, str):
                    result.append(res)
                else:
                    result.extend(res)

                break

    return result


def extract_consumed_sqs(policies: dict[str]) -> list[str]:
    """Extract SQS queues ARNs that are used for consuming from list of IAM Policies

    Use regex capture to extract SQS queue ARNs

    Args:
        policies: List of IAM Policies details

    Returns:
        list[str]: List of SQS queues ARNs
    """
    result = []
    sns_sqs_policy = find_irsa_sns_sqs_policy(policies)

    if sns_sqs_policy is not None:
        for statement in policies[sns_sqs_policy]["Statement"]:
            if CONSUME_SQS_PERMISSIONS == set(statement["Action"]):
                res = statement["Resource"]

                if isinstance(res, str):
                    result.append(res)
                else:
                    result.extend(res)

                break

    return result


def extract_published_sqs(policies: dict[str]) -> list[str]:
    """Extract SQS queues ARNs that are used for publishing from list of IAM Policies

    Use regex capture to extract SQS queue ARNs

    Args:
        policies: List of IAM Policies details

    Returns:
        list[str]: List of SQS queues ARNs
    """
    result = []
    sns_sqs_policy = find_irsa_sns_sqs_policy(policies)

    if sns_sqs_policy is not None:
        for statement in policies[sns_sqs_policy]["Statement"]:
            if (
                PUBLISH_SQS_PERMISSIONS & set(statement["Action"])
                == PUBLISH_SQS_PERMISSIONS
            ):
                res = statement["Resource"]

                if isinstance(res, str):
                    result.append(res)
                else:
                    result.extend(res)

                break

    return result
