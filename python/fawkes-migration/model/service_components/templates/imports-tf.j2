{% for entry in roles %}
import {
  to = module.{{module_name}}["{{ entry.role_name }}"].aws_iam_role.this
  id = "{{ entry.role_name }}"
}
{% if entry.chamber_policy %}
import {
  to = module.{{module_name}}["{{ entry.role_name }}"].aws_iam_role_policy.inline_credentials_attachment
  id = "{{ entry.role_name }}:{{ entry.chamber_policy }}"
}
{% endif %}
{%- for custom_policy in entry.custom_policies %}
import {
  to = module.{{module_name}}["{{ entry.role_name }}"].aws_iam_role_policy.custom["{{ custom_policy }}"]
  id = "{{ entry.role_name }}:{{ custom_policy }}"
}
{% endfor %}
{%- if entry.sns_sqs_policy %}
import {
  to = module.{{module_name}}["{{ entry.role_name }}"].aws_iam_role_policy.sns_sqs_read_write[0]
  id = "{{ entry.role_name }}:{{ entry.sns_sqs_policy }}"
}
{% endif %}
{% endfor %}
