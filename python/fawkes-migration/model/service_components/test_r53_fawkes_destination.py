import json
from unittest.mock import Mock

# import team_centric
import infra_r53
from . import r53_fawkes_destination


r53_data = [
    infra_r53.R53RecordSet(rs, "/hostedzone/Z10345542I232HZ60CL5T", "xendit-staging")
    for rs in [
        {
            "Name": "ewallet-service-v2-dev.ap-southeast-1.priv.stg.tidnex.dev.",
            "Type": "CNAME",
            "SetIdentifier": "fawkes",
            "Weight": 0,
            "TTL": 60,
            "ResourceRecords": [
                {"Value": "xnd-sg-stg-aws-0-priv.sg.live.non_pci.stg.tidnex.dev"}
            ],
        },
        {
            "Name": "ewallet-service-v2-dev.ap-southeast-1.priv.stg.tidnex.dev.",
            "Type": "CNAME",
            "SetIdentifier": "mass_conversion",
            "Weight": 100,
            "TTL": 60,
            "ResourceRecords": [
                {"Value": "traefik-internal.ap-southeast-1.priv.stg.tidnex.dev"}
            ],
        },
    ]
]


def test_r53_fawkes_destination_migrate(monkeypatch):
    m1 = Mock()
    m1.return_value = False
    monkeypatch.setattr(r53_fawkes_destination.lib, "upsert_fawkes_destination", m1)

    m2 = Mock()
    m2.return_value = r53_data
    monkeypatch.setattr(
        r53_fawkes_destination.infra_r53, "TARGET_AWS_PROFILES", ["xendit-staging"]
    )
    monkeypatch.setattr(r53_fawkes_destination.infra_r53, "get_record_set", m2)

    r53_fawkes_destination.Route53FawkesDestination().migrate({})
    assert m2.call_args
    assert m1.call_args_list == [
        (
            (
                "team-payment-growth",
                {
                    "Name": "ewallet-service-v2-dev.ap-southeast-1.priv.stg.tidnex.dev.",
                    "Type": "CNAME",
                    "SetIdentifier": "fawkes",
                    "Weight": 0,
                    "TTL": 60,
                    "ResourceRecords": [
                        {
                            "Value": "xnd-sg-stg-aws-0-priv.sg.live.non_pci.stg.tidnex.dev"
                        }
                    ],
                },
                "/hostedzone/Z10345542I232HZ60CL5T",
            ),
            dict(gen_import=True, dry_run=True),
        )
    ]
