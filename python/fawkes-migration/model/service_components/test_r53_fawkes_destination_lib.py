from . import r53_fawkes_destination_lib as lib


def test_upsert_fawkes_destination():
    args = (
        "team-payment-growth",
        {
            "Name": "ewallet-service-v2-dev.ap-southeast-1.priv.stg.tidnex.dev.",
            "Type": "CNAME",
            "SetIdentifier": "fawkes",
            "Weight": 0,
            "TTL": 60,
            "ResourceRecords": [
                {"Value": "xnd-sg-stg-aws-0-priv.sg.live.non_pci.stg.tidnex.dev"}
            ],
        },
        "/hostedzone/Z10345542I232HZ60CL5T",
    )
    kwargs = dict(gen_import=True, dry_run=False)

    lib.upsert_fawkes_destination(*args, **kwargs)

    assert lib.team_centric.write_input.call_args_list == [
        (
            (
                {
                    "zone_name": "stg.tidnex.dev",
                    "records": [
                        {
                            "name": "ewallet-service-v2-dev.ap-southeast-1.priv",
                            "type": "CNAME",
                            "ttl": 60,
                            "weighted": {
                                "enabled": True,
                                "values": {
                                    "mass_conversion": {
                                        "weight": 100,
                                        "values": [
                                            "traefik-internal.ap-southeast-1.priv.stg.tidnex.dev"
                                        ],
                                    },
                                    "fawkes": {
                                        "weight": 0,
                                        "values": [
                                            "xnd-sg-stg-aws-0-priv.sg.live.non_pci.stg.tidnex.dev"
                                        ],
                                    },
                                },
                            },
                        }
                    ],
                },
                "team-payment-growth",
                "input-r53-stg.tidnex.dev.yaml",
            ),
            dict(sort_keys=False),
        )
    ]
