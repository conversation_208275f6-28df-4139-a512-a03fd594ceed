import datetime

import pytest
import ruamel

from . import service_role
from model import globals

@pytest.fixture
def sc():
    return service_role.IamRoleServiceAccount()

@pytest.fixture(autouse=True)
def mock_aws_clients(mock_aws_session):
    session = mock_aws_session
    assert session is globals.cache.aws_session
    iam = session.client("iam")
    iam.get_role.return_value = {"Role": iam_role}
    iam.list_role_policies.return_value = {"PolicyNames": list(policies)}

    def get_role_policy(PolicyName, **kwargs):
        return policies[PolicyName]

    iam.get_role_policy = get_role_policy

    kms = session.client("kms")
    kms.list_aliases.return_value = {"Aliases": []}

    return session


def test_filter_irsa(sc):
    irsa = service_role.lib.get_all_irsa(
        service_role.globals.cache.source_cluster.client
    )
    irsa = sc.filter_irsa(
        irsa, "development", "digipay-dev", "ewallet-service-v2-development", []
    )
    assert len(irsa) == 1

def test_migrate(monkeypatch, sc):
    monkeypatch.setattr(globals.Inputs, "dry_run", False)
    params = dict(
        add_dest_cluster=True,
        force_import=False,
        exclude=[],
        write_irsa=False,
    )
    sc.migrate(params)

    expected = ruamel.yaml.YAML().load(open("./test_data/team-centric/team-payment-growth/expected-service-role.yaml"))
    assert service_role.team_centric.write_input.call_args.args == (expected, 'team-payment-growth', 'input-service-role.yaml', False)


iam_role = {
    "Path": "/",
    "RoleName": "ewallet-service-v2-development-staging",
    "RoleId": "AROA2IQ3YTYMDOAXR7R5I",
    "Arn": "arn:aws:iam::************:role/ewallet-service-v2-development-staging",
    "CreateDate": datetime.datetime(2021, 5, 24, 4, 38, 53, tzinfo=datetime.UTC),
    "AssumeRolePolicyDocument": {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {
                    "Federated": "arn:aws:iam::************:oidc-provider/oidc.eks.ap-southeast-1.amazonaws.com/id/4D7EF4BC05E2196522DC612EA7B8E2F4"
                },
                "Action": "sts:AssumeRoleWithWebIdentity",
                "Condition": {
                    "StringEquals": {
                        "oidc.eks.ap-southeast-1.amazonaws.com/id/4D7EF4BC05E2196522DC612EA7B8E2F4:sub": "system:serviceaccount:digipay-dev:ewallet-service-v2-development"
                    }
                },
            },
            {
                "Effect": "Allow",
                "Principal": {"Service": "pods.eks.amazonaws.com"},
                "Action": ["sts:TagSession", "sts:AssumeRole"],
            },
        ],
    },
    "MaxSessionDuration": 3600,
    "Tags": [
        {
            "Key": "TFProject",
            "Value": "github.com/xendit/xendit-infrastructure//terraform/xendit/team-payment-growth/aws-service-role",
        },
        {"Key": "cost:team", "Value": "team-payment-growth"},
        {"Key": "Owner", "Value": "team-payment-growth"},
        {"Key": "cost:owner", "Value": "team-payment-growth"},
        {"Key": "ManagedBy", "Value": "terraform"},
    ],
    "RoleLastUsed": {
        "LastUsedDate": datetime.datetime(2025, 6, 11, 4, 42, 9, tzinfo=datetime.UTC),
        "Region": "us-west-2",
    },
}

policies = {
    "ewallet-service-v2_policy_ap-southeast-1_staging_development": {
        "RoleName": "ewallet-service-v2-development-staging",
        "PolicyName": "ewallet-service-v2_policy_ap-southeast-1_staging_development",
        "PolicyDocument": {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Action": "sns:Publish",
                    "Effect": "Allow",
                    "Resource": ["arn:aws:sns:*:************:digipay*"],
                },
                {
                    "Action": [
                        "sqs:DeleteMessage",
                        "sqs:GetQueueUrl",
                        "sqs:ChangeMessageVisibility",
                        "sqs:SendMessageBatch",
                        "sqs:UntagQueue",
                        "sqs:ReceiveMessage",
                        "sqs:SendMessage",
                        "sqs:GetQueueAttributes",
                        "sqs:ListQueueTags",
                        "sqs:TagQueue",
                        "sqs:ListDeadLetterSourceQueues",
                        "sqs:DeleteMessageBatch",
                        "sqs:PurgeQueue",
                        "sqs:DeleteQueue",
                        "sqs:CreateQueue",
                        "sqs:ChangeMessageVisibilityBatch",
                        "sqs:SetQueueAttributes",
                    ],
                    "Effect": "Allow",
                    "Resource": ["arn:aws:sqs:*:************:digipay*"],
                },
                {
                    "Action": [
                        "kms:Encrypt",
                        "kms:Decrypt",
                        "kms:GenerateDataKey*",
                        "kms:DescribeKey",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:kms:ap-southeast-1:************:key/5b71089f-3236-4e54-bfc7-22b3d24b84a4"
                    ],
                },
            ],
        },
        "ResponseMetadata": {
            "RequestId": "3b9f3b45-14b6-4622-99fa-c6ba9778f8b3",
            "HTTPStatusCode": 200,
            "HTTPHeaders": {
                "date": "Wed, 11 Jun 2025 04:51:55 GMT",
                "x-amzn-requestid": "3b9f3b45-14b6-4622-99fa-c6ba9778f8b3",
                "content-type": "text/xml",
                "content-length": "1596",
            },
            "RetryAttempts": 0,
        },
    },
    "terraform-20210524043856502200000001": {
        "RoleName": "ewallet-service-v2-development-staging",
        "PolicyName": "terraform-20210524043856502200000001",
        "PolicyDocument": {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Sid": "",
                    "Effect": "Allow",
                    "Action": [
                        "ssm:GetParametersByPath",
                        "ssm:GetParameters",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Resource": [
                        "arn:aws:ssm:us-west-2:************:parameter/staging/development/xendit-infra/*",
                        "arn:aws:ssm:us-west-2:************:parameter/staging/development/service-discovery/*",
                        "arn:aws:ssm:us-west-2:************:parameter/staging/development/ewallet-service-v2/*",
                        "arn:aws:kms:us-west-2:************:key/e4849d9b-7f16-4043-85ee-c2371d29a7a2",
                    ],
                },
                {
                    "Sid": "",
                    "Effect": "Allow",
                    "Action": [
                        "ssm:DescribeParameters",
                        "kms:ListKeys",
                        "kms:ListAliases",
                    ],
                    "Resource": "*",
                },
            ],
        },
        "ResponseMetadata": {
            "RequestId": "287b19ec-1ebb-46a7-982a-fe7ed3436662",
            "HTTPStatusCode": 200,
            "HTTPHeaders": {
                "date": "Wed, 11 Jun 2025 04:51:55 GMT",
                "x-amzn-requestid": "287b19ec-1ebb-46a7-982a-fe7ed3436662",
                "content-type": "text/xml",
                "content-length": "1962",
            },
            "RetryAttempts": 0,
        },
    },
}
