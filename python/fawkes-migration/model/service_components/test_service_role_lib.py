from . import service_role_lib as lib
import pytest


@pytest.mark.parametrize(
    "namespace,expected",
    [
        ("cashpay-live", True),
        ("payout-dev", False),
    ],
)
def test_match_namespace(namespace, expected):
    prefixes = ["cashpay", "direct-debit"]

    assert lib.match_name(namespace, prefixes) == expected


def test_match_namespace_all():
    prefixes = ["all"]

    assert lib.match_name("cashpay-dev", prefixes)


@pytest.mark.parametrize(
    "name,expected",
    [
        ("payment-statement-service-development", True),
        ("cashpay-service", False),
    ],
)
def test_match_name(name, expected):
    prefixes = ["payment-statement-service"]

    assert lib.match_name(name, prefixes) == expected


def test_match_name_all():
    prefixes = ["all"]

    assert lib.match_name("payment-statement-service-development", prefixes)


@pytest.mark.parametrize(
    "role_name,expected",
    [
        ("my-role", 0),
        ("not-found", -1),
    ],
)
def test_get_role_index(role_name, expected):
    service_accounts = [{"role_name": "my-role"}]

    assert lib.get_role_index(service_accounts, role_name) == expected


@pytest.mark.parametrize(
    "namespace,expected",
    [
        ("cashpay-live", True),
        ("payout-dev", False),
    ],
)
def test_contains_sa(namespace, expected):
    name = "my-service-account"
    service_accounts = [{"name": name, "namespace": "cashpay-live"}]

    assert lib.contains_sa(service_accounts, name, namespace) == expected


def test_find_chamber_policy():
    name = "terraform-20220825080545657000000001"
    policies = {
        name: {
            "Statement": [
                {
                    "Action": [
                        "ssm:GetParametersByPath",
                        "ssm:GetParameters",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/xendit-infra/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/service-discovery/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/instamoney-api-gateway/*",
                        "arn:aws:kms:ap-southeast-1:************:key/846fab0e-feda-43c8-9d45-c1b99d69b8bd",
                    ],
                    "Sid": "",
                },
                {
                    "Action": [
                        "ssm:DescribeParameters",
                        "kms:ListKeys",
                        "kms:ListAliases",
                    ],
                    "Effect": "Allow",
                    "Resource": "*",
                    "Sid": "",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.find_chamber_policy(policies) == name


def test_find_chamber_policy_not_found():
    name = "terraform-20220825080545657000000001"
    policies = {
        name: {
            "Statement": [
                {
                    "Action": [
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/xendit-infra/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/service-discovery/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/instamoney-api-gateway/*",
                        "arn:aws:kms:ap-southeast-1:************:key/846fab0e-feda-43c8-9d45-c1b99d69b8bd",
                    ],
                    "Sid": "",
                },
                {
                    "Action": [
                        "ssm:DescribeParameters",
                        "kms:ListKeys",
                        "kms:ListAliases",
                    ],
                    "Effect": "Allow",
                    "Resource": "*",
                    "Sid": "",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.find_chamber_policy(policies) is None


def test_extract_chamber_services():
    name = "terraform-20220825080545657000000001"
    policies = {
        name: {
            "Statement": [
                {
                    "Action": [
                        "ssm:GetParametersByPath",
                        "ssm:GetParameters",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/xendit-infra/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/service-discovery/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/instamoney-api-gateway/*",
                        "arn:aws:kms:ap-southeast-1:************:key/846fab0e-feda-43c8-9d45-c1b99d69b8bd",
                    ],
                    "Sid": "",
                },
                {
                    "Action": [
                        "ssm:DescribeParameters",
                        "kms:ListKeys",
                        "kms:ListAliases",
                    ],
                    "Effect": "Allow",
                    "Resource": "*",
                    "Sid": "",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_chamber_services(policies) == [
        "xendit-infra",
        "service-discovery",
        "instamoney-api-gateway",
    ]


def test_extract_chamber_services_not_found():
    name = "terraform-20220825080545657000000001"
    policies = {
        name: {
            "Statement": [
                {
                    "Action": [
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/xendit-infra/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/service-discovery/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/instamoney-api-gateway/*",
                        "arn:aws:kms:ap-southeast-1:************:key/846fab0e-feda-43c8-9d45-c1b99d69b8bd",
                    ],
                    "Sid": "",
                },
                {
                    "Action": [
                        "ssm:DescribeParameters",
                        "kms:ListKeys",
                        "kms:ListAliases",
                    ],
                    "Effect": "Allow",
                    "Resource": "*",
                    "Sid": "",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_chamber_services(policies) == []


def test_extract_chamber_modes():
    name = "terraform-20220825080545657000000001"
    policies = {
        name: {
            "Statement": [
                {
                    "Action": [
                        "ssm:GetParametersByPath",
                        "ssm:GetParameters",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/xendit-infra/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/service-discovery/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/instamoney-api-gateway/*",
                        "arn:aws:kms:ap-southeast-1:************:key/846fab0e-feda-43c8-9d45-c1b99d69b8bd",
                    ],
                    "Sid": "",
                },
                {
                    "Action": [
                        "ssm:DescribeParameters",
                        "kms:ListKeys",
                        "kms:ListAliases",
                    ],
                    "Effect": "Allow",
                    "Resource": "*",
                    "Sid": "",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_chamber_modes(policies) == [
        "live",
        "live",
        "live",
    ]


def test_extract_chamber_modes_not_found():
    name = "terraform-20220825080545657000000001"
    policies = {
        name: {
            "Statement": [
                {
                    "Action": [
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/xendit-infra/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/service-discovery/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/instamoney-api-gateway/*",
                        "arn:aws:kms:ap-southeast-1:************:key/846fab0e-feda-43c8-9d45-c1b99d69b8bd",
                    ],
                    "Sid": "",
                },
                {
                    "Action": [
                        "ssm:DescribeParameters",
                        "kms:ListKeys",
                        "kms:ListAliases",
                    ],
                    "Effect": "Allow",
                    "Resource": "*",
                    "Sid": "",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_chamber_modes(policies) == []


@pytest.mark.parametrize(
    "modes,expected",
    [
        ([], False),
        (["live", "live"], True),
        (["development", "development"], True),
        (["dev", "dev"], False),
        (["development", "live"], False),
    ],
)
def test_check_modes_consistency(modes, expected):
    assert lib.check_modes_consistency(modes) == expected


@pytest.mark.parametrize(
    "aws_profile,expected",
    [
        ("not-xendit", ""),
        ("xendit", "xendit"),
        ("xendit-staging", "xendit"),
        ("instamoney", "instamoney"),
        ("instamoney-staging", "instamoney"),
        ("iluma", "iluma"),
        ("iluma-staging", "iluma"),
    ],
)
def test_get_org(aws_profile, expected):
    assert lib.get_org(aws_profile) == expected


@pytest.mark.parametrize(
    "cluster,expected",
    [
        ("not-xendit", ""),
        ("trident-im-production-0", "production"),
        ("im-sg-prod-aws-0", "production"),
        ("trident-staging-0", "staging"),
        ("xnd-sg-stg-aws-0", "staging"),
    ],
)
def test_get_environment(cluster, expected):
    assert lib.get_environment(cluster) == expected


def test_render_tf_import_xnd():
    org = "xendit"
    env = "production"
    import_entries = [
        {
            "role_name": "test-role",
            "chamber_policy": "chamber-policy",
            "custom_policies": [],
        }
    ]

    expected = """
import {
  to = module.xnd["test-role"].aws_iam_role.this
  id = "test-role"
}

import {
  to = module.xnd["test-role"].aws_iam_role_policy.inline_credentials_attachment
  id = "test-role:chamber-policy"
}

"""

    assert lib.render_tf_import(org, env, import_entries) == expected


def test_render_tf_import_xnd_custom():
    org = "xendit"
    env = "production"
    import_entries = [
        {
            "role_name": "test-role",
            "chamber_policy": "chamber-policy",
            "custom_policies": [
                "test-custom-policy",
            ],
        }
    ]

    expected = """
import {
  to = module.xnd["test-role"].aws_iam_role.this
  id = "test-role"
}

import {
  to = module.xnd["test-role"].aws_iam_role_policy.inline_credentials_attachment
  id = "test-role:chamber-policy"
}

import {
  to = module.xnd["test-role"].aws_iam_role_policy.custom["test-custom-policy"]
  id = "test-role:test-custom-policy"
}

"""

    assert lib.render_tf_import(org, env, import_entries) == expected


def test_render_tf_import_xnd_stg():
    org = "xendit"
    env = "staging"
    import_entries = [
        {
            "role_name": "test-role",
            "chamber_policy": "chamber-policy",
            "custom_policies": [],
        }
    ]

    expected = """
import {
  to = module.xnd_stg["test-role"].aws_iam_role.this
  id = "test-role"
}

import {
  to = module.xnd_stg["test-role"].aws_iam_role_policy.inline_credentials_attachment
  id = "test-role:chamber-policy"
}

"""

    assert lib.render_tf_import(org, env, import_entries) == expected


def test_render_tf_import_im():
    org = "instamoney"
    env = "production"
    import_entries = [
        {
            "role_name": "test-role",
            "chamber_policy": "chamber-policy",
            "custom_policies": [],
        }
    ]

    expected = """
import {
  to = module.im["test-role"].aws_iam_role.this
  id = "test-role"
}

import {
  to = module.im["test-role"].aws_iam_role_policy.inline_credentials_attachment
  id = "test-role:chamber-policy"
}

"""

    assert lib.render_tf_import(org, env, import_entries) == expected


def test_render_tf_import_im_stg():
    org = "instamoney"
    env = "staging"
    import_entries = [
        {
            "role_name": "test-role",
            "chamber_policy": "chamber-policy",
            "custom_policies": [],
        }
    ]

    expected = """
import {
  to = module.im_stg["test-role"].aws_iam_role.this
  id = "test-role"
}

import {
  to = module.im_stg["test-role"].aws_iam_role_policy.inline_credentials_attachment
  id = "test-role:chamber-policy"
}

"""

    assert lib.render_tf_import(org, env, import_entries) == expected


def test_find_irsa_sns_sqs_policy():
    name = "terraform-20220825080545657000000001"
    policies = {
        f"{name}": {
            "Statement": [
                {
                    "Action": [
                        "sqs:ReceiveMessage",
                        "sqs:ListQueueTags",
                        "sqs:ListDeadLetterSourceQueues",
                        "sqs:GetQueueUrl",
                        "sqs:GetQueueAttributes",
                        "sqs:DeleteMessage",
                        "sqs:ChangeMessageVisibilityBatch",
                        "sqs:ChangeMessageVisibility",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-queue",
                        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-dlq",
                    ],
                },
                {
                    "Action": [
                        "sns:Publish",
                        "sns:ListSubscriptionsByTopic",
                        "sns:GetTopicAttributes",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc_stg-dev-topic",
                        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc-settlement_stg-dev-topic",
                    ],
                },
                {
                    "Action": [
                        "kms:GenerateDataKey*",
                        "kms:Encrypt",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": "arn:aws:kms:ap-southeast-1:705506614808:key/550a2b65-e69d-4e2c-aab3-0f4163ed60f8",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.find_irsa_sns_sqs_policy(policies) == name


def test_find_irsa_sns_sqs_policy_none():
    name = "terraform-20220825080545657000000001"
    policies = {
        f"{name}": {
            "Statement": [
                {
                    "Action": [
                        "ssm:GetParametersByPath",
                        "ssm:GetParameters",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/xendit-infra/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/service-discovery/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/instamoney-api-gateway/*",
                        "arn:aws:kms:ap-southeast-1:************:key/846fab0e-feda-43c8-9d45-c1b99d69b8bd",
                    ],
                    "Sid": "",
                },
                {
                    "Action": [
                        "ssm:DescribeParameters",
                        "kms:ListKeys",
                        "kms:ListAliases",
                    ],
                    "Effect": "Allow",
                    "Resource": "*",
                    "Sid": "",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.find_irsa_sns_sqs_policy(policies) is None


def test_find_irsa_custom_policies():
    name = "terraform-20220825080545657000000001"
    policies = {
        name: {
            "Statement": [
                {
                    "Action": [
                        "ssm:GetParametersByPath",
                        "ssm:GetParameters",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/xendit-infra/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/service-discovery/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/instamoney-api-gateway/*",
                        "arn:aws:kms:ap-southeast-1:************:key/846fab0e-feda-43c8-9d45-c1b99d69b8bd",
                    ],
                    "Sid": "",
                },
                {
                    "Action": [
                        "ssm:DescribeParameters",
                        "kms:ListKeys",
                        "kms:ListAliases",
                    ],
                    "Effect": "Allow",
                    "Resource": "*",
                    "Sid": "",
                },
            ],
            "Version": "2012-10-17",
        },
        "dbs-forex-connector-sns-policy_staging-development": {
            "Statement": [
                {
                    "Effect": "Allow",
                    "Action": "sns:*",
                    "Resource": "arn:aws:sns:*:705506614808:fa_dbs-forex-conn_stg-dev*",
                }
            ],
        },
    }

    assert lib.find_irsa_custom_policies(policies) == [
        "dbs-forex-connector-sns-policy_staging-development"
    ]


def test_find_irsa_custom_policies_not_found():
    name = "terraform-20220825080545657000000001"
    policies = {
        name: {
            "Statement": [
                {
                    "Action": [
                        "ssm:GetParametersByPath",
                        "ssm:GetParameters",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/xendit-infra/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/service-discovery/*",
                        "arn:aws:ssm:ap-southeast-1:************:parameter/staging/live/instamoney-api-gateway/*",
                        "arn:aws:kms:ap-southeast-1:************:key/846fab0e-feda-43c8-9d45-c1b99d69b8bd",
                    ],
                    "Sid": "",
                },
                {
                    "Action": [
                        "ssm:DescribeParameters",
                        "kms:ListKeys",
                        "kms:ListAliases",
                    ],
                    "Effect": "Allow",
                    "Resource": "*",
                    "Sid": "",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.find_irsa_custom_policies(policies) == []


def test_extract_kms_keys():
    kms_key = "arn:aws:kms:ap-southeast-1:705506614808:key/550a2b65-e69d-4e2c-aab3-0f4163ed60f8"
    policies = {
        "terraform-20220825080545657000000001": {
            "Statement": [
                {
                    "Action": [
                        "sqs:ReceiveMessage",
                        "sqs:ListQueueTags",
                        "sqs:ListDeadLetterSourceQueues",
                        "sqs:GetQueueUrl",
                        "sqs:GetQueueAttributes",
                        "sqs:DeleteMessage",
                        "sqs:ChangeMessageVisibilityBatch",
                        "sqs:ChangeMessageVisibility",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-queue",
                        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-dlq",
                    ],
                },
                {
                    "Action": [
                        "sns:Publish",
                        "sns:ListSubscriptionsByTopic",
                        "sns:GetTopicAttributes",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc_stg-dev-topic",
                        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc-settlement_stg-dev-topic",
                    ],
                },
                {
                    "Action": [
                        "kms:GenerateDataKey*",
                        "kms:Encrypt",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": kms_key,
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_kms_keys(policies) == [kms_key]


def test_extract_kms_keys_not_found():
    policies = {
        "terraform-20220825080545657000000001": {
            "Statement": [
                {
                    "Action": [
                        "sqs:ReceiveMessage",
                        "sqs:ListQueueTags",
                        "sqs:ListDeadLetterSourceQueues",
                        "sqs:GetQueueUrl",
                        "sqs:GetQueueAttributes",
                        "sqs:DeleteMessage",
                        "sqs:ChangeMessageVisibilityBatch",
                        "sqs:ChangeMessageVisibility",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-queue",
                        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-dlq",
                    ],
                },
                {
                    "Action": [
                        "sns:Publish",
                        "sns:ListSubscriptionsByTopic",
                        "sns:GetTopicAttributes",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc_stg-dev-topic",
                        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc-settlement_stg-dev-topic",
                    ],
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_kms_keys(policies) == []


def test_extract_published_sns():
    published_sns = [
        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc_stg-dev-topic",
        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc-settlement_stg-dev-topic",
    ]
    policies = {
        "terraform-20220825080545657000000001": {
            "Statement": [
                {
                    "Action": [
                        "sqs:ReceiveMessage",
                        "sqs:ListQueueTags",
                        "sqs:ListDeadLetterSourceQueues",
                        "sqs:GetQueueUrl",
                        "sqs:GetQueueAttributes",
                        "sqs:DeleteMessage",
                        "sqs:ChangeMessageVisibilityBatch",
                        "sqs:ChangeMessageVisibility",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-queue",
                        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-dlq",
                    ],
                },
                {
                    "Action": [
                        "sns:Publish",
                        "sns:ListSubscriptionsByTopic",
                        "sns:GetTopicAttributes",
                    ],
                    "Effect": "Allow",
                    "Resource": published_sns,
                },
                {
                    "Action": [
                        "kms:GenerateDataKey*",
                        "kms:Encrypt",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": "arn:aws:kms:ap-southeast-1:705506614808:key/550a2b65-e69d-4e2c-aab3-0f4163ed60f8",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_published_sns(policies) == published_sns


def test_extract_published_sns_not_found():
    policies = {
        "terraform-20220825080545657000000001": {
            "Statement": [
                {
                    "Action": [
                        "sqs:ReceiveMessage",
                        "sqs:ListQueueTags",
                        "sqs:ListDeadLetterSourceQueues",
                        "sqs:GetQueueUrl",
                        "sqs:GetQueueAttributes",
                        "sqs:DeleteMessage",
                        "sqs:ChangeMessageVisibilityBatch",
                        "sqs:ChangeMessageVisibility",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-queue",
                        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-dlq",
                    ],
                },
                {
                    "Action": [
                        "kms:GenerateDataKey*",
                        "kms:Encrypt",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": "arn:aws:kms:ap-southeast-1:705506614808:key/550a2b65-e69d-4e2c-aab3-0f4163ed60f8",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_published_sns(policies) == []


def test_extract_consumed_sqs():
    consumed_sqs = [
        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-queue",
        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-dlq",
    ]
    policies = {
        "terraform-20220825080545657000000001": {
            "Statement": [
                {
                    "Action": [
                        "sqs:ReceiveMessage",
                        "sqs:ListQueueTags",
                        "sqs:ListDeadLetterSourceQueues",
                        "sqs:GetQueueUrl",
                        "sqs:GetQueueAttributes",
                        "sqs:DeleteMessage",
                        "sqs:ChangeMessageVisibilityBatch",
                        "sqs:ChangeMessageVisibility",
                    ],
                    "Effect": "Allow",
                    "Resource": consumed_sqs,
                },
                {
                    "Action": [
                        "sns:Publish",
                        "sns:ListSubscriptionsByTopic",
                        "sns:GetTopicAttributes",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc_stg-dev-topic",
                        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc-settlement_stg-dev-topic",
                    ],
                },
                {
                    "Action": [
                        "kms:GenerateDataKey*",
                        "kms:Encrypt",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": "arn:aws:kms:ap-southeast-1:705506614808:key/550a2b65-e69d-4e2c-aab3-0f4163ed60f8",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_consumed_sqs(policies) == consumed_sqs


def test_extract_consumed_sqs_not_found():
    policies = {
        "terraform-20220825080545657000000001": {
            "Statement": [
                {
                    "Action": [
                        "sns:Publish",
                        "sns:ListSubscriptionsByTopic",
                        "sns:GetTopicAttributes",
                    ],
                    "Effect": "Allow",
                    "Resource": [
                        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc_stg-dev-topic",
                        "arn:aws:sns:ap-southeast-1:705506614808:forex_fx-conv-svc-settlement_stg-dev-topic",
                    ],
                },
                {
                    "Action": [
                        "kms:GenerateDataKey*",
                        "kms:Encrypt",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": "arn:aws:kms:ap-southeast-1:705506614808:key/550a2b65-e69d-4e2c-aab3-0f4163ed60f8",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_consumed_sqs(policies) == []


def test_extract_published_sqs():
    published_sqs = [
        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-queue",
        "arn:aws:sqs:ap-southeast-1:705506614808:forex_fx-scb-conn_stg-dev_crd-dbt-notif-dlq",
    ]
    policies = {
        "terraform-20220825080545657000000001": {
            "Statement": [
                {
                    "Action": [
                        "sqs:CancelMessageMoveTask",
                        "sqs:ChangeMessageVisibility",
                        "sqs:ChangeMessageVisibilityBatch",
                        "sqs:DeleteMessage",
                        "sqs:GetQueueAttributes",
                        "sqs:GetQueueUrl",
                        "sqs:ListDeadLetterSourceQueues",
                        "sqs:ListDeadLetterSourceQueues",
                        "sqs:ListMessageMoveTasks",
                        "sqs:ListQueueTags",
                        "sqs:ReceiveMessage",
                        "sqs:SendMessage",
                        "sqs:StartMessageMoveTask",
                    ],
                    "Effect": "Allow",
                    "Resource": published_sqs,
                },
                {
                    "Action": [
                        "kms:GenerateDataKey*",
                        "kms:Encrypt",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": "arn:aws:kms:ap-southeast-1:705506614808:key/550a2b65-e69d-4e2c-aab3-0f4163ed60f8",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_published_sqs(policies) == published_sqs


def test_extract_published_sqs_not_found():
    policies = {
        "terraform-20220825080545657000000001": {
            "Statement": [
                {
                    "Action": [
                        "kms:GenerateDataKey*",
                        "kms:Encrypt",
                        "kms:DescribeKey",
                        "kms:Decrypt",
                    ],
                    "Effect": "Allow",
                    "Resource": "arn:aws:kms:ap-southeast-1:705506614808:key/550a2b65-e69d-4e2c-aab3-0f4163ed60f8",
                },
            ],
            "Version": "2012-10-17",
        }
    }

    assert lib.extract_published_sqs(policies) == []
