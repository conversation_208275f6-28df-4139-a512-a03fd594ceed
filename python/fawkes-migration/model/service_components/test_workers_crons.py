import pytest
from unittest.mock import Mock, call
import copy

import kubernetes

from model import globals
from model import cluster, workload
from . import workers_crons as main

C = main.globals.cache

def setup(monkeypatch, replicas: list[int], suspends: list[bool]):
    c1 = cluster.Cluster("cluster1", Mock())
    monkeypatch.setattr(C, "source_cluster", c1)
    ns1 = c1.get_namespace("namespace1")

    deployments = [
        Mock(
            **{
                "metadata.name": "app1-worker",
                "metadata.namespace": ns1.name,
                "metadata.labels": {f"argocd.{c1.name}": "app1"},
                "spec.replicas": r,
            }
        )
        for r in replicas
    ]
    cronjobs = [
        Mock(
            **{
                "metadata.name": "app1-cron",
                "metadata.namespace": ns1.name,
                "metadata.labels": {f"argocd.{c1.name}": "app1"},
                "spec.suspend": s,
            }
        )
        for s in suspends
    ]
    ns1.deployments = {"app1-worker": workload.Deployment(c1, deployments[0])}
    ns1.cronjobs = {"app1-cron": workload.CronJob(c1, cronjobs[0])}

    app_manifest = {
        "metadata": {"name": "app1"},
        "spec": {
            "destination": {"namespace": ns1.name},
        },
        "status": {"resources": [
            {"kind": "Deployment", "name": "app1-worker"},
            {"kind": "CronJob", "name": "app1-cron"},
        ]},
    }
    c1.applications = {"app1": cluster.Application(c1, app_manifest)}
    #c1.applications["app1"].deployments = {k: v for k, v in ns1.deployments.items()}
    #c1.applications["app1"].cronjobs = {k: v for k, v in ns1.cronjobs.items()}

    c2 = copy.deepcopy(c1)
    monkeypatch.setattr(C, "dest_cluster", c2)
    c2.name = "cluster2"
    ns2 = c2.get_namespace(ns1.name)
    ns2.deployments["app1-worker"] = workload.Deployment(c2, deployments[1])
    ns2.cronjobs["app1-cron"] = workload.CronJob(c2, cronjobs[1])


@pytest.fixture()
def mock_k8s_client():
    return Mock()


@pytest.fixture(autouse=True)
def patch_inputs(monkeypatch):
    monkeypatch.setattr(globals.Inputs, "service", "app1")
    monkeypatch.setattr(globals.Inputs, "namespace", "namespace1")
    monkeypatch.setattr(globals.Inputs, "dry_run", False)

@pytest.fixture(autouse=True)
def mock_defaults(monkeypatch, mock_k8s_client):
    monkeypatch.setattr(kubernetes, "client", mock_k8s_client)


def patch_deployment(cluster, namespace, name, replicas):
    return (
        call.AppsV1Api(cluster.client_factory())
        .patch_namespaced_deployment(
            name,
            namespace,
            {"spec": {"replicas": replicas}}
            if replicas > 0
            else {
                "spec": {"replicas": 0},
                "metadata": {"labels": {"enable-workers-and-crons": None}},
            },
        )
        .call_list()
    )


def patch_cronjob(cluster, namespace, name, suspend):
    return (
        call.BatchV1Api(cluster.client_factory())
        .patch_namespaced_cron_job(
            name,
            namespace,
            {"spec": {"suspend": False}}
            if not suspend
            else {
                "spec": {"suspend": True},
                "metadata": {"labels": {"enable-workers-and-crons": None}},
            },
        )
        .call_list()
    )

@pytest.mark.parametrize("dry_run,workload_name,replicas,suspends,expected_mock_calls", [
    # move workloads from dest_cluster to source_cluster
    # use lambda because C.source_cluster can only be accessed after calling setup()
    (False, None, [0, 2], [True, False], lambda: [
        patch_deployment(C.source_cluster, "namespace1", "app1-worker", 2),
        patch_deployment(C.dest_cluster, "namespace1", "app1-worker", 0),
        patch_cronjob(C.dest_cluster, "namespace1", "app1-cron", True),
        patch_cronjob(C.source_cluster, "namespace1", "app1-cron", False),
    ]),
    # dry_run should do nothing
    (True, None, [0, 2], [True, False], lambda: []),
    # only rollback the specified workload
    (False, "app1-worker", [0, 2], [True, False], lambda: [
        patch_deployment(C.source_cluster, "namespace1", "app1-worker", 2),
        patch_deployment(C.dest_cluster, "namespace1", "app1-worker", 0),
    ]),
    # already moved, disable destination workloads
    (False, None, [2, 0], [False, True], lambda: [
        patch_deployment(C.dest_cluster, "namespace1", "app1-worker", 0),
        patch_cronjob(C.dest_cluster, "namespace1", "app1-cron", True),
        patch_cronjob(C.source_cluster, "namespace1", "app1-cron", False),
    ]),
])
def test_rollback(monkeypatch, mock_k8s_client, dry_run, workload_name, replicas, suspends, expected_mock_calls):
    setup(monkeypatch, replicas, suspends)
    monkeypatch.setattr(globals.Inputs, "dry_run", dry_run)
    main.rollback.callback(workload_name, True, True)

    assert mock_k8s_client.mock_calls == sum(expected_mock_calls(), [])


def test_rollback_warn(monkeypatch, mock_k8s_client):
    """Both sides are disabled, show a warning."""
    monkeypatch.setattr(main, "logging", Mock(wraps=main.logging))
    setup(monkeypatch, [0, 0], [True, True])
    main.rollback.callback(None, True, True)

    main.logging.warning.assert_called_with(
        "Deployment %s has zero replicas in both clusters!"
        " Please confirm this is intentional, if not then scale up the source deployment.",
        "app1-worker",
    )
    assert mock_k8s_client.mock_calls == patch_cronjob(
        C.dest_cluster, "namespace1", "app1-cron", True
    ) + patch_cronjob(C.source_cluster, "namespace1", "app1-cron", False)
