import json
import logging
import os
import signal
import sys
import traceback

import click
from click_repl import repl as _repl, register_repl
import kubernetes
import kubernetes_asyncio

from model import globals
from model.workload import DeploymentUnavailableError
from .workers_crons_strategy import MigrationStrategy


def signal_handler(signal, stack):
    if signal == signal.SIGTERM:
        sys.exit(1)


signal.signal(signal.SIGTERM, signal_handler)

G = globals
C = globals.cache
Inputs = globals.Inputs


def migrate_workers(workload, dry_run=False):
    src_cluster = C.source_cluster
    dest_cluster = C.dest_cluster

    logging.info(f"Migrating workers from {src_cluster.name} to {dest_cluster.name}")

    src_ns = src_cluster.get_namespace(Inputs.namespace)
    dest_ns = dest_cluster.get_namespace(Inputs.namespace)
    application = Inputs.service

    src_app = src_cluster.applications[application]
    deploys = []
    # Validate deployments
    validate_errors = []
    for d in sorted(src_app.worker_deployments):
        src_deploy = src_ns.deployments[d]
        if workload and d != workload:
            logging.debug(f"{d} is ignored, skipping")
            continue
        elif (Inputs.namespace, d) in IgnoredWorkloads.deployments:
            logging.debug(f"{d} is ignored, skipping")
            continue
        elif not d.endswith("worker"):
            logging.debug(f"{d} is not a worker, skipping")
            continue
        elif src_deploy.model.spec.replicas == 0:
            logging.debug(f"{d} has zero replicas in source cluster, skipping")
            continue

        if "automated" in src_app.model["spec"]["syncPolicy"]:
            validate_errors.append(
                f"{d} has autosync enabled. Please disable autosync on application {src_deploy.application_name} in source cluster first."
            )

        if d not in dest_ns.deployments:
            validate_errors.append(f"{d} does not exist in destination cluster")
        elif (s_images := [c.image for c in src_deploy.containers]) != (
            d_images := [c.image for c in dest_ns.deployments[d].containers]
        ):
            validate_errors.append(
                f"{d} container images mismatched, source={s_images} dest={d_images}"
            )

        deploys.append(d)

    for e in validate_errors:
        logging.error(e)
    if validate_errors:
        raise ValidationError("Cannot proceed until previous errors are fixed.")

    migrate_errors = []
    for d in deploys:
        logging.info(f"Migrating worker {d}" + (" (dry run)" if dry_run else ""))
        d1 = src_ns.deployments[d]
        d2 = dest_ns.deployments[d]
        m = MigrationStrategy.choose_strategy(d1, d2)()
        try:
            if dry_run:
                continue
            m.migrate(d1, d2)
        except DeploymentUnavailableError as e:
            logging.error(e)
            migrate_errors.append(e)
            raise

    assert not migrate_errors


def migrate_cronjobs(workload, dry_run=False):
    src_cluster = C.source_cluster
    dest_cluster = C.dest_cluster

    logging.info(f"Migrating cronjobs from {src_cluster.name} to {dest_cluster.name}")

    src_ns = src_cluster.get_namespace(Inputs.namespace)
    dest_ns = dest_cluster.get_namespace(Inputs.namespace)
    application = Inputs.service

    src_app = src_cluster.applications[application]
    cronjobs = []
    validate_errors = []
    for cj in sorted(src_app.cronjobs.keys()):
        src_cj = src_ns.cronjobs[cj]
        if workload and cj != workload:
            logging.debug(f"{cj} is ignored, skipping")
            continue
        elif (Inputs.namespace, cj) in IgnoredWorkloads.cronjobs:
            logging.debug(f"{cj} is ignored, skipping")
            continue
        elif src_ns.cronjobs[cj].model.spec.suspend:
            logging.debug(f"{cj} is suspended in source cluster, skipping")
            continue

        if "automated" in src_app.model["spec"]["syncPolicy"]:
            validate_errors.append(
                f"{cj} has autosync enabled. Please disable autosync on application {src_cj.application_name} in source cluster first."
            )

        if cj not in dest_ns.cronjobs:
            validate_errors.append(f"{cj} does not exist in destination cluster")

        cronjobs.append(cj)

    for e in validate_errors:
        logging.error(e)
    if validate_errors:
        raise ValidationError("Cannot proceed until previous errors are fixed.")

    for cj in cronjobs:
        logging.info(f"Migrating cronjob {cj}" + (" (dry run)" if dry_run else ""))
        src_cj = src_ns.cronjobs[cj]
        dest_cj = dest_ns.cronjobs[cj]
        if dry_run:
            continue
        try:
            src_cj.set_suspended(True)
            dest_cj.set_suspended(False, set_label=True)
        except (kubernetes.client.ApiException, KeyboardInterrupt, SystemExit):
            src_cj.set_suspended(False)
            dest_cj.set_suspended(True, remove_label=True)
            raise


class ValidationError(Exception):
    pass


class IgnoredWorkloads:
    # Workloads to ignore in the source cluster
    deployments = [
        (
            "dev-platform-live",
            "convoy-worker",
        ),  # Moved to convoy-dev namespace in xnd-sg-stg-aws-0
        (
            "dev-platform-live",
            "retool-retool-wf-temporal-temporal-worker",
        ),  # non-xendit deployment
        ("dev-platform-live", "retool-workflow-worker"),  # non-xendit deployment
    ]
    cronjobs = [
        (
            "dev-platform-live",
            "convoy-migrate-down",
        ),  # Moved to convoy-dev namespace in xnd-sg-stg-aws-0
        (
            "dev-platform-live",
            "convoy-migrate-up",
        ),  # Moved to convoy-dev namespace in xnd-sg-stg-aws-0
    ]


@click.group(invoke_without_command=True)
@click.pass_context
def cli(ctx, **kwargs):
    if ctx.invoked_subcommand is None:
        ctx.invoke(repl)

    pass


@cli.command(name="backup", help="Save cluster resources to json files.")
@click.option(
    "--overwrite",
    default=True,
)
def backup_cmd(overwrite):
    assert Inputs.service
    # load_cluster_data()
    backup(overwrite)


def backup(overwrite):
    # Save cluster states
    # Output files can be applied with `kubectl apply -f`
    for c in [C.source_cluster, C.dest_cluster]:
        file = f"backup-{c.name}-{Inputs.service}.json"
        if not os.path.exists(file) or overwrite:
            logging.info(f"Saving workers and crons to {file}")
            json.dump(c.saveSnapshot(Inputs.service), open(file, "w"), indent=2)


@cli.command(help="Reload cluster data, useful in interactive mode only.")
def load():
    pass
    # load_cluster_data(True)


@cli.command(help="Rollback cluster resources to previous backup.")
def restore_from_backup():
    # Rollback to backup state
    logging.info("Rolling back destination cluster")
    C.dest_cluster.rollbackFromSnapshot(
        json.load(open(f"backup-{C.dest_cluster.name}-{Inputs.service}.json")),
        remove_label=True,
    )
    logging.info("Rolling back source cluster")
    C.source_cluster.rollbackFromSnapshot(
        json.load(open(f"backup-{C.source_cluster.name}-{Inputs.service}.json"))
    )

    # Reload clusters
    # load_cluster_data(True)


@cli.command(
    help="Move all running workloads back to source cluster. Inverse of migrate operation."
)
@click.option(
    "--workload",
    help="Name of deployment/cronjob to migrate. Leave unset to migrate all deployments and cronjobs in namespace.",
)
@click.option(
    "--workers",
    default=True,
    help="Enable/disable migrating worker deployments in namespace.",
)
@click.option(
    "--cronjobs",
    default=True,
    help="Enable/disable migrating cronjobs in namespace.",
)
def rollback(workload, workers, cronjobs):
    # load_cluster_data()
    dry_run = Inputs.dry_run

    src_ns = C.source_cluster.get_namespace(Inputs.namespace)
    dest_ns = C.dest_cluster.get_namespace(Inputs.namespace)

    for d in (
        C.source_cluster.applications[Inputs.service].worker_deployments
        if workers
        else []
    ):
        if workload and d != workload:
            continue

        logging.info(f"Rolling back worker {d}" + (" (dry run)" if dry_run else ""))
        src_d = src_ns.deployments[d]
        dest_d = dest_ns.deployments[d]
        if dry_run:
            continue
        else:
            if src_d.desired_replicas > 0:
                dest_d.scale(0, remove_label=True)
            elif dest_d.desired_replicas > 0:
                src_d.scale(dest_d.desired_replicas)
                dest_d.scale(0, remove_label=True)
            else:
                logging.warning(
                    "Deployment %s has zero replicas in both clusters!"
                    " Please confirm this is intentional, if not then scale up the source deployment.",
                    d,
                )

    for cj in (
        C.source_cluster.applications[Inputs.service].cronjobs if cronjobs else []
    ):
        if workload and cj != workload:
            continue

        logging.info(f"Rolling back cronjob {cj}" + (" (dry run)" if dry_run else ""))
        src_cj = src_ns.cronjobs[cj]
        dest_cj = dest_ns.cronjobs[cj]
        if dry_run:
            continue
        else:
            dest_cj.set_suspended(True, remove_label=True)
            src_cj.set_suspended(False)


@cli.command(name="list", help="List cluster resources to migrate.")
@click.option("--include-non-workers", default=False, is_flag=True)
@click.option(
    "--ignore-application",
    default=False,
    is_flag=True,
    help="List all workloads in namespace regardless of --application option",
)
def list_(include_non_workers, ignore_application):
    # load_cluster_data()
    src_ns = C.source_cluster.get_namespace(Inputs.namespace)
    dest_ns = C.dest_cluster.get_namespace(Inputs.namespace)

    print(
        "\n".join(
            ([f"Application: {Inputs.service}"] if not ignore_application else [])
            + [
                f"Namespace: {Inputs.namespace}",
                f"Source cluster: {C.source_cluster.name}",
                f"Destination cluster: {C.dest_cluster.name}",
                "Deployments (name, source_replicas, dest_replicas):",
            ]
        )
    )

    app = C.source_cluster.applications[Inputs.service]
    deployments = app.deployments if include_non_workers else app.worker_deployments
    width = max((len(d) for d in deployments), default=0)
    for d in deployments:
        if (Inputs.namespace, d) in IgnoredWorkloads.deployments:
            continue
        d1 = src_ns.deployments[d]
        d2 = dest_ns.deployments.get(d)
        print(
            "  {:{width}} {} {}".format(
                d,
                d1.model.spec.replicas,
                (d2.model.spec.replicas if d2 else "N/A"),
                width=width,
            )
        )

    print("Cronjobs (name, source_suspend, dest_suspend):")
    width = max((len(cj) for cj in app.cronjobs), default=0)
    for cj, cj1 in app.cronjobs.items():
        if (Inputs.namespace, cj) in IgnoredWorkloads.cronjobs:
            continue
        cj2 = dest_ns.cronjobs.get(cj)
        print(
            "  {:{width}} {} {}".format(
                cj,
                cj1.model.spec.suspend,
                (cj2.model.spec.suspend if cj2 else "N/A"),
                width=width,
            )
        )


@cli.command(help="Migrate deployments and cronjobs between clusters.")
@click.option(
    "--workload",
    help="Name of deployment/cronjob to migrate. Leave unset to migrate all deployments and cronjobs in namespace.",
)
@click.option(
    "--workers",
    default=True,
    help="Enable/disable migrating worker deployments in namespace.",
)
@click.option(
    "--cronjobs",
    default=True,
    help="Enable/disable migrating cronjobs in namespace.",
)
@click.option(
    "--dry-run",
    default=True,
)
def migrate(workload, workers, cronjobs, dry_run):
    dry_run = Inputs.dry_run or dry_run

    app = C.source_cluster.applications[Inputs.service]

    # Inputs.workload = workload
    # load_cluster_data()
    if not dry_run:
        backup(False)

    if workload and workload not in list(app.deployments) + list(app.cronjobs):
        logging.error(
            f"{workload} is not a deployment or cronjob in {C.source_cluster.name}"
        )
        return False

    try:
        if workers:
            migrate_workers(workload, dry_run=dry_run)
        if cronjobs:
            migrate_cronjobs(workload, dry_run=dry_run)
        logging.info("Migration done")
    except (
        DeploymentUnavailableError,
        KeyboardInterrupt,
        ValidationError,
        kubernetes.client.ApiException,
        kubernetes_asyncio.client.ApiException,
    ) as e:
        if Inputs.debug:
            traceback.print_exception(e)
        print(f"{e.__class__.__qualname__}: {e}")
        return False


@cli.command(help="Start a Python debugger session.")
def pdb():
    breakpoint()


@cli.command(help="Start an interactive session. Default when no command is given.")
def repl():
    _repl(click.get_current_context())


if __name__ == "__main__":
    register_repl(cli)
    cli()
