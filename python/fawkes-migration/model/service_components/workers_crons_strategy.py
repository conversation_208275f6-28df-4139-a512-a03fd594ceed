import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor

from model.workload import Deployment, DeploymentUnavailableError
import kubernetes
import kubernetes_asyncio


class MigrationStrategy:
    @staticmethod
    def choose_strategy(source: Deployment, destination: Deployment):
        if source.model.spec.replicas == 1:
            s = SingleReplica
        elif source.model.spec.replicas >= 1:
            s = MultipleReplicas

        return s

    def wait_pods_available(self, deployment):
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            loop = None
        if loop:
            # Running in Jupyter, need new thread
            ThreadPoolExecutor().submit(
                asyncio.run, deployment.wait_pods_available()
            ).result()
        else:
            asyncio.run(deployment.wait_pods_available())


class MultipleReplicas(MigrationStrategy):
    def migrate(self, source, destination, replicas=None):
        replicas = replicas or source.model.spec.replicas
        assert replicas > 0
        destination.scale(replicas, set_label=True)
        try:
            self.wait_pods_available(destination)
            source.scale(0)
        except DeploymentUnavailableError:
            # destination.scale(0)
            raise


class SingleReplica(MigrationStrategy):
    def migrate(self, source, destination):
        replicas = 1
        try:
            source.scale(0)
            destination.scale(replicas, set_label=True)
            self.wait_pods_available(destination)
        except (
            DeploymentUnavailableError,
            kubernetes.client.ApiException,
            kubernetes_asyncio.client.ApiException,
            KeyboardInterrupt,
            SystemExit,
        ):
            source.scale(replicas)
            destination.scale(0, remove_label=True)
            raise
