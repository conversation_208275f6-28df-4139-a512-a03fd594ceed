import pytest
import kubernetes

from .cluster import Cluster

@pytest.fixture
def kwok_client_factory(constants):
    return lambda : kubernetes.client.ApiClient(
        configuration=kubernetes.client.Configuration(host=f'http://localhost:{constants.SOURCE_PORT}')
    )

@pytest.fixture
def cluster(kwok_client_factory):
    return Cluster("source", kwok_client_factory)

def test_cluster(cluster):
    res = cluster.get_api_resource(kind='Namespace')
    ns1 = cluster.get_resource_instances(res, name='default')
    assert ns1.metadata.name == 'default'

    ns2 = cluster.api_call(kubernetes.client.CoreV1Api, 'read_namespace', name='default')
    assert ns2.metadata.name == 'default'

    ns_list = cluster.get_resource_instances(res).items
    assert set([
        'default', 'kube-system', 'argocd', 'digipay-dev'
    ]) <= set(ns.metadata.name for ns in ns_list)

    app = cluster.applications['ewallet-service-v2-development']
    assert app

    assert len(app.deployments)
    assert len(app.cronjobs)
