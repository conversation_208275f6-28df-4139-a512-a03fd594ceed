from . import globals

def _test_cluster(cluster, name):
    assert cluster.name == name
    client = cluster.dynamic_client
    ns_list = client.resources.get(api_version='v1', kind='Namespace').get().items
    assert {'default', 'kube-system'} <= set(ns.metadata.name for ns in ns_list)

def test_source_cluster():
    name = globals.Inputs.source_cluster
    cluster = globals.cache.source_cluster
    _test_cluster(cluster, name)

def test_dest_cluster():
    name = globals.Inputs.dest_cluster
    cluster = globals.cache.dest_cluster
    _test_cluster(cluster, name)
