[project]
name = "fawkes-migration"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "awscli>=1.40.14",
    "awsipranges>=0.3.3",
    "boto3>=1.38.15",
    "click-repl>=0.3.0",
    "click>=8.1.8",
    "jinja2>=3.1.6",
    "kubernetes>=32.0.1",
    "ruamel-yaml>=0.18.10",
    "kubernetes-asyncio>=32.3.2",
    "pytest>=8.3.5",
]
[tool.pytest.ini_options]
log_cli = "True"
log_cli_level = "INFO"
pythonpath = "../library"
