INFO:root:Running the script in dry-run=False mode=live namespace=('all',)
INFO:botocore.tokens:Loading cached SSO token for xendit
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:botocore.tokens:Loading cached SSO token for xendit
INFO:botocore.tokens:Loading cached SSO token for xendit
INFO:botocore.tokens:Loading cached SSO token for xendit
INFO:botocore.tokens:Loading cached SSO token for xendit
INFO:botocore.tokens:Loading cached SSO token for xendit
INFO:botocore.tokens:Loading cached SSO token for gmf
INFO:botocore.tokens:Loading cached SSO token for xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=auth-platform-prod-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
WARNING:root:Primary destination record is not exist on Route53 host=auth-platform-prod-live-pci-sg-jkt.tidnex.com
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
WARNING:root:Primary destination record is not exist on Route53 host=auth-platform-prod-live-pci-or-sg.tidnex.com
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=xendit-authentication-service-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Fawkes destination is not added to team centric since the primary destination does not exist host=accounts-service-pci-prod-live.ap-southeast-1.tidnex.com
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=api-auth-service-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit live.tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=api-auth-service-prod-live.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=api-auth.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=api-crypto-signatures-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=api-crypto-signatures.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=api-logs-service-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=api-logs-worker-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=api-settings-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=api-settings.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=oauth-service-v2-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=webhook-service-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit live.tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=webhooks-service-prod-live.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=xendit-api-gateway-pci-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=xendit-gateway-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-fundamentals input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=xendit-billing-service-prod-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-financial-group input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=credit-card-settings-service-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit pci.tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=credit-card-settings-service-live-pci.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=cc-settings-service.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=cc-settings-service.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=mid-onboarding-automation-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit pci.tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=mid-onboarding-automation-live-pci.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit pci.tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=promotion-service-live-pci.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Adding Fawkes destination to team centric host=promotion-service.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=money-in-healthcheck-prod-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:botocore.tokens:Loading cached SSO token for xendit
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging xendit.co
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit xendit.co
INFO:root:Update/Insert Fawkes destination for stack=team-payment-growth host=money-in-healthcheck.xendit.co
INFO:root:Adding Fawkes destination to team centric host=money-in-healthcheck.xendit.co set_id=fawkes stack=team-payment-growth input_file=input-r53-xendit.co.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=xendit-deposit-service-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=xendit-deposit-service.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=end-user-consent-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-core-payments input_file=input-r53-tidnex.com.yaml
INFO:root:Skipping ingress since it's already on Cloudflare host=end-user-consent-live-public.xendit.co
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Fawkes destination is not added to team centric since the primary destination does not exist host=payment-auto-recon-prod-live.ap-southeast-1.tidnex.com
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=payment-setting-service-audit-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-core-payments input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=payment-setting-service-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-core-payments input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=unified-payments-data-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-core-payments input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=bi-snap-mapper-api-prod-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=payment-account-linking-intent-service-prod-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging xendit.co
INFO:root:Getting records in hosted zone: xendit xendit.co
INFO:root:Getting public hosted zones: gmf
INFO:root:Update/Insert Fawkes destination for stack=team-merchant-interfaces host=payments-processor.xendit.co
INFO:root:Adding Fawkes destination to team centric host=payments-processor.xendit.co set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-xendit.co.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Adding Fawkes destination to team centric host=charge-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=charge-prod-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit xendit.co
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging xendit.co
INFO:root:Getting public hosted zones: gmf
INFO:root:Update/Insert Fawkes destination for stack=team-merchant-interfaces host=charge.xendit.co
INFO:root:Adding Fawkes destination to team centric host=charge.xendit.co set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-xendit.co.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=payment-method-service-v2-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit xendit.co
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging xendit.co
INFO:root:Getting public hosted zones: gmf
INFO:root:Update/Insert Fawkes destination for stack=team-merchant-interfaces host=payment-method-service.xendit.co
INFO:root:Adding Fawkes destination to team centric host=payment-method-service.xendit.co set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-xendit.co.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=payments-processor-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=refund-service-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Adding Fawkes destination to team centric host=payments-processor-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Adding Fawkes destination to team centric host=payments-processor-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Adding Fawkes destination to team centric host=payment-method-service-v2-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Update/Insert Fawkes destination for stack=team-merchant-interfaces host=payments-processor.xendit.co
INFO:root:Adding Fawkes destination to team centric host=payments-processor.xendit.co set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-xendit.co.yaml
INFO:root:Update/Insert Fawkes destination for stack=team-merchant-interfaces host=payment-method-service.xendit.co
INFO:root:Adding Fawkes destination to team centric host=payment-method-service.xendit.co set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-xendit.co.yaml
INFO:root:Adding Fawkes destination to team centric host=payments-processor-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Adding Fawkes destination to team centric host=charge-prod-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Update/Insert Fawkes destination for stack=team-merchant-interfaces host=payments-processor.xendit.co
INFO:root:Adding Fawkes destination to team centric host=payments-processor.xendit.co set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-xendit.co.yaml
INFO:root:Update/Insert Fawkes destination for stack=team-merchant-interfaces host=charge.xendit.co
INFO:root:Adding Fawkes destination to team centric host=charge.xendit.co set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-xendit.co.yaml
INFO:root:Adding Fawkes destination to team centric host=payments-processor-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Adding Fawkes destination to team centric host=refund-service-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-interfaces input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit live.tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=dashboard-gateway-platform-live.tidnex.com set_id=fawkes stack=team-dashboard input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=dashboard-gateway-platform-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-dashboard input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Update/Insert Fawkes destination for stack=team-dashboard host=dashboard-gateway.ap-southeast-1.tidnex.com
INFO:root:Adding Fawkes destination to team centric host=dashboard-gateway.ap-southeast-1.tidnex.com set_id=fawkes stack=team-dashboard input_file=input-r53-tidnex.com.yaml
INFO:root:Skipping ingress since it's already on Cloudflare host=dashboard-gateway.xendit.co
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Adding Fawkes destination to team centric host=xendit-file-service-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-foundation input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=file-service.tidnex.com set_id=fawkes stack=team-foundation input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Adding Fawkes destination to team centric host=notification-service-prod-live-pci-ap-southeast-1.tidnex.com set_id=fawkes stack=team-foundation input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting records in hosted zone: xendit pci.tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=notification-service-prod-live-pci.tidnex.com set_id=fawkes stack=team-foundation input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fingerprint-service-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fraud-detection-service-legacy-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fraud-feature-manager-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fraud-feature-manager-legacy-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fraud-feature-manager.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit live.tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=fraud-feature-manager-prod-live.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fraud-rule-manager-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fraud-rule-manager-legacy-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit live.tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fraud-rule-manager-prod-live.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fraud-rule-manager.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fraud-static-data-pvd-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit pci.tidnex.com
INFO:root:Adding Fawkes destination to team centric host=fraud-static-data-pvd-prod-live-pci.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=fraud-static-data-pvd.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=protego-prod-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting records in hosted zone: xendit pci.tidnex.com
INFO:root:Adding Fawkes destination to team centric host=protego-prod-live-pci.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Skipping ingress since it's not resolvable to any ip addresses host=fraud-detection-service-prod-live.us-west-2.tidnex.com
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Adding Fawkes destination to team centric host=fds.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Skipping ingress since it's already on Cloudflare host=xenshield-prod-live.xendit.co
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=xendit-key-aggregator-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit live.tidnex.com
INFO:root:Adding Fawkes destination to team centric host=xendit-key-aggregator-prod-live.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=key-aggregator.tidnex.com set_id=fawkes stack=team-payment-growth input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Update/Insert Fawkes destination for stack=team-bizops host=xendit-admin-dashboard-prod-live.ap-southeast-1.tidnex.com
INFO:root:Adding Fawkes destination to team centric host=xendit-admin-dashboard-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-bizops input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit live.tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Update/Insert Fawkes destination for stack=team-bizops host=admin-dashboard-gateway-live.tidnex.com
INFO:root:Adding Fawkes destination to team centric host=admin-dashboard-gateway-live.tidnex.com set_id=fawkes stack=team-bizops input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=xendit-kyc-service-prod-live-pci.ap-southeast-1.tidnex.com set_id=fawkes stack=team-onboarding-accounts input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=xnd-biz-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-onboarding-accounts input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=xnd-biz-prod-live.us-west-2.tidnex.com set_id=fawkes stack=team-onboarding-accounts input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
WARNING:root:Primary destination record is not exist on Route53 host=business-service.tidnex.com
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=chargeback-service-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-bizops input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=chargeback.tidnex.com set_id=fawkes stack=team-bizops input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=eval-service-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-bizops input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=limits-service-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-bizops input_file=input-r53-tidnex.com.yaml
INFO:root:Skipping ingress since it's already on Cloudflare host=tpi.xendit.co
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=webhooks-service-opensearch.ap-southeast-1.tidnex.com set_id=fawkes stack=team-tpi input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=transaction-service-v4-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-financial-group input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=txn-service-v4-pci-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-financial-group input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=transaction-service-v4.tidnex.com set_id=fawkes stack=team-merchant-financial-group input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=unified-transaction-view-service-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-merchant-financial-group input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=xendit-platform-service.tidnex.com set_id=fawkes stack=team-xenplatform input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting public hosted zones: gmf
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Adding Fawkes destination to team centric host=xendit-platform-service.ap-southeast-1.tidnex.com set_id=fawkes stack=team-xenplatform input_file=input-r53-tidnex.com.yaml
INFO:root:Getting public hosted zones: iluma
INFO:root:Getting public hosted zones: iluma-staging
INFO:root:Getting public hosted zones: instamoney
INFO:root:Getting public hosted zones: instamoney-staging
INFO:root:Getting public hosted zones: xendit
INFO:root:Getting records in hosted zone: xendit tidnex.com
INFO:root:Getting public hosted zones: xendit-staging
INFO:root:Getting records in hosted zone: xendit-staging tidnex.com
INFO:root:Getting public hosted zones: gmf
INFO:root:Adding Fawkes destination to team centric host=xendit-platform-transaction-service-prod-live.ap-southeast-1.tidnex.com set_id=fawkes stack=team-xenplatform input_file=input-r53-tidnex.com.yaml
INFO:root:Summary: total_ingress=185 processed_ingress=107 start_index=0 end_index=185 ok=97 warning=5 skip_cf=4 skip_unresolvable=1