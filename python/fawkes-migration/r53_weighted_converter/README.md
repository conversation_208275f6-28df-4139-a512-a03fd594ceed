# Route53 Weighted Record Converter

This tools convert Route53 CNAME record from simple routing to weighted routing.

A second script is included named `convert_in_teamcentric.py` that does the same thing but accepts a team-centric route53 yaml file as input.

## Prerequisite

- [uv](https://docs.astral.sh/uv/)

If running in container:

- Docker
- make

## Run

1. Install python dependencies using `uv sync --frozen`
1. Run the script using `uv run main.py`
   Example of running the script to target all modes and namespaces on ts0

   ```shell
   uv run main.py --aws-profile xendit-staging --region ap-southeast-1 --cluster trident-staging-0 --mode all --dry-run false
   ```


If you prefer running on Docker container:

1. Export AWS credentials to `~/.aws/credentials` for the following profile
    - iluma
    - iluma-staging
    - instamoney
    - instamoney-staging
    - xendit
    - xendit-staging
1. Get shell to run the script, `make shell`
1. Install python dependencies using `uv sync --frozen`
1. Run the conversion, `uv run main.py`

### Team-centric variant

For cases where the team-centric route53 yaml files are the input and they contain simple CNAME records, use the other script and pass the path to the relevant yaml file.

```shell
uv run convert_in_teamcentric.py --yaml-file ../../terraform/xendit/team-regional-orchestration/input-r53-stg.tidnex.dev.yaml --aws-profile xendit-staging --dry-run false
```

Note that this will not make any modifications to the yaml file. It will directly change the R53 records from simple to weighted via the AWS API. It will be the user's responsibility to make the necessary changes to the yaml file afterwards.

## Rollback

If you need to rollback changes, run the following command

```shell
uv run rollback.py --source r53-[cluster-name].yaml
```
