#!/usr/bin/env python3

import os
import sys
import logging
import click
import ruamel.yaml

sys.path.append("../library")

from infra_r53 import get_public_hosted_zone
from infra_lib import get_ip_list
from lib import convert_weighted_record, is_simple_record_set


def process_yaml_file(
    yaml_file, dry_run=True, identifier="mass_conversion", aws_profile=None
):
    """
    Process the YAML file and convert non-weighted records to weighted records.

    Args:
        yaml_file: Path to the YAML file
        dry_run: Whether to run in dry-run mode
        identifier: Set identifier for converted records
        aws_profile: AWS profile name to use

    Returns:
        tuple: (total_records, converted_records, skipped_records)
    """
    # Check if AWS profile is provided
    if not aws_profile and not dry_run:
        logging.warning("AWS profile not provided. Using default credentials.")

    # Load the YAML file
    yaml = ruamel.yaml.YAML()
    yaml.preserve_quotes = True

    try:
        with open(yaml_file, "r") as f:
            data = yaml.load(f)
    except Exception as e:
        logging.error(f"Error loading YAML file: {e}")
        return 0, 0, 0

    zone_name = data.get("zone_name")
    if not zone_name:
        logging.error("No zone_name found in YAML file")
        return 0, 0, 0

    # Get the hosted zone ID
    hosted_zone_id = None
    if not dry_run:
        # Use the get_public_hosted_zone function from the library
        hosted_zones = get_public_hosted_zone(aws_profile)
        if zone_name in hosted_zones:
            # Extract the ID (remove the /hostedzone/ prefix)
            hosted_zone_id = hosted_zones[zone_name]["Id"].replace("/hostedzone/", "")
        else:
            logging.error(f"Could not find hosted zone ID for {zone_name}")
            return 0, 0, 0

    records = data.get("records", [])
    total_records = len(records)
    converted_records = 0
    skipped_records = 0

    for record in records:
        # Check if it's a weighted record or a simple record
        if "weighted" in record and record["weighted"]["enabled"]:
            # Already a weighted record, skip
            skipped_records += 1
            logging.info(f"Skipping {record['name']} as it's already a weighted record")
            continue

        # Format the record for Route53
        record_set = format_record_for_route53(record, zone_name)

        logging.info(f"Processing record: {record['name']}")

        if not dry_run:
            # Check if record is CNAME
            if record_set["Type"] != "CNAME":
                logging.info(
                    f"Skipping conversion of {record_set['Name']} since record type is not CNAME: {record_set['Type']}"
                )
                skipped_records += 1
                continue

            # Check if record is using simple routing
            if not is_simple_record_set(record_set):
                logging.info(
                    f"Skipping conversion of {record_set['Name']} since record routing type is not simple"
                )
                skipped_records += 1
                continue

            # Get IP addresses before conversion
            before_conversion = set(get_ip_list(record_set["Name"].rstrip(".")))

            try:
                # Call the convert_weighted_record function from lib.py
                convert_weighted_record(
                    record_set=record_set,
                    identifier=identifier,
                    hosted_zone_id=hosted_zone_id,
                    aws_profile=aws_profile,
                )

                # Verify the conversion by checking IP addresses
                after_conversion = set(get_ip_list(record_set["Name"].rstrip(".")))

                if before_conversion != after_conversion:
                    logging.error(
                        f"Conversion of {record_set['Name']} produced different DNS lookup result. Stop conversion immediately"
                    )
                    return total_records, converted_records, skipped_records

                converted_records += 1
            except Exception as e:
                logging.error(f"Error converting record {record_set['Name']}: {e}")
                skipped_records += 1
        else:
            # In dry-run mode, just log what would be done
            logging.info(
                f"Would convert {record['name']} to weighted routing with identifier {identifier}"
            )
            converted_records += 1

    return total_records, converted_records, skipped_records


def format_record_for_route53(record, zone_name):
    """
    Format a record from the YAML file to match the Route53 API format.

    Args:
        record: Record from the YAML file
        zone_name: The zone name to append to the record name

    Returns:
        dict: Formatted record for Route53 API
    """
    # Create a basic record set
    record_set = {
        "Name": f"{record['name']}.{zone_name}",
        "Type": record["type"],
        "TTL": record["ttl"],
    }

    # It's a simple record, add the resource records
    resource_records = []
    for value in record["values"]:
        resource_records.append({"Value": value})

    record_set["ResourceRecords"] = resource_records

    return record_set


@click.command()
@click.option(
    "--yaml-file", required=True, help="Path to the YAML file containing records"
)
@click.option(
    "--identifier",
    default="mass_conversion",
    show_default=True,
    help="Set identifier for converted records",
)
@click.option(
    "--dry-run",
    default=True,
    show_default=True,
    help="Flag to indicate whether the script will run in dry run mode",
)
@click.option("--aws-profile", help="AWS profile name to use for authentication")
def main(yaml_file: str, identifier: str, dry_run: bool, aws_profile: str):
    """Convert Route53 records to weighted routing based on a YAML file."""

    # Check if the YAML file exists
    if not os.path.isfile(yaml_file):
        logging.error(f"YAML file not found: {yaml_file}")
        sys.exit(1)

    # Process the YAML file
    logging.info(f"Running in dry-run={dry_run}")

    total, converted, skipped = process_yaml_file(
        yaml_file=yaml_file,
        dry_run=dry_run,
        identifier=identifier,
        aws_profile=aws_profile,
    )

    logging.info(
        f"Summary: total_records={total} converted={converted} skipped={skipped}"
    )

    if dry_run and converted > 0:
        logging.info("To apply these changes, run with the --dry-run false")


if __name__ == "__main__":
    main()
