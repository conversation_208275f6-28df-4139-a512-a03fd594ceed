from botocore.config import Config
import boto3
import logging


def convert_weighted_record(
    record_set: dict, identifier: str, hosted_zone_id: str, aws_profile: str
):
    """
    Convert given record set to weighted routing policy.
    Conversion logic handles both alias and non alias record set.
    Conversion is skipped when given record set is neither CNAME type nor simple routing.

    Args:
        record_set: Record set that will be converted to weighted routing policy
        identifier: Set identifier for converted record
        hosted_zone_id: ID of hosted zone where record set is located
        aws_profile: AWS profile name that will be used to convert record set
    """
    config = Config(retries={"max_attempts": 10, "mode": "adaptive"})
    session = boto3.Session(profile_name=aws_profile)
    client = session.client("route53", config=config)

    if record_set["Type"] != "CNAME":
        print(
            f"Skipping conversion of {record_set['Name']} since record type is not CNAME: {record_set['Type']}"
        )
        return

    if not is_simple_record_set(record_set):
        print(
            f"Skipping conversion of {record_set['Name']} since record routing type is not simple: {record_set['SetIdentifier']}"
        )
        return

    try:
        if "AliasTarget" in record_set:
            # Handle record set that uses alias
            client.change_resource_record_sets(
                HostedZoneId=hosted_zone_id,
                ChangeBatch={
                    "Comment": "Mass conversion of record set to weighted policy",
                    "Changes": [
                        {
                            "Action": "DELETE",
                            "ResourceRecordSet": {
                                "Name": record_set["Name"],
                                "Type": record_set["Type"],
                                "AliasTarget": {
                                    "HostedZoneId": record_set["AliasTarget"][
                                        "HostedZoneId"
                                    ],
                                    "DNSName": record_set["AliasTarget"]["DNSName"],
                                    "EvaluateTargetHealth": record_set["AliasTarget"][
                                        "EvaluateTargetHealth"
                                    ],
                                },
                            },
                        },
                        {
                            "Action": "UPSERT",
                            "ResourceRecordSet": {
                                "Name": record_set["Name"],
                                "Type": record_set["Type"],
                                "SetIdentifier": identifier,
                                "Weight": 100,
                                "AliasTarget": {
                                    "HostedZoneId": record_set["AliasTarget"][
                                        "HostedZoneId"
                                    ],
                                    "DNSName": record_set["AliasTarget"]["DNSName"],
                                    "EvaluateTargetHealth": record_set["AliasTarget"][
                                        "EvaluateTargetHealth"
                                    ],
                                },
                            },
                        },
                    ],
                },
            )
        else:
            # Treat record set as non alias
            client.change_resource_record_sets(
                HostedZoneId=hosted_zone_id,
                ChangeBatch={
                    "Comment": "Mass conversion of record set to weighted policy",
                    "Changes": [
                        {
                            "Action": "DELETE",
                            "ResourceRecordSet": {
                                "Name": record_set["Name"],
                                "Type": record_set["Type"],
                                "TTL": record_set["TTL"],
                                "ResourceRecords": record_set["ResourceRecords"],
                            },
                        },
                        {
                            "Action": "UPSERT",
                            "ResourceRecordSet": {
                                "Name": record_set["Name"],
                                "Type": record_set["Type"],
                                "SetIdentifier": identifier,
                                "Weight": 100,
                                "TTL": record_set["TTL"],
                                "ResourceRecords": record_set["ResourceRecords"],
                            },
                        },
                    ],
                },
            )
    except client.exceptions.InvalidChangeBatch as e:
        logging.warning(f"{e}")
        pass


def is_simple_record_set(record_set: dict) -> bool:
    """Check if given record set is using simple routing

    Args:
        record_set: Record set that will be checked

    Returns:
        bool: True if record set is using simple routing. False otherwise
    """
    result = True

    if "SetIdentifier" in record_set:
        result = False

    return result
