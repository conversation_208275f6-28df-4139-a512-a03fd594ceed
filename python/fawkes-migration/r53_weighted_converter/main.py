import sys
import logging

import ruamel.yaml
import click

import infra_lib
import infra_r53
from . import lib

@click.command()
@click.option(
    "--start-index",
    default=0,
    type=int,
    help="Starting index number from which entry will be processed",
)
@click.option(
    "--end-index",
    default=sys.maxsize,
    type=int,
    help="Final index number where entry will be stopped to be processed",
)
@click.pass_context
def main(
    ctx,
    start_index: int,
    end_index: int,
):
    cluster = ctx.parent.params['source_cluster']
    region = ctx.parent.params['region']
    aws_profile = ctx.parent.params['aws_profile']
    mode = ctx.parent.params['mode']
    dry_run = ctx.parent.params['dry_run']
    namespace = ctx.parent.params['namespace']

    logging.info(f"Running in dry-run={dry_run} mode={mode} namespace={namespace}")

    # Get all ingress route from the source cluster
    ingress_routes = infra_lib.get_all_ingress_routes(
        aws_profile=aws_profile,
        region=region,
        cluster_name=cluster,
    )

    total = len(ingress_routes)
    skip_not_cname = 0
    skip_not_simple = 0
    skip_not_resolvable = 0
    ok_count = 0
    total_record = 0
    set_identifier = "mass_conversion"

    # Filter based on mode
    ingress_routes = [ir for ir in ingress_routes if infra_lib.match_mode(ir[1], mode)]

    # Filter based on namespaces
    ingress_routes = [
        ir for ir in ingress_routes if infra_lib.match_namespace(namespace, ir[1])
    ]

    normalized_end_index = total if end_index == sys.maxsize else end_index
    eligible_ingress = ingress_routes[start_index:normalized_end_index]
    total_processed = len(eligible_ingress)
    eligible_records = []

    for ir in eligible_ingress:
        host = ir[3]
        ip_addresses = infra_lib.get_ip_list(host)

        # Filter out domain name that that matches the following criteria:
        # - is not resolvable to any of ip addresses
        # - is not a CNAME
        # - is not using a simple routing
        if len(ip_addresses) > 0:
            record_sets = infra_r53.get_record_set(host)
            total_record += len(record_sets)

            for item in record_sets:
                if item.record_set["Type"] != "CNAME":
                    logging.info(
                        f"Host ({host}, {item.record_set["Type"]}) will be skipped from conversion since it is not a CNAME"
                    )
                    skip_not_cname += 1
                    continue

                if not lib.is_simple_record_set(item.record_set):
                    logging.info(
                        f"Host ({host}, {item.record_set["SetIdentifier"]}) will be skipped from conversion since it does not use a simple routing"
                    )
                    skip_not_simple += 1
                    continue

                eligible_records.append(item)
        else:
            logging.info(
                f"Host {host} will be skipped from conversion since it is not resolvable to any ip addresses"
            )
            total_record += 1
            skip_not_resolvable += 1

    try:
        dump_records = {}

        for item in eligible_records:
            domain_name = item.record_set["Name"].rstrip(".")
            dump_records[domain_name] = {}
            dump_records[domain_name]["record_set"] = item.record_set
            dump_records[domain_name]["aws_profile"] = item.aws_profile
            dump_records[domain_name]["zone_id"] = item.zone_id
            before_conversion = set(infra_lib.get_ip_list(domain_name))
            logging.info(
                f"Converting ({domain_name}, {item.zone_id}, {item.aws_profile}) to weighted routing with identifier {set_identifier}"
            )

            if not dry_run:
                lib.convert_weighted_record(
                    record_set=item.record_set,
                    identifier=set_identifier,
                    hosted_zone_id=item.zone_id,
                    aws_profile=item.aws_profile,
                )

            after_conversion = set(infra_lib.get_ip_list(domain_name))
            if before_conversion != after_conversion:
                logging.error(
                    f"Conversion of {domain_name} produce different DNS lookup result. Stop conversion immediately"
                )
                break

            ok_count += 1
    finally:
        if not dry_run:
            with open(f"r53-{cluster}.yaml", "w") as f:
                ruamel.yaml.representer.RoundTripRepresenter.ignore_aliases = (
                    lambda x, y: True
                )

                yaml = ruamel.yaml.YAML()
                yaml.indent(mapping=2, sequence=4, offset=2)
                yaml.preserve_quotes = True
                yaml.width = 10240

                yaml.dump(dump_records, f)

    logging.info(
        f"Summary: total_ingress={total} processed_ingress={total_processed} start_index={start_index} end_index={normalized_end_index} total_records={total_record} ok={ok_count} not_cname={skip_not_cname} not_simple={skip_not_simple} not_resolvable={skip_not_resolvable}"
    )


if __name__ == "__main__":
    main()
