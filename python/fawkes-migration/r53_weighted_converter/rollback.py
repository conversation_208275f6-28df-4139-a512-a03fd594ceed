import click
import ruamel.yaml
import boto3


def convert_weighted_to_simple(
    current_record: dict, intended_record: dict, hosted_zone_id: str, client: any
):
    client.change_resource_record_sets(
        HostedZoneId=hosted_zone_id,
        ChangeBatch={
            "Comment": "Rollback non alias record set to simple",
            "Changes": [
                {
                    "Action": "DELETE",
                    "ResourceRecordSet": {
                        "Name": current_record["Name"],
                        "Type": current_record["Type"],
                        "SetIdentifier": current_record["SetIdentifier"],
                        "TTL": current_record["TTL"],
                        "Weight": current_record["Weight"],
                        "ResourceRecords": current_record["ResourceRecords"],
                    },
                },
                {
                    "Action": "UPSERT",
                    "ResourceRecordSet": {
                        "Name": intended_record["Name"],
                        "Type": intended_record["Type"],
                        "TTL": intended_record["TTL"],
                        "ResourceRecords": intended_record["ResourceRecords"],
                    },
                },
            ],
        },
    )


def convert_alias_weighted_to_simple(
    current_record: dict, intended_record: dict, hosted_zone_id: str, client: any
):
    client.change_resource_record_sets(
        HostedZoneId=hosted_zone_id,
        ChangeBatch={
            "Comment": "Rollback alias record set to simple",
            "Changes": [
                {
                    "Action": "DELETE",
                    "ResourceRecordSet": {
                        "Name": current_record["Name"],
                        "Type": current_record["Type"],
                        "SetIdentifier": current_record["SetIdentifier"],
                        "Weight": current_record["Weight"],
                        "AliasTarget": current_record["AliasTarget"],
                    },
                },
                {
                    "Action": "UPSERT",
                    "ResourceRecordSet": {
                        "Name": intended_record["Name"],
                        "Type": intended_record["Type"],
                        "AliasTarget": intended_record["AliasTarget"],
                    },
                },
            ],
        },
    )


def convert_simple_record(
    record_set: dict, original_record_set: dict, hosted_zone_id: str, aws_profile: str
):
    """
    Convert given record set to simple routing policy.
    Conversion logic handles both alias and non alias record set.

    Args:
      record_set: Record set that will be converted to weighted routing policy
      original_record_set: Original record set
      hosted_zone_id: ID of hosted zone where record set is located
      aws_profile: AWS profile name that will be used to convert record set
    """
    session = boto3.Session(profile_name=aws_profile)
    client = session.client("route53")

    # Return early when both record_set and original_record_set type are neither CNAME
    if record_set["Type"] != "CNAME" or original_record_set["Type"] != "CNAME":
        print("Both current and intended record set must be of type CNAME")
        return

    if "AliasTarget" in record_set:
        if "AliasTarget" in original_record_set:
            convert_alias_weighted_to_simple(
                current_record=record_set,
                intended_record=original_record_set,
                hosted_zone_id=hosted_zone_id,
                client=client,
            )
        else:
            print("Both current and intended record set must be of an Alias")
            return
    else:
        if "AliasTarget" not in original_record_set:
            convert_weighted_to_simple(
                current_record=record_set,
                intended_record=original_record_set,
                hosted_zone_id=hosted_zone_id,
                client=client,
            )


def get_record_set(name: str, hosted_zone_id: str, aws_profile: str) -> dict:
    aws_session = boto3.Session(profile_name=aws_profile)
    client = aws_session.client("route53")
    result = client.list_resource_record_sets(
        HostedZoneId=hosted_zone_id,
        StartRecordName=name,
        StartRecordType="CNAME",
        MaxItems="1",
    )

    if len(result["ResourceRecordSets"]) == 0:
        return {}
    else:
        return result["ResourceRecordSets"][0]


@click.command()
@click.option(
    "--source", help="Source file contains list of route53 record to rollback"
)
def main(source):
    with open(source, "r") as f:
        yaml = ruamel.yaml.YAML()
        yaml.indent(mapping=2, sequence=4, offset=2)
        yaml.preserve_quotes = True

        source_records = yaml.load(f)

    for key, value in source_records.items():
        record = get_record_set(
            name=key, hosted_zone_id=value["zone_id"], aws_profile=value["aws_profile"]
        )
        convert_simple_record(
            record_set=record,
            original_record_set=value["record_set"],
            hosted_zone_id=value["zone_id"],
            aws_profile=value["aws_profile"],
        )


if __name__ == "__main__":
    main()
