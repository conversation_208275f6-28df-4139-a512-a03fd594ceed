apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  annotations:
    notifications.argoproj.io/subscribe.on-created.slack: infra-ops
    notifications.argoproj.io/subscribe.on-deployed.slack: infra-ops
    notifications.argoproj.io/subscribe.on-sync-failed.slack: infra-ops
    notifications.argoproj.io/subscribe.on-sync-succeeded.slack: infra-ops
  generation: 149107
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: bootstrap
    catalog: xendit-argo-catalog
    deployer: argocd
    development: "true"
    env: staging
    environment: staging
    generation: fawkes
    live: "false"
    platform: xendit
    region: ap-southeast-1
    repository: xendit-argo-catalog
  name: ewallet-service-v2-development
  namespace: argocd
spec:
  destination:
    namespace: digipay-dev
    server: https://kubernetes.default.svc
  ignoreDifferences:
    - group: autoscaling
      jqPathExpressions:
        - .spec.metrics[].resource.name | select((. == "cpu") or (. == "memory"))
      kind: HorizontalPodAutoscaler
    - group: autoscaling
      jsonPointers:
        - /spec/maxReplicas
      kind: HorizontalPodAutoscaler
    - group: autoscaling
      jsonPointers:
        - /spec/minReplicas
      kind: HorizontalPodAutoscaler
    - group: argoproj.io
      jsonPointers:
        - /spec/replicas
      kind: Rollout
    - group: apps
      jsonPointers:
        - /spec/replicas
      kind: Deployment
  project: default
  source:
    path: charts/xendit-deployment_v2.11.1
    plugin:
      env:
        - name: AUTO_HELM
          value: "True"
        - name: AUTO_DEFAULT
          value: "False"
        - name: AUTO_LABEL
          value: "False"
        - name: AUTO_KUSTO
          value: "True"
      name: xendit
    repoURL: **************:xendit/xendit-argo-catalog.git
    targetRevision: argo-fawkes
  syncPolicy:
    automated: {}
    retry:
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
      limit: 5
    syncOptions:
      - Validate=true
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=false
      - ApplyOutOfSyncOnly=true
      - Replace=false
      - ServerSideApply=false
status:
  controllerNamespace: argocd
  health:
    lastTransitionTime: "2025-06-06T11:43:37Z"
    status: Healthy
  history:
    - deployStartedAt: "2025-05-13T06:59:11Z"
      deployedAt: "2025-05-13T06:59:13Z"
      id: 302
      initiatedBy:
        automated: true
      revision: 1900d3045ec465646e58a350716c78bad903461c
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
    - deployStartedAt: "2025-05-14T08:39:11Z"
      deployedAt: "2025-05-14T08:39:13Z"
      id: 303
      initiatedBy:
        automated: true
      revision: de50ad2544feb6197a9e42493498d182387027cc
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
    - deployStartedAt: "2025-05-14T09:30:17Z"
      deployedAt: "2025-05-14T09:30:20Z"
      id: 304
      initiatedBy:
        automated: true
      revision: 854658937cb2582323141e0a31fa877279343cc4
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
    - deployStartedAt: "2025-05-15T06:20:08Z"
      deployedAt: "2025-05-15T06:20:21Z"
      id: 305
      initiatedBy:
        automated: true
      revision: bbc3dae4a9f68b66a989111affd449da99c658ba
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
    - deployStartedAt: "2025-05-16T05:49:15Z"
      deployedAt: "2025-05-16T05:49:17Z"
      id: 306
      initiatedBy:
        automated: true
      revision: fbb0f4d46b5ee03ff1102b8972974d66b41713ac
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
    - deployStartedAt: "2025-05-20T10:31:27Z"
      deployedAt: "2025-05-20T10:31:29Z"
      id: 307
      initiatedBy:
        automated: true
      revision: 4736a94b1f335c139a6184802b9ebd4a886970eb
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
    - deployStartedAt: "2025-05-21T03:13:11Z"
      deployedAt: "2025-05-21T03:13:14Z"
      id: 308
      initiatedBy:
        automated: true
      revision: 159cbd1463bc46fadc4bf4a74bff38b79e6c3199
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
    - deployStartedAt: "2025-05-21T03:23:23Z"
      deployedAt: "2025-05-21T03:23:28Z"
      id: 309
      initiatedBy:
        automated: true
      revision: d29360f70ab8a9f53d6bab104017bca3699f2f93
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
    - deployStartedAt: "2025-05-21T10:18:27Z"
      deployedAt: "2025-05-21T10:18:29Z"
      id: 310
      initiatedBy:
        automated: true
      revision: d2f73276c0921fea52d318cdab5dd3045ffb76c3
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
    - deployStartedAt: "2025-05-28T07:46:38Z"
      deployedAt: "2025-05-28T07:46:53Z"
      id: 311
      initiatedBy:
        automated: true
      revision: e23c8b0176e3478a7c61f2b24dd55656b643bfb4
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
  operationState:
    finishedAt: "2025-05-28T07:46:53Z"
    message: successfully synced (all tasks run)
    operation:
      initiatedBy:
        automated: true
      retry:
        backoff:
          duration: 5s
          factor: 2
          maxDuration: 3m
        limit: 5
      sync:
        revision: e23c8b0176e3478a7c61f2b24dd55656b643bfb4
        syncOptions:
          - Validate=true
          - CreateNamespace=true
          - PrunePropagationPolicy=foreground
          - PruneLast=false
          - ApplyOutOfSyncOnly=true
          - Replace=false
          - ServerSideApply=false
    phase: Succeeded
    startedAt: "2025-05-28T07:46:38Z"
    syncResult:
      resources:
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-void-request-worker configured
          name: ewallet-service-v2-development-void-request-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-retry-worker-sqs-worker configured
          name: ewallet-service-v2-development-retry-worker-sqs-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-void-callback-sqs-worker configured
          name: ewallet-service-v2-development-void-callback-sqs-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-forex-settlement-sqs-worker configured
          name: ewallet-service-v2-development-forex-settlement-sqs-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-record-transaction-sqs-worker configured
          name: ewallet-service-v2-development-record-transaction-sqs-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-retry-register-sqs-worker configured
          name: ewallet-service-v2-development-retry-register-sqs-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-callback-received-sqs-worker configured
          name: ewallet-service-v2-development-callback-received-sqs-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-refund-callback-sqs-worker configured
          name: ewallet-service-v2-development-refund-callback-sqs-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-retry-callback-sqs-worker configured
          name: ewallet-service-v2-development-retry-callback-sqs-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-data-archival-worker configured
          name: ewallet-service-v2-development-data-archival-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-refund-request-worker configured
          name: ewallet-service-v2-development-refund-request-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-send-webhook-sqs-worker configured
          name: ewallet-service-v2-development-send-webhook-sqs-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-retry-distributor-worker configured
          name: ewallet-service-v2-development-retry-distributor-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-recon-post-capture-worker-worker configured
          name: ewallet-service-v2-development-recon-post-capture-worker-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: apps
          hookPhase: Running
          kind: Deployment
          message: deployment.apps/ewallet-service-v2-development-status-check-worker-worker configured
          name: ewallet-service-v2-development-status-check-worker-worker
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: batch
          hookPhase: Running
          kind: CronJob
          message: cronjob.batch/ewallet-service-v2-development-resend-webhooks-cron configured
          name: ewallet-service-v2-development-resend-webhooks-cron
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: batch
          hookPhase: Running
          kind: CronJob
          message: cronjob.batch/ewallet-service-v2-development-recon-captures-cron configured
          name: ewallet-service-v2-development-recon-captures-cron
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: batch
          hookPhase: Running
          kind: CronJob
          message: cronjob.batch/ewallet-service-v2-development-expire-charge-cron configured
          name: ewallet-service-v2-development-expire-charge-cron
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: batch
          hookPhase: Running
          kind: CronJob
          message: cronjob.batch/ewallet-service-v2-development-rtry-clr-cron configured
          name: ewallet-service-v2-development-rtry-clr-cron
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1
        - group: argoproj.io
          hookPhase: Running
          kind: Rollout
          message: rollout.argoproj.io/ewallet-service-v2-development-server configured
          name: ewallet-service-v2-development-server
          namespace: digipay-dev
          status: Synced
          syncPhase: Sync
          version: v1alpha1
      revision: e23c8b0176e3478a7c61f2b24dd55656b643bfb4
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
  reconciledAt: "2025-06-06T12:50:01Z"
  resources:
    - kind: ConfigMap
      name: ewallet-service-v2-development
      namespace: digipay-dev
      status: Synced
      version: v1
    - health:
        status: Healthy
      kind: Service
      name: ewallet-service-v2-development-server
      namespace: digipay-dev
      status: Synced
      version: v1
    - health:
        status: Healthy
      kind: Service
      name: ewallet-service-v2-development-server-canary
      namespace: digipay-dev
      status: Synced
      version: v1
    - health:
        status: Healthy
      kind: Service
      name: ewallet-service-v2-development-server-stable
      namespace: digipay-dev
      status: Synced
      version: v1
    - kind: ServiceAccount
      name: ewallet-service-v2-development
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-callback-received-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-data-archival-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-forex-settlement-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-recon-post-capture-worker-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-record-transaction-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-refund-callback-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-refund-request-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-retry-callback-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-retry-distributor-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-retry-register-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-retry-worker-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-send-webhook-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-status-check-worker-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-void-callback-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: apps
      health:
        status: Healthy
      kind: Deployment
      name: ewallet-service-v2-development-void-request-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: argoproj.io
      health:
        status: Healthy
      kind: Rollout
      name: ewallet-service-v2-development-server
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
    - group: autoscaling
      health:
        message: the HPA controller was able to get the target's current scale
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-callback-received-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: autoscaling
      health:
        message: the HPA controller was able to get the target's current scale
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-forex-settlement-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: autoscaling
      health:
        message: the HPA controller was able to get the target's current scale
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-recon-post-capture-worker-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: autoscaling
      health:
        message: the HPA controller was able to get the target's current scale
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-record-transaction-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: autoscaling
      health:
        message: the HPA controller was able to get the target's current scale
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-refund-callback-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: autoscaling
      health:
        message: the HPA controller was able to get the target's current scale
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-retry-callback-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: autoscaling
      health:
        message: the HPA controller was able to get the target's current scale
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-retry-register-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: autoscaling
      health:
        message: the HPA controller was able to get the target's current scale
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-retry-worker-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: autoscaling
      health:
        message: the HPA controller was able to get the target's current scale
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-send-webhook-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: autoscaling
      health:
        message: recommended size matches current size
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-status-check-worker-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: autoscaling
      health:
        message: the HPA controller was able to get the target's current scale
        status: Healthy
      kind: HorizontalPodAutoscaler
      name: ewallet-service-v2-development-void-callback-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v2
    - group: batch
      kind: CronJob
      name: ewallet-service-v2-development-expire-charge-cron
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: batch
      kind: CronJob
      name: ewallet-service-v2-development-recon-captures-cron
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: batch
      kind: CronJob
      name: ewallet-service-v2-development-resend-webhooks-cron
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: batch
      kind: CronJob
      name: ewallet-service-v2-development-rtry-clr-cron
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-callback-received-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-data-archival-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-forex-settlement-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-recon-post-capture-worker-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-record-transaction-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-refund-callback-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-refund-request-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-retry-callback-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-retry-distributor-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-retry-register-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-retry-worker-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-send-webhook-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-server
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-status-check-worker-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-void-callback-sqs-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: policy
      health:
        message: PodDisruptionBudget has SufficientPods
        status: Healthy
      kind: PodDisruptionBudget
      name: ewallet-service-v2-development-void-request-worker
      namespace: digipay-dev
      status: Synced
      version: v1
    - group: traefik.containo.us
      kind: IngressRoute
      name: ewallet-service-v2-development-server
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
    - group: traefik.containo.us
      kind: IngressRoute
      name: ewallet-service-v2-development-server-http
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
    - group: traefik.containo.us
      kind: IngressRoute
      name: ewallet-service-v2-development-server-redirection
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
    - group: traefik.containo.us
      kind: IngressRoute
      name: ewallet-service-v2-development-server-redirection-http
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
    - group: traefik.containo.us
      kind: Middleware
      name: ewallet-service-v2-development-server
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
    - group: traefik.containo.us
      kind: Middleware
      name: ewallet-service-v2-development-server-redirect
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
    - group: traefik.containo.us
      kind: Middleware
      name: ewallet-service-v2-development-server-redirection
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
    - group: traefik.containo.us
      kind: Middleware
      name: ewallet-service-v2-development-server-redirection-addprefix
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
    - group: traefik.containo.us
      kind: Middleware
      name: ewallet-service-v2-development-server-redirection-redirect
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
    - group: traefik.containo.us
      kind: Middleware
      name: ewallet-service-v2-development-server-redirection-replacepathregex
      namespace: digipay-dev
      status: Synced
      version: v1alpha1
  sourceHydrator: {}
  sourceType: Plugin
  summary:
    images:
      - 705506614808.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
  sync:
    comparedTo:
      destination:
        namespace: digipay-dev
        server: https://kubernetes.default.svc
      ignoreDifferences:
        - group: autoscaling
          jqPathExpressions:
            - .spec.metrics[].resource.name | select((. == "cpu") or (. == "memory"))
          kind: HorizontalPodAutoscaler
        - group: autoscaling
          jsonPointers:
            - /spec/maxReplicas
          kind: HorizontalPodAutoscaler
        - group: autoscaling
          jsonPointers:
            - /spec/minReplicas
          kind: HorizontalPodAutoscaler
        - group: argoproj.io
          jsonPointers:
            - /spec/replicas
          kind: Rollout
        - group: apps
          jsonPointers:
            - /spec/replicas
          kind: Deployment
      source:
        path: charts/xendit-deployment_v2.11.1
        plugin:
          env:
            - name: AUTO_HELM
              value: "True"
            - name: AUTO_DEFAULT
              value: "False"
            - name: AUTO_LABEL
              value: "False"
            - name: AUTO_KUSTO
              value: "True"
          name: xendit
        repoURL: **************:xendit/xendit-argo-catalog.git
        targetRevision: argo-fawkes
    revision: 9266519fe06bc11789d9bbb76bf08d0990e0a3d2
    status: Synced
