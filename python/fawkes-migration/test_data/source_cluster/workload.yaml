apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "165"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 204
  labels:
    app: ewallet-service-v2-development-callback-received-sqs-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    awsfis: "true"
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-callback-received-sqs-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-callback-received-sqs-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:11Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-callback-received-sqs-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-callback-received-sqs-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-callback-received-sqs-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-callback-received-sqs-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - callback-received-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-callback-received-sqs-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-callback-received-sqs-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-callback-received-sqs-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-callback-received-sqs-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 75Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-callback-received-sqs-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-callback-received-sqs-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 2
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:42Z"
      lastUpdateTime: "2025-05-28T07:47:23Z"
      message: ReplicaSet "ewallet-service-v2-development-callback-received-sqs-worker-76554dbd8f" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-04T22:34:54Z"
      lastUpdateTime: "2025-06-04T22:34:54Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 204
  readyReplicas: 2
  replicas: 2
  updatedReplicas: 2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "159"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 159
  labels:
    app: ewallet-service-v2-development-data-archival-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-data-archival-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-data-archival-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:16Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-data-archival-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-data-archival-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-data-archival-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-data-archival-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - data-archival-cron
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DATA_ARCHIVAL_CHARGE_REQUESTS_CRON_TAB
              value: 5 */1 * * *
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-data-archival-cron
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-data-archival-cron
            - name: SERVICE_NAME
              value: ewallet-service-v2-data-archival-cron
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          name: ewallet-service-v2-development-data-archival-worker
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 58Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-data-archival-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-data-archival-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: "2024-09-11T10:37:36Z"
      lastUpdateTime: "2025-05-28T07:46:52Z"
      message: ReplicaSet "ewallet-service-v2-development-data-archival-worker-79bc788df" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-05T02:03:50Z"
      lastUpdateTime: "2025-06-05T02:03:50Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 159
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "164"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 186
  labels:
    app: ewallet-service-v2-development-forex-settlement-sqs-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-forex-settlement-sqs-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-forex-settlement-sqs-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:20Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-forex-settlement-sqs-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-forex-settlement-sqs-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-forex-settlement-sqs-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-forex-settlement-sqs-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - forex-settlement-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-forex-settlement-sqs-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-forex-settlement-sqs-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-forex-settlement-sqs-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-forex-settlement-sqs-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 53Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-forex-settlement-sqs-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-forex-settlement-sqs-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 2
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:32Z"
      lastUpdateTime: "2025-05-28T07:47:31Z"
      message: ReplicaSet "ewallet-service-v2-development-forex-settlement-sqs-worker-647744bc5" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-04T12:05:38Z"
      lastUpdateTime: "2025-06-04T12:05:38Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 186
  readyReplicas: 2
  replicas: 2
  updatedReplicas: 2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "164"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 164
  labels:
    app: ewallet-service-v2-development-recon-post-capture-worker-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-recon-post-capture-worker-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-recon-post-capture-worker-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:23Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-recon-post-capture-worker-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-recon-post-capture-worker-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-recon-post-capture-worker-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-recon-post-capture-worker-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - recon-post-capture-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-recon-post-capture-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-recon-post-capture-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-recon-post-capture-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-recon-post-capture-worker-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 56Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-recon-post-capture-worker-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-recon-post-capture-worker-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:11Z"
      lastUpdateTime: "2025-05-28T07:47:23Z"
      message: ReplicaSet "ewallet-service-v2-development-recon-post-capture-worker-worker-6cdf5f9c98" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-04T16:01:59Z"
      lastUpdateTime: "2025-06-04T16:01:59Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 164
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "164"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 217
  labels:
    app: ewallet-service-v2-development-record-transaction-sqs-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    awsfis: "true"
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-record-transaction-sqs-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-record-transaction-sqs-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:26Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-record-transaction-sqs-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-record-transaction-sqs-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-record-transaction-sqs-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-record-transaction-sqs-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - record-transaction-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-record-transaction-sqs-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-record-transaction-sqs-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-record-transaction-sqs-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-record-transaction-sqs-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 72Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-record-transaction-sqs-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-record-transaction-sqs-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 2
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:22Z"
      lastUpdateTime: "2025-05-28T07:47:31Z"
      message: ReplicaSet "ewallet-service-v2-development-record-transaction-sqs-worker-66d9fc9f95" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-04T22:34:55Z"
      lastUpdateTime: "2025-06-04T22:34:55Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 217
  readyReplicas: 2
  replicas: 2
  updatedReplicas: 2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "164"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 186
  labels:
    app: ewallet-service-v2-development-refund-callback-sqs-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    awsfis: "true"
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-refund-callback-sqs-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-refund-callback-sqs-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:29Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-refund-callback-sqs-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-refund-callback-sqs-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-refund-callback-sqs-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-refund-callback-sqs-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - process-refund-callback-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-process-refund-callback-sqs-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-process-refund-callback-sqs-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-process-refund-callback-sqs-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-refund-callback-sqs-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 54Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-refund-callback-sqs-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-refund-callback-sqs-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 2
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:32Z"
      lastUpdateTime: "2025-05-28T07:47:23Z"
      message: ReplicaSet "ewallet-service-v2-development-refund-callback-sqs-worker-f686cd8bc" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-05T20:42:41Z"
      lastUpdateTime: "2025-06-05T20:42:41Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 186
  readyReplicas: 2
  replicas: 2
  updatedReplicas: 2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "163"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 163
  labels:
    app: ewallet-service-v2-development-refund-request-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    awsfis: "true"
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-refund-request-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-refund-request-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:32Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-refund-request-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-refund-request-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-refund-request-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-refund-request-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - process-refund-request-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-refund-request-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-refund-request-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-refund-request-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-refund-request-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 61Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-refund-request-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-refund-request-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:22Z"
      lastUpdateTime: "2025-05-28T07:47:23Z"
      message: ReplicaSet "ewallet-service-v2-development-refund-request-worker-765f98d47c" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-05T02:04:21Z"
      lastUpdateTime: "2025-06-05T02:04:21Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 163
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "163"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 167
  labels:
    app: ewallet-service-v2-development-retry-callback-sqs-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-retry-callback-sqs-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-retry-callback-sqs-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:35Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-retry-callback-sqs-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-retry-callback-sqs-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-retry-callback-sqs-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-retry-callback-sqs-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - retry-callback-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: CHARGE_RETRY_MAX_BACKOFF
              value: 24h
            - name: CHARGE_RETRY_MIN_BACKOFF
              value: 1m
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-retry-callback-sqs-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-retry-callback-sqs-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-retry-callback-sqs-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-retry-callback-sqs-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 70Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-retry-callback-sqs-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-retry-callback-sqs-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:11Z"
      lastUpdateTime: "2025-05-28T07:47:31Z"
      message: ReplicaSet "ewallet-service-v2-development-retry-callback-sqs-worker-d9b498dc6" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-04T16:01:56Z"
      lastUpdateTime: "2025-06-04T16:01:56Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 167
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "163"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 163
  labels:
    app: ewallet-service-v2-development-retry-distributor-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-retry-distributor-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-retry-distributor-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:39Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-retry-distributor-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-retry-distributor-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-retry-distributor-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-retry-distributor-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - retry-distributor
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-retry-distributor
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-retry-distributor
            - name: SERVICE_NAME
              value: ewallet-service-v2-retry-distributor
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          name: ewallet-service-v2-development-retry-distributor-worker
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 46Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-retry-distributor-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-retry-distributor-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:12Z"
      lastUpdateTime: "2025-05-28T07:46:53Z"
      message: ReplicaSet "ewallet-service-v2-development-retry-distributor-worker-7c7dbc97bb" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-05T02:03:43Z"
      lastUpdateTime: "2025-06-05T02:03:43Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 163
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "164"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 164
  labels:
    app: ewallet-service-v2-development-retry-register-sqs-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-retry-register-sqs-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-retry-register-sqs-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:47Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-retry-register-sqs-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-retry-register-sqs-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-retry-register-sqs-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-retry-register-sqs-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - retry-register
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: CHARGE_RETRY_MAX_BACKOFF
              value: 24h
            - name: CHARGE_RETRY_MIN_BACKOFF
              value: 1m
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-retry-sqs-register
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-retry-sqs-register
            - name: SERVICE_NAME
              value: ewallet-service-v2-retry-sqs-register
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-retry-register-sqs-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 66Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-retry-register-sqs-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-retry-register-sqs-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:31Z"
      lastUpdateTime: "2025-05-28T07:47:30Z"
      message: ReplicaSet "ewallet-service-v2-development-retry-register-sqs-worker-9d69bf699" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-05T02:04:22Z"
      lastUpdateTime: "2025-06-05T02:04:22Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 164
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "162"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 162
  labels:
    app: ewallet-service-v2-development-retry-worker-sqs-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-retry-worker-sqs-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-retry-worker-sqs-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:52Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-retry-worker-sqs-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-retry-worker-sqs-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-retry-worker-sqs-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-retry-worker-sqs-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - retry-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: CHARGE_RETRY_MAX_BACKOFF
              value: 24h
            - name: CHARGE_RETRY_MIN_BACKOFF
              value: 1m
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-retry-sqs-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-retry-sqs-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-retry-sqs-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-retry-worker-sqs-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 68Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-retry-worker-sqs-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-retry-worker-sqs-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:51Z"
      lastUpdateTime: "2025-05-28T07:47:30Z"
      message: ReplicaSet "ewallet-service-v2-development-retry-worker-sqs-worker-965f8dc77" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-05T02:04:23Z"
      lastUpdateTime: "2025-06-05T02:04:23Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 162
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "163"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 188
  labels:
    app: ewallet-service-v2-development-send-webhook-sqs-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    awsfis: "true"
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-send-webhook-sqs-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-send-webhook-sqs-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:40:55Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-send-webhook-sqs-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-send-webhook-sqs-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-send-webhook-sqs-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-send-webhook-sqs-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - send-webhook-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-send-webhook-sqs-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-send-webhook-sqs-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-send-webhook-sqs-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-send-webhook-sqs-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 71Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-send-webhook-sqs-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-send-webhook-sqs-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 2
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:21Z"
      lastUpdateTime: "2025-05-28T07:47:33Z"
      message: ReplicaSet "ewallet-service-v2-development-send-webhook-sqs-worker-65cb987586" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-05T04:14:16Z"
      lastUpdateTime: "2025-06-05T04:14:16Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 188
  readyReplicas: 2
  replicas: 2
  updatedReplicas: 2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "163"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 364
  labels:
    app: ewallet-service-v2-development-status-check-worker-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-status-check-worker-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-status-check-worker-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:41:00Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-status-check-worker-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-status-check-worker-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-status-check-worker-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-status-check-worker-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - status-check-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-status-check-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-status-check-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-status-check-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          name: ewallet-service-v2-development-status-check-worker-worker
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 51Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-status-check-worker-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-status-check-worker-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 2
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:13Z"
      lastUpdateTime: "2025-05-28T07:46:54Z"
      message: ReplicaSet "ewallet-service-v2-development-status-check-worker-worker-5fd49d4877" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-04T19:51:52Z"
      lastUpdateTime: "2025-06-04T19:51:52Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 364
  readyReplicas: 2
  replicas: 2
  updatedReplicas: 2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "161"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 183
  labels:
    app: ewallet-service-v2-development-void-callback-sqs-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-void-callback-sqs-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-void-callback-sqs-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:41:06Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-void-callback-sqs-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-void-callback-sqs-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-void-callback-sqs-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-void-callback-sqs-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - process-void-callback-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-process-void-callback-sqs-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-process-void-callback-sqs-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-process-void-callback-sqs-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-void-callback-sqs-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 57Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-void-callback-sqs-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-void-callback-sqs-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 2
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:31Z"
      lastUpdateTime: "2025-05-28T07:47:31Z"
      message: ReplicaSet "ewallet-service-v2-development-void-callback-sqs-worker-6cfb799c8c" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-04T22:34:55Z"
      lastUpdateTime: "2025-06-04T22:34:55Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 183
  readyReplicas: 2
  replicas: 2
  updatedReplicas: 2
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    deployment.kubernetes.io/revision: "162"
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 162
  labels:
    app: ewallet-service-v2-development-void-request-worker
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-void-request-worker
  namespace: digipay-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-void-request-worker
      environment: staging
      mode: development
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        helm.sh/chart: xendit-deployment-2.11.1
        kubectl.kubernetes.io/restartedAt: "2024-09-19T10:41:10Z"
        linkerd.io/inject: disabled
      labels:
        app: ewallet-service-v2-development-void-request-worker
        environment: staging
        mode: development
      name: ewallet-service-v2-development-void-request-worker
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - worker
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-void-request-worker
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-void-request-worker
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - process-void-request-worker
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2-void-request-worker
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2-void-request-worker
            - name: SERVICE_NAME
              value: ewallet-service-v2-void-request-worker
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-void-request-worker
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 54Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        fsGroup: 65534
      serviceAccount: ewallet-service-v2-development
      serviceAccountName: ewallet-service-v2-development
      terminationGracePeriodSeconds: 100
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-void-request-worker
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-void-request-worker
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  availableReplicas: 1
  conditions:
    - lastTransitionTime: "2024-11-23T16:20:32Z"
      lastUpdateTime: "2025-05-28T07:47:29Z"
      message: ReplicaSet "ewallet-service-v2-development-void-request-worker-7d9f5f4b96" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-06-04T12:05:37Z"
      lastUpdateTime: "2025-06-04T12:05:37Z"
      message: Deployment has minimum availability.
      reason: MinimumReplicasAvailable
      status: "True"
      type: Available
  observedGeneration: 162
  readyReplicas: 1
  replicas: 1
  updatedReplicas: 1
---
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
    linkerd.io/inject: disabled
    rollout.argoproj.io/revision: "155"
  generation: 172
  labels:
    app: ewallet-service-v2-development-server
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    awsfis: "true"
    environment: staging
    mode: development
  name: ewallet-service-v2-development-server
  namespace: digipay-dev
spec:
  replicas: 1
  restartAt: "2024-09-19T10:41:13Z"
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: ewallet-service-v2-development-server
      environment: staging
      mode: development
  strategy:
    canary:
      canaryService: ewallet-service-v2-development-server-canary
      maxSurge: 2
      maxUnavailable: 0
      stableService: ewallet-service-v2-development-server-stable
      steps:
        - setWeight: 0
        - pause:
            duration: 1m
        - setWeight: 50
        - pause:
            duration: 1m
        - setWeight: 70
        - pause:
            duration: 1m
        - setWeight: 100
        - pause:
            duration: 1m
      trafficRouting:
        smi:
          rootService: ewallet-service-v2-development-server
  template:
    metadata:
      annotations:
        ad.datadoghq.com/linkerd-proxy.check_names: '["linkerd"]'
        ad.datadoghq.com/linkerd-proxy.init_configs: '[{}]'
        ad.datadoghq.com/linkerd-proxy.instances: '[{"prometheus_url": "http://%%host%%:4191/metrics"}]'
        checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
        config.alpha.linkerd.io/proxy-wait-before-exit-seconds: "60"
        config.linkerd.io/proxy-memory-limit: 128Mi
        config.linkerd.io/skip-outbound-ports: 443,4222,5432,5671,5672,6379,8126,9096,9196,27017
        helm.sh/chart: xendit-deployment-2.11.1
        linkerd.io/identity-mode: default
        linkerd.io/inject: enabled
      labels:
        app: ewallet-service-v2-development-server
        environment: staging
        mode: development
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - preference:
                matchExpressions:
                  - key: workload
                    operator: In
                    values:
                      - server
              weight: 1
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: mode
                    operator: In
                    values:
                      - development
                  - key: iss.internal/schedule-priority
                    operator: In
                    values:
                      - cost-saving
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-server
                topologyKey: kubernetes.io/hostname
              weight: 50
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - ewallet-service-v2-development-server
                topologyKey: failure-domain.beta.kubernetes.io/zone
              weight: 50
      containers:
        - command:
            - chamber
            - exec
            - staging/development/service-discovery
            - staging/development/ewallet-service-v2
            - --
            - ./main
            - rest
          env:
            - name: CHAMBER_KMS_KEY_ALIAS
              value: parameter_store_key
            - name: CHAMBER_AWS_REGION
              value: us-west-2
            - name: AWS_REGION
              value: us-west-2
            - name: DD_AGENT_HOST
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: BUSINESS_SERVICE_TIMEOUT
              value: 3000m
            - name: DD_SERVICE_NAME
              value: ewallet-service-v2
            - name: LOGGER_SERVICE_NAME
              value: ewallet-service-v2
            - name: SERVICE_NAME
              value: ewallet-service-v2
          envFrom:
            - configMapRef:
                name: ewallet-service-v2-development
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
          imagePullPolicy: Always
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - sleep 20
          livenessProbe:
            httpGet:
              path: /healthcheck/liveness
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 60
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: ewallet-service-v2-development-server
          ports:
            - containerPort: 3000
              protocol: TCP
          readinessProbe:
            failureThreshold: 5
            httpGet:
              path: /healthcheck/readiness
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              memory: 512Mi
            requests:
              cpu: 30m
              memory: 50Mi
      securityContext:
        fsGroup: 65534
      serviceAccountName: ewallet-service-v2-development
      shareProcessNamespace: false
      terminationGracePeriodSeconds: 90
      tolerations:
        - effect: NoSchedule
          key: mode
          operator: Equal
          value: development
        - effect: NoSchedule
          key: capacity-type
          operator: Equal
          value: spot
        - effect: NoSchedule
          key: iss.internal/schedule-priority
          operator: Equal
          value: cost-saving
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-server
              environment: staging
              mode: development
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: DoNotSchedule
        - labelSelector:
            matchLabels:
              app: ewallet-service-v2-development-server
              environment: staging
              mode: development
          maxSkew: 3
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: DoNotSchedule
status:
  HPAReplicas: 1
  availableReplicas: 1
  blueGreen: {}
  canary:
    weights:
      canary:
        podTemplateHash: 65846dc87
        serviceName: ewallet-service-v2-development-server-canary
        weight: 0
      stable:
        podTemplateHash: 65846dc87
        serviceName: ewallet-service-v2-development-server-stable
        weight: 100
  conditions:
    - lastTransitionTime: "2025-05-28T07:51:05Z"
      lastUpdateTime: "2025-05-28T07:51:05Z"
      message: RolloutCompleted
      reason: RolloutCompleted
      status: "True"
      type: Completed
    - lastTransitionTime: "2025-05-28T07:51:05Z"
      lastUpdateTime: "2025-05-28T07:51:05Z"
      message: Rollout is paused
      reason: RolloutPaused
      status: "False"
      type: Paused
    - lastTransitionTime: "2025-05-31T03:56:16Z"
      lastUpdateTime: "2025-05-31T03:56:16Z"
      message: Rollout is healthy
      reason: RolloutHealthy
      status: "True"
      type: Healthy
    - lastTransitionTime: "2025-05-28T07:51:05Z"
      lastUpdateTime: "2025-05-31T03:56:16Z"
      message: ReplicaSet "ewallet-service-v2-development-server-65846dc87" has successfully progressed.
      reason: NewReplicaSetAvailable
      status: "True"
      type: Progressing
    - lastTransitionTime: "2025-05-31T03:56:16Z"
      lastUpdateTime: "2025-05-31T03:56:16Z"
      message: Rollout has minimum availability
      reason: AvailableReason
      status: "True"
      type: Available
  currentPodHash: 65846dc87
  currentStepHash: 55d89f6846
  currentStepIndex: 8
  observedGeneration: "172"
  phase: Healthy
  readyReplicas: 1
  replicas: 1
  restartedAt: "2024-09-19T10:41:13Z"
  selector: app=ewallet-service-v2-development-server,environment=staging,mode=development
  stableRS: 65846dc87
  updatedReplicas: 1
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 154
  labels:
    app: ewallet-service-v2-development-expire-charge-cron
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-expire-charge-cron
  namespace: digipay-dev
spec:
  concurrencyPolicy: Forbid
  failedJobsHistoryLimit: 1
  jobTemplate:
    metadata:
    spec:
      activeDeadlineSeconds: 60
      backoffLimit: 6
      template:
        metadata:
          annotations:
            checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
            helm.sh/chart: xendit-deployment-2.11.1
            linkerd.io/inject: disabled
          labels:
            app: ewallet-service-v2-development-expire-charge-cron
            environment: staging
            mode: development
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
                - preference:
                    matchExpressions:
                      - key: workload
                        operator: In
                        values:
                          - worker
                  weight: 1
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: mode
                        operator: In
                        values:
                          - development
                      - key: iss.internal/schedule-priority
                        operator: In
                        values:
                          - cost-saving
          containers:
            - command:
                - chamber
                - exec
                - staging/development/service-discovery
                - staging/development/ewallet-service-v2
                - --
                - ./main
                - expire-charge
              env:
                - name: CHAMBER_KMS_KEY_ALIAS
                  value: parameter_store_key
                - name: CHAMBER_AWS_REGION
                  value: us-west-2
                - name: AWS_REGION
                  value: us-west-2
                - name: DD_AGENT_HOST
                  valueFrom:
                    fieldRef:
                      apiVersion: v1
                      fieldPath: status.hostIP
                - name: DD_SERVICE_NAME
                  value: ewallet-service-v2-expire-charge-cron
                - name: LOGGER_SERVICE_NAME
                  value: ewallet-service-v2-expire-charge-cron
                - name: SERVICE_NAME
                  value: ewallet-service-v2-expire-charge-cron
              envFrom:
                - configMapRef:
                    name: ewallet-service-v2-development
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
              imagePullPolicy: Always
              name: ewallet-service-v2-development-expire-charge-cron
              resources:
                limits:
                  memory: 300Mi
                requests:
                  cpu: 100m
                  memory: 100Mi
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
          dnsPolicy: ClusterFirst
          restartPolicy: OnFailure
          schedulerName: default-scheduler
          securityContext:
            fsGroup: 65534
          serviceAccount: ewallet-service-v2-development
          serviceAccountName: ewallet-service-v2-development
          terminationGracePeriodSeconds: 90
          tolerations:
            - effect: NoSchedule
              key: mode
              operator: Equal
              value: development
            - effect: NoSchedule
              key: capacity-type
              operator: Equal
              value: spot
            - effect: NoSchedule
              key: iss.internal/schedule-priority
              operator: Equal
              value: cost-saving
  schedule: '*/1 * * * *'
  startingDeadlineSeconds: 300
  successfulJobsHistoryLimit: 1
  suspend: false
status:
  lastScheduleTime: "2025-06-06T09:03:00Z"
  lastSuccessfulTime: "2025-06-06T09:03:15Z"
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 155
  labels:
    app: ewallet-service-v2-development-recon-captures-cron
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-recon-captures-cron
  namespace: digipay-dev
spec:
  concurrencyPolicy: Forbid
  failedJobsHistoryLimit: 1
  jobTemplate:
    metadata:
    spec:
      activeDeadlineSeconds: 300
      backoffLimit: 6
      template:
        metadata:
          annotations:
            checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
            helm.sh/chart: xendit-deployment-2.11.1
            linkerd.io/inject: disabled
          labels:
            app: ewallet-service-v2-development-recon-captures-cron
            environment: staging
            mode: development
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
                - preference:
                    matchExpressions:
                      - key: workload
                        operator: In
                        values:
                          - worker
                  weight: 1
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: mode
                        operator: In
                        values:
                          - development
                      - key: iss.internal/schedule-priority
                        operator: In
                        values:
                          - cost-saving
          containers:
            - command:
                - chamber
                - exec
                - staging/development/service-discovery
                - staging/development/ewallet-service-v2
                - --
                - ./main
                - reconcile-captures-job
              env:
                - name: CHAMBER_KMS_KEY_ALIAS
                  value: parameter_store_key
                - name: CHAMBER_AWS_REGION
                  value: us-west-2
                - name: AWS_REGION
                  value: us-west-2
                - name: DD_AGENT_HOST
                  valueFrom:
                    fieldRef:
                      apiVersion: v1
                      fieldPath: status.hostIP
                - name: DD_SERVICE_NAME
                  value: ewallet-service-v2-recon-captures-cron
                - name: LOGGER_SERVICE_NAME
                  value: ewallet-service-v2-recon-captures-cron
                - name: SERVICE_NAME
                  value: ewallet-service-v2-recon-captures-cron
              envFrom:
                - configMapRef:
                    name: ewallet-service-v2-development
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
              imagePullPolicy: Always
              name: ewallet-service-v2-development-recon-captures-cron
              resources:
                limits:
                  memory: 300Mi
                requests:
                  cpu: 100m
                  memory: 100Mi
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
          dnsPolicy: ClusterFirst
          restartPolicy: OnFailure
          schedulerName: default-scheduler
          securityContext:
            fsGroup: 65534
          serviceAccount: ewallet-service-v2-development
          serviceAccountName: ewallet-service-v2-development
          terminationGracePeriodSeconds: 90
          tolerations:
            - effect: NoSchedule
              key: mode
              operator: Equal
              value: development
            - effect: NoSchedule
              key: capacity-type
              operator: Equal
              value: spot
            - effect: NoSchedule
              key: iss.internal/schedule-priority
              operator: Equal
              value: cost-saving
  schedule: '*/10 * * * *'
  startingDeadlineSeconds: 300
  successfulJobsHistoryLimit: 1
  suspend: true
status:
  lastScheduleTime: "2024-10-31T04:50:00Z"
  lastSuccessfulTime: "2024-10-31T04:50:23Z"
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 155
  labels:
    app: ewallet-service-v2-development-resend-webhooks-cron
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-resend-webhooks-cron
  namespace: digipay-dev
spec:
  concurrencyPolicy: Forbid
  failedJobsHistoryLimit: 1
  jobTemplate:
    metadata:
    spec:
      activeDeadlineSeconds: 60
      backoffLimit: 6
      template:
        metadata:
          annotations:
            checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
            helm.sh/chart: xendit-deployment-2.11.1
            linkerd.io/inject: disabled
          labels:
            app: ewallet-service-v2-development-resend-webhooks-cron
            environment: staging
            mode: development
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
                - preference:
                    matchExpressions:
                      - key: workload
                        operator: In
                        values:
                          - worker
                  weight: 1
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: mode
                        operator: In
                        values:
                          - development
                      - key: iss.internal/schedule-priority
                        operator: In
                        values:
                          - cost-saving
          containers:
            - command:
                - chamber
                - exec
                - staging/development/service-discovery
                - staging/development/ewallet-service-v2
                - --
                - ./main
                - resend-webhooks
              env:
                - name: CHAMBER_KMS_KEY_ALIAS
                  value: parameter_store_key
                - name: CHAMBER_AWS_REGION
                  value: us-west-2
                - name: AWS_REGION
                  value: us-west-2
                - name: DD_AGENT_HOST
                  valueFrom:
                    fieldRef:
                      apiVersion: v1
                      fieldPath: status.hostIP
                - name: DD_SERVICE_NAME
                  value: ewallet-service-v2-resend-webhooks-cron
                - name: LOGGER_SERVICE_NAME
                  value: ewallet-service-v2-resend-webhooks-cron
                - name: SERVICE_NAME
                  value: ewallet-service-v2-resend-webhooks-cron
              envFrom:
                - configMapRef:
                    name: ewallet-service-v2-development
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
              imagePullPolicy: Always
              name: ewallet-service-v2-development-resend-webhooks-cron
              resources:
                limits:
                  memory: 300Mi
                requests:
                  cpu: 100m
                  memory: 100Mi
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
          dnsPolicy: ClusterFirst
          restartPolicy: OnFailure
          schedulerName: default-scheduler
          securityContext:
            fsGroup: 65534
          serviceAccount: ewallet-service-v2-development
          serviceAccountName: ewallet-service-v2-development
          terminationGracePeriodSeconds: 90
          tolerations:
            - effect: NoSchedule
              key: mode
              operator: Equal
              value: development
            - effect: NoSchedule
              key: capacity-type
              operator: Equal
              value: spot
            - effect: NoSchedule
              key: iss.internal/schedule-priority
              operator: Equal
              value: cost-saving
  schedule: '*/1 * * * *'
  startingDeadlineSeconds: 300
  successfulJobsHistoryLimit: 1
  suspend: true
status:
  lastScheduleTime: "2024-10-31T04:14:00Z"
  lastSuccessfulTime: "2024-10-31T04:14:14Z"
---
apiVersion: batch/v1
kind: CronJob
metadata:
  annotations:
    checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
    helm.sh/chart: xendit-deployment-2.11.1
    iss.internal/cluster-name: xnd-sg-stg-aws-0
    iss.internal/pagerduty-team-name: ConfigureMe
    iss.internal/product-name: ConfigureMe
    iss.internal/product-owner: ConfigureMe
    iss.internal/service-language: nodejs
    iss.internal/service-name: ewallet-service-v2
    iss.internal/service-name-display: ConfigureMe
    iss.internal/service-type: web
    iss.internal/slack-channel-name: orphaned-service-alerts
    kubernetes.io/change-cause: ""
  generation: 154
  labels:
    app: ewallet-service-v2-development-rtry-clr-cron
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development-rtry-clr-cron
  namespace: digipay-dev
spec:
  concurrencyPolicy: Forbid
  failedJobsHistoryLimit: 1
  jobTemplate:
    metadata:
    spec:
      activeDeadlineSeconds: 180
      backoffLimit: 6
      template:
        metadata:
          annotations:
            checksum/shared-env: c4a96bd336d76dfc4238183d81de9595805f978eee3a7869fbe8871138d28c37
            helm.sh/chart: xendit-deployment-2.11.1
            linkerd.io/inject: disabled
          labels:
            app: ewallet-service-v2-development-rtry-clr-cron
            environment: staging
            mode: development
        spec:
          affinity:
            nodeAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
                - preference:
                    matchExpressions:
                      - key: workload
                        operator: In
                        values:
                          - worker
                  weight: 1
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: mode
                        operator: In
                        values:
                          - development
                      - key: iss.internal/schedule-priority
                        operator: In
                        values:
                          - cost-saving
          containers:
            - command:
                - chamber
                - exec
                - staging/development/service-discovery
                - staging/development/ewallet-service-v2
                - --
                - ./main
                - retry-cleaner
              env:
                - name: CHAMBER_KMS_KEY_ALIAS
                  value: parameter_store_key
                - name: CHAMBER_AWS_REGION
                  value: us-west-2
                - name: AWS_REGION
                  value: us-west-2
                - name: DD_AGENT_HOST
                  valueFrom:
                    fieldRef:
                      apiVersion: v1
                      fieldPath: status.hostIP
                - name: DD_SERVICE_NAME
                  value: ewallet-service-v2-retry-cleaner-cron
                - name: LOGGER_SERVICE_NAME
                  value: ewallet-service-v2-retry-cleaner-cron
                - name: SERVICE_NAME
                  value: ewallet-service-v2-retry-cleaner-cron
              envFrom:
                - configMapRef:
                    name: ewallet-service-v2-development
              image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/xendit/ewallet-service-v2:v1.135.0-rc2
              imagePullPolicy: Always
              name: ewallet-service-v2-development-rtry-clr-cron
              resources:
                limits:
                  memory: 300Mi
                requests:
                  cpu: 100m
                  memory: 100Mi
              terminationMessagePath: /dev/termination-log
              terminationMessagePolicy: File
          dnsPolicy: ClusterFirst
          restartPolicy: OnFailure
          schedulerName: default-scheduler
          securityContext:
            fsGroup: 65534
          serviceAccount: ewallet-service-v2-development
          serviceAccountName: ewallet-service-v2-development
          terminationGracePeriodSeconds: 90
          tolerations:
            - effect: NoSchedule
              key: mode
              operator: Equal
              value: development
            - effect: NoSchedule
              key: capacity-type
              operator: Equal
              value: spot
            - effect: NoSchedule
              key: iss.internal/schedule-priority
              operator: Equal
              value: cost-saving
  schedule: '*/15 * * * *'
  startingDeadlineSeconds: 300
  successfulJobsHistoryLimit: 1
  suspend: false
status:
  lastScheduleTime: "2025-06-06T09:00:00Z"
  lastSuccessfulTime: "2025-06-06T09:00:15Z"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/ewallet-service-v2-development-staging
    helm.sh/chart: xendit-deployment-2.11.1
  labels:
    app: ewallet-service-v2-development
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
    xendit.co/chart: xendit-deployment
    xendit.co/chart-version: 2.11.1
    xendit.co/service: ewallet-service-v2
  name: ewallet-service-v2-development
  namespace: digipay-dev
---
apiVersion: v1
kind: Service
metadata:
  annotations: {}
  labels:
    app: ewallet-service-v2-development-server
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
  name: ewallet-service-v2-development-server
  namespace: digipay-dev
spec:
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - name: http-app
      port: 80
      protocol: TCP
      targetPort: 3000
  selector:
    app: ewallet-service-v2-development-server
    environment: staging
    mode: development
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    argo-rollouts.argoproj.io/managed-by-rollouts: ewallet-service-v2-development-server
  labels:
    app: ewallet-service-v2-development-server
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
  name: ewallet-service-v2-development-server-canary
  namespace: digipay-dev
spec:
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - name: http-app
      port: 80
      protocol: TCP
      targetPort: 3000
  selector:
    app: ewallet-service-v2-development-server
    environment: staging
    mode: development
    rollouts-pod-template-hash: 65846dc87
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    argo-rollouts.argoproj.io/managed-by-rollouts: ewallet-service-v2-development-server
  labels:
    app: ewallet-service-v2-development-server
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
    environment: staging
    mode: development
  name: ewallet-service-v2-development-server-stable
  namespace: digipay-dev
spec:
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - name: http-app
      port: 80
      protocol: TCP
      targetPort: 3000
  selector:
    app: ewallet-service-v2-development-server
    environment: staging
    mode: development
    rollouts-pod-template-hash: 65846dc87
  sessionAffinity: None
  type: ClusterIP
status:
  loadBalancer: {}
---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  annotations:
    kubernetes.io/ingress.class: traefik
  generation: 1
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-server
  namespace: digipay-dev
spec:
  entryPoints:
    - websecure-pv
  routes:
    - kind: Rule
      match: Host(`ewallet-service-v2-dev.ap-southeast-1.priv.stg.tidnex.dev`)
      middlewares:
        - name: ewallet-service-v2-development-server
          namespace: digipay-dev
      services:
        - kind: Service
          name: ewallet-service-v2-development-server
          namespace: digipay-dev
          port: 80
  tls: {}
---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  annotations:
    kubernetes.io/ingress.class: traefik
  generation: 1
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-server-http
  namespace: digipay-dev
spec:
  entryPoints:
    - web-pv
  routes:
    - kind: Rule
      match: Host(`ewallet-service-v2-dev.ap-southeast-1.priv.stg.tidnex.dev`)
      middlewares:
        - name: ewallet-service-v2-development-server-redirect
          namespace: digipay-dev
      services:
        - kind: Service
          name: ewallet-service-v2-development-server
          namespace: digipay-dev
          port: 80
---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  annotations:
    kubernetes.io/ingress.class: traefik
  generation: 1
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-server-redirection
  namespace: digipay-dev
spec:
  entryPoints:
    - websecure
  routes:
    - kind: Rule
      match: Host(`ewallet-service-dev.ap-southeast-1.stg.tidnex.dev`)
      middlewares:
        - name: ewallet-service-v2-development-server-redirection
          namespace: digipay-dev
        - name: ewallet-service-v2-development-server-redirection-addprefix
          namespace: digipay-dev
        - name: ewallet-service-v2-development-server-redirection-replacepathregex
          namespace: digipay-dev
      services:
        - kind: Service
          name: ewallet-service-v2-development-server
          namespace: digipay-dev
          port: 80
  tls: {}
---
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  annotations:
    kubernetes.io/ingress.class: traefik
  generation: 1
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-server-redirection-http
  namespace: digipay-dev
spec:
  entryPoints:
    - web
  routes:
    - kind: Rule
      match: Host(`ewallet-service-dev.ap-southeast-1.stg.tidnex.dev`)
      middlewares:
        - name: ewallet-service-v2-development-server-redirection-redirect
          namespace: digipay-dev
      services:
        - kind: Service
          name: ewallet-service-v2-development-server
          namespace: digipay-dev
          port: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-callback-received-sqs-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 4
  metrics:
    - external:
        metric:
          name: aws.sqs.approximate_number_of_messages_visible
          selector:
            matchLabels:
              queuename: digipay_ewallet-service-v2_stg-dev_callback-received-queue
        target:
          type: Value
          value: "10"
      type: External
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-callback-received-sqs-worker
status:
  conditions:
    - lastTransitionTime: "2025-03-20T04:19:04Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-05-26T06:12:32Z"
      message: 'the HPA was unable to compute the replica count: unable to get external metric digipay-dev/aws.sqs.approximate_number_of_messages_visible/&LabelSelector{MatchLabels:map[string]string{queuename: digipay_ewallet-service-v2_stg-dev_callback-received-queue,},MatchExpressions:[]LabelSelectorRequirement{},}: unable to fetch metrics from external metrics API: Internal error occurred: DatadogMetric is invalid, err: Global error (all queries) from backend, invalid syntax in query? Check Cluster Agent leader logs for details'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-05-21T19:48:40Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
  currentMetrics:
    - type: ""
  currentReplicas: 2
  desiredReplicas: 2
  lastScaleTime: "2025-04-11T04:23:24Z"
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-forex-settlement-sqs-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 4
  metrics:
    - external:
        metric:
          name: aws.sqs.approximate_number_of_messages_visible
          selector:
            matchLabels:
              queuename: digipay_ewallet-service-v2_stg-dev_forex-settlement-queue
        target:
          type: Value
          value: "10"
      type: External
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-forex-settlement-sqs-worker
status:
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:28Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-05-26T06:12:32Z"
      message: 'the HPA was unable to compute the replica count: unable to get external metric digipay-dev/aws.sqs.approximate_number_of_messages_visible/&LabelSelector{MatchLabels:map[string]string{queuename: digipay_ewallet-service-v2_stg-dev_forex-settlement-queue,},MatchExpressions:[]LabelSelectorRequirement{},}: unable to fetch metrics from external metrics API: Internal error occurred: DatadogMetric is invalid, err: Global error (all queries) from backend, invalid syntax in query? Check Cluster Agent leader logs for details'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-05-21T19:48:39Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
  currentMetrics:
    - type: ""
  currentReplicas: 2
  desiredReplicas: 2
  lastScaleTime: "2025-04-11T04:23:24Z"
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-recon-post-capture-worker-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 2
  metrics:
    - external:
        metric:
          name: aws.sqs.approximate_number_of_messages_visible
          selector:
            matchLabels:
              queuename: digipay_ewallet-service-v2-recon_stg-dev_post-capture-queue
        target:
          type: Value
          value: "10"
      type: External
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-recon-post-capture-worker-worker
status:
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:28Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-05-26T06:12:32Z"
      message: 'the HPA was unable to compute the replica count: unable to get external metric digipay-dev/aws.sqs.approximate_number_of_messages_visible/&LabelSelector{MatchLabels:map[string]string{queuename: digipay_ewallet-service-v2-recon_stg-dev_post-capture-queue,},MatchExpressions:[]LabelSelectorRequirement{},}: unable to fetch metrics from external metrics API: Internal error occurred: DatadogMetric is invalid, err: Global error (all queries) from backend, invalid syntax in query? Check Cluster Agent leader logs for details'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-05-21T19:48:40Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
  currentMetrics:
    - type: ""
  currentReplicas: 1
  desiredReplicas: 1
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-record-transaction-sqs-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 4
  metrics:
    - external:
        metric:
          name: aws.sqs.approximate_number_of_messages_visible
          selector:
            matchLabels:
              queuename: digipay_ewallet-service-v2_stg-dev_record-transaction-queue
        target:
          type: Value
          value: "10"
      type: External
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-record-transaction-sqs-worker
status:
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:28Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-05-26T06:12:32Z"
      message: 'the HPA was unable to compute the replica count: unable to get external metric digipay-dev/aws.sqs.approximate_number_of_messages_visible/&LabelSelector{MatchLabels:map[string]string{queuename: digipay_ewallet-service-v2_stg-dev_record-transaction-queue,},MatchExpressions:[]LabelSelectorRequirement{},}: unable to fetch metrics from external metrics API: Internal error occurred: DatadogMetric is invalid, err: Global error (all queries) from backend, invalid syntax in query? Check Cluster Agent leader logs for details'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-05-21T19:48:47Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
  currentMetrics:
    - type: ""
  currentReplicas: 2
  desiredReplicas: 2
  lastScaleTime: "2025-04-11T04:23:24Z"
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-refund-callback-sqs-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 4
  metrics:
    - external:
        metric:
          name: aws.sqs.approximate_number_of_messages_visible
          selector:
            matchLabels:
              queuename: digipay_ewallet-service-v2_stg-dev_refund-callback-queue
        target:
          type: Value
          value: "10"
      type: External
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-refund-callback-sqs-worker
status:
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:28Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-05-26T06:12:32Z"
      message: 'the HPA was unable to compute the replica count: unable to get external metric digipay-dev/aws.sqs.approximate_number_of_messages_visible/&LabelSelector{MatchLabels:map[string]string{queuename: digipay_ewallet-service-v2_stg-dev_refund-callback-queue,},MatchExpressions:[]LabelSelectorRequirement{},}: unable to fetch metrics from external metrics API: Internal error occurred: DatadogMetric is invalid, err: Global error (all queries) from backend, invalid syntax in query? Check Cluster Agent leader logs for details'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-05-21T19:48:39Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
  currentMetrics:
    - type: ""
  currentReplicas: 2
  desiredReplicas: 2
  lastScaleTime: "2025-04-11T04:23:24Z"
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-retry-callback-sqs-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 2
  metrics:
    - external:
        metric:
          name: aws.sqs.approximate_number_of_messages_visible
          selector:
            matchLabels:
              queuename: digipay_ewallet-service-v2_stg-dev_retry-callback-queue
        target:
          type: Value
          value: "10"
      type: External
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-retry-callback-sqs-worker
status:
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:28Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-05-26T06:12:32Z"
      message: 'the HPA was unable to compute the replica count: unable to get external metric digipay-dev/aws.sqs.approximate_number_of_messages_visible/&LabelSelector{MatchLabels:map[string]string{queuename: digipay_ewallet-service-v2_stg-dev_retry-callback-queue,},MatchExpressions:[]LabelSelectorRequirement{},}: unable to fetch metrics from external metrics API: Internal error occurred: DatadogMetric is invalid, err: Global error (all queries) from backend, invalid syntax in query? Check Cluster Agent leader logs for details'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-05-21T19:48:49Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
  currentMetrics:
    - type: ""
  currentReplicas: 1
  desiredReplicas: 1
  lastScaleTime: "2024-10-23T06:48:37Z"
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-retry-register-sqs-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 2
  metrics:
    - external:
        metric:
          name: aws.sqs.approximate_number_of_messages_visible
          selector:
            matchLabels:
              queuename: digipay_ewallet-service-v2_stg-dev_retry-queue
        target:
          type: Value
          value: "10"
      type: External
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-retry-register-sqs-worker
status:
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:28Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-05-26T06:12:32Z"
      message: 'the HPA was unable to compute the replica count: unable to get external metric digipay-dev/aws.sqs.approximate_number_of_messages_visible/&LabelSelector{MatchLabels:map[string]string{queuename: digipay_ewallet-service-v2_stg-dev_retry-queue,},MatchExpressions:[]LabelSelectorRequirement{},}: unable to fetch metrics from external metrics API: Internal error occurred: DatadogMetric is invalid, err: Global error (all queries) from backend, invalid syntax in query? Check Cluster Agent leader logs for details'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-05-21T19:48:46Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
  currentMetrics:
    - type: ""
  currentReplicas: 1
  desiredReplicas: 1
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-retry-worker-sqs-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 2
  metrics:
    - external:
        metric:
          name: aws.sqs.approximate_number_of_messages_visible
          selector:
            matchLabels:
              queuename: digipay_ewallet-service-v2_stg-dev_retry-jobs-queue
        target:
          type: Value
          value: "10"
      type: External
  minReplicas: 1
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-retry-worker-sqs-worker
status:
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:28Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-05-26T06:12:32Z"
      message: 'the HPA was unable to compute the replica count: unable to get external metric digipay-dev/aws.sqs.approximate_number_of_messages_visible/&LabelSelector{MatchLabels:map[string]string{queuename: digipay_ewallet-service-v2_stg-dev_retry-jobs-queue,},MatchExpressions:[]LabelSelectorRequirement{},}: unable to fetch metrics from external metrics API: Internal error occurred: DatadogMetric is invalid, err: Global error (all queries) from backend, invalid syntax in query? Check Cluster Agent leader logs for details'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-05-21T19:48:37Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
  currentMetrics:
    - type: ""
  currentReplicas: 1
  desiredReplicas: 1
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-send-webhook-sqs-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 4
  metrics:
    - external:
        metric:
          name: aws.sqs.approximate_number_of_messages_visible
          selector:
            matchLabels:
              queuename: digipay_ewallet-service-v2_stg-dev_send-webhook-queue
        target:
          type: Value
          value: "10"
      type: External
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-send-webhook-sqs-worker
status:
  conditions:
    - lastTransitionTime: "2025-04-07T13:36:10Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-05-26T06:12:32Z"
      message: 'the HPA was unable to compute the replica count: unable to get external metric digipay-dev/aws.sqs.approximate_number_of_messages_visible/&LabelSelector{MatchLabels:map[string]string{queuename: digipay_ewallet-service-v2_stg-dev_send-webhook-queue,},MatchExpressions:[]LabelSelectorRequirement{},}: unable to fetch metrics from external metrics API: Internal error occurred: DatadogMetric is invalid, err: Global error (all queries) from backend, invalid syntax in query? Check Cluster Agent leader logs for details'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-05-21T19:48:37Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
  currentMetrics:
    - type: ""
  currentReplicas: 2
  desiredReplicas: 2
  lastScaleTime: "2025-04-11T04:23:24Z"
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-status-check-worker-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 4
  metrics:
    - resource:
        name: memory
        target:
          averageUtilization: 90
          type: Utilization
      type: Resource
    - resource:
        name: cpu
        target:
          averageUtilization: 80
          type: Utilization
      type: Resource
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-status-check-worker-worker
status:
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:28Z"
      message: recommended size matches current size
      reason: ReadyForNewScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-06-06T05:52:49Z"
      message: the HPA was able to successfully calculate a replica count from memory resource utilization (percentage of request)
      reason: ValidMetricFound
      status: "True"
      type: ScalingActive
    - lastTransitionTime: "2025-04-20T16:06:37Z"
      message: the desired count is within the acceptable range
      reason: DesiredWithinRange
      status: "False"
      type: ScalingLimited
  currentMetrics:
    - resource:
        current:
          averageUtilization: 58
          averageValue: "31227904"
        name: memory
      type: Resource
    - resource:
        current:
          averageUtilization: 33
          averageValue: 10m
        name: cpu
      type: Resource
  currentReplicas: 2
  desiredReplicas: 2
  lastScaleTime: "2025-05-02T13:24:39Z"
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  annotations: {}
  labels:
    argocd-xnd-sg-stg-aws-0.stg.tidnex.dev: ewallet-service-v2-development
  name: ewallet-service-v2-development-void-callback-sqs-worker
  namespace: digipay-dev
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 180
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 360
    scaleUp:
      policies:
        - periodSeconds: 60
          type: Pods
          value: 1
      selectPolicy: Max
      stabilizationWindowSeconds: 300
  maxReplicas: 4
  metrics:
    - external:
        metric:
          name: aws.sqs.approximate_number_of_messages_visible
          selector:
            matchLabels:
              queuename: digipay_ewallet-service-v2_stg-dev_void-callback-queue
        target:
          type: Value
          value: "10"
      type: External
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ewallet-service-v2-development-void-callback-sqs-worker
status:
  conditions:
    - lastTransitionTime: "2024-09-08T11:42:28Z"
      message: the HPA controller was able to get the target's current scale
      reason: SucceededGetScale
      status: "True"
      type: AbleToScale
    - lastTransitionTime: "2025-05-26T06:12:32Z"
      message: 'the HPA was unable to compute the replica count: unable to get external metric digipay-dev/aws.sqs.approximate_number_of_messages_visible/&LabelSelector{MatchLabels:map[string]string{queuename: digipay_ewallet-service-v2_stg-dev_void-callback-queue,},MatchExpressions:[]LabelSelectorRequirement{},}: unable to fetch metrics from external metrics API: Internal error occurred: DatadogMetric is invalid, err: Global error (all queries) from backend, invalid syntax in query? Check Cluster Agent leader logs for details'
      reason: FailedGetExternalMetric
      status: "False"
      type: ScalingActive
    - lastTransitionTime: "2025-05-21T19:48:40Z"
      message: the desired replica count is less than the minimum replica count
      reason: TooFewReplicas
      status: "True"
      type: ScalingLimited
  currentMetrics:
    - type: ""
  currentReplicas: 2
  desiredReplicas: 2
  lastScaleTime: "2025-04-11T04:23:24Z"
