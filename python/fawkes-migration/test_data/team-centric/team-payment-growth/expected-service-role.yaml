aws:
  xendit:
    global:
      service_role:
        staging:
          - service_name: ewallet-service-v2
            mode: development
            clusters:
              - ''
              - src-stg
            role_name: ewallet-service-v2-development-staging
            service_accounts:
              - name: ewallet-service-v2-development
                namespace: digipay-dev
            role_custom_policies:
              ewallet-service-v2_policy_ap-southeast-1_staging_development:
                Version: '2012-10-17'
                Statement:
                  - Action:
                      - sns:Publish
                    Effect: Allow
                    Resource:
                      - arn:aws:sns:*:************:digipay*
                  - Action:
                      - sqs:DeleteMessage
                      - sqs:GetQueueUrl
                      - sqs:ChangeMessageVisibility
                      - sqs:SendMessageBatch
                      - sqs:UntagQueue
                      - sqs:ReceiveMessage
                      - sqs:SendMessage
                      - sqs:GetQueueAttributes
                      - sqs:ListQueueTags
                      - sqs:TagQueue
                      - sqs:ListDeadLetterSourceQueues
                      - sqs:DeleteMessageBatch
                      - sqs:PurgeQueue
                      - sqs:DeleteQueue
                      - sqs:CreateQueue
                      - sqs:ChangeMessageVisibilityBatch
                      - sqs:SetQueueAttributes
                    Effect: Allow
                    Resource:
                      - arn:aws:sqs:*:************:digipay*
                  - Action:
                      - kms:Encrypt
                      - kms:Decrypt
                      - kms:GenerateDataKey*
                      - kms:DescribeKey
                    Effect: Allow
                    Resource:
                      - arn:aws:kms:ap-southeast-1:************:key/5b71089f-3236-4e54-bfc7-22b3d24b84a4
